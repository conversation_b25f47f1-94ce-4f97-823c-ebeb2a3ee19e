<!-- 侧边菜单组件 -->
<template>
  <div
    class="o-hky-menu"
    :style="{ width: isCollapse ? 0 : 208 + 'px' }"
  >
    <div class="o-hky-menu_wrapper">
      <div class="o-hky-menu_wrapper_app" @click="reload">
        {{ title }}
      </div>
      <div class="o-hky-menu_wrapper_menus webkit-scrollbar">
        <el-menu
          :default-active="defaultActive"
          class="o-hky-menu_wrapper_menus_el"
          text-color="var(--menu-text-color)"
          active-text-color="var(--o-primary-color)"
          @select="handleSelect"
        >
          <template
            v-for="(item, index) in slideMenu"
          >
            <el-submenu
              v-if="item.children != null && item.children.length > 0"
              :key="index"
              :index="item.id"
            >
              <template slot="title">
                <span slot="title">{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="(item2, index2) in item.children"
                :key="index2"
                :index="item2.id"
              >
                <span slot="title">{{ item2.title }}</span>
              </el-menu-item>
            </el-submenu>
            <el-menu-item v-else :key="index" :index="item.id">
              <span slot="title">{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>
    </div>
    <div @click="collapse" class="o-hky-menu_collapse">
      <span
        class="iconfont icon-arrow-left"
        :class="{ 'arrow-right': isCollapse }"
      ></span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { expandMenu, findMenuByPath, getAppCodeCache, getMid, MenuContext, } from "../../util/desktop";
import { Menu } from "../../model";
import { menuTreeToPathMap } from "../common/util";


@Component({
  name: "o-main-menu",
})
export default class MainMenu extends Vue {
  @Prop({ default: ""}) title!: string;

  @Prop()
  private slideMenu!: Menu[];

  private isCollapse: boolean = false;

  private expandMenus: object = {};

  private defaultActive: string = "";

  @Watch("slideMenu")
  watchSlideMenu(){
    this.init()
  }

  init(): void {
    //平铺数据，便于通过key查找数据
    this.expandMenus = expandMenu(this.slideMenu);
    //设置当前选中的菜单
    this.setRoutePath();
  }

  @Watch("$route")
  watchRoute() {
    //设置当前选中的菜单
    this.setRoutePath();
  }

  async created() {
    this.init();
  }

  setRoutePath() {
    const { origin,pathname } = window.location
    const url = origin + pathname
    const slideMenuMap = menuTreeToPathMap(this.slideMenu)

    const id = slideMenuMap[url]?.id
    if(!id) return this.defaultActive = ""
    this.defaultActive = id
  }

  collapse(): void {
    this.isCollapse = !this.isCollapse;
    (this as any).$customEventBus && (this as any).$customEventBus.$emit("menu-collapse",this.isCollapse)
  }

  reload(): void {
    window.location.reload();
  }

  handleSelect(key: string) {
    const indexList = key.split('-');
    let menu = this.slideMenu as any


    indexList.forEach((item)=>{
      if(menu.children?.length) {
        menu = menu.children[Number(item)]
      }else{
        menu = menu[Number(item)]
      }
    })

    this.$emit("openURL",menu)
  }
}
</script>

<style lang="less">
.o-hky-menu {
  position: relative;
  height: 100vh;
  font-family: PingFangSC-Regular;
  transition: all 0.3s;
  background: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(83, 97, 255, 0.05);
  .el-submenu .el-submenu__title{
    // color: #333;
  }
  .el-loading-mask {
    z-index: 10;
  }
  &_wrapper {
    width: 100%;
    overflow: hidden;
    color: #333;
    font-size: 14px;
    font-weight: 700;
    //应用标题样式
    &_app {
      height: 48px;
      line-height: 48px;
      border-bottom: 1px solid #d6d6d6;
      cursor: pointer;
      padding: 0 20px;
    }
    //菜单样式
    &_menus {
      box-sizing: border-box;
      height: calc(100vh - 64px);
      overflow: scroll;
      overflow-x: hidden;
      //自定义菜单样式
      &_el {
        // width: 178px;
        border-right: none !important;
        .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
          transform: rotateZ(-180deg);
        }
        .el-submenu {
          &__title {
            height: 46px;
            line-height: 46px;
            &:focus {
              background: none;
            }
            &:hover {
              background: none;
            }
          }
        }
        .el-submenu__title{
          height: 56px;
          line-height: 56px;
          font-weight: 700 !important;
          &:hover{
            background: var(--menu-hover-color);
          }
        }
        .el-menu-item {
          height: 50px !important;
          line-height: 50px !important;
          font-weight: 400;
          &:focus {
            background: none;
          }
          &.is-active,&:hover {
            height: 40px;
            background: var(--menu-hover-color);
            font-size: 14px;
            line-height: 40px;
            font-weight: 400;
          }
        }
      }
    }
  }
  &_collapse {
    position: absolute;
    top: 50%;
    border-radius: 0 8px 8px 0;
    right: -10px;
    font-size: 40px;
    z-index: 10;
    width: 10px;
    height: 40px;
    cursor: pointer;
    background: #d9dde6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #858c9f;
    &:hover {
      background: var(--o-primary-color);
      color: #ffffff;
    }
    .arrow-right {
      transform: rotateZ(180deg) scale(0.8);
    }
    span {
      transition: all 0.3s;
      font-size: 10px;
      transform: scale(0.8);
    }
  }
}
</style>
