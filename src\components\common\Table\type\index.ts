export type TableHeader = TableHeaderItem[]

export type TableData = Record<string,any>[]

export interface TableHeaderItem {
    // 标题名称
    label:string;
    // 字段
    prop:string;
    // 列最小宽度
    minWidth?:string | number;
    // 表格当前列宽度
    width?:string | number;
    // 当前列是否需要浮动
    fixed?:string | boolean;
    // 插槽名称
    slot?:string;
    // 格式化内容
    formatter?:Function;
    // render
    render?:Function;
    // 按钮是否禁用
    disabled?:boolean;
    // 对齐方式
    align?:"left" |"right" | "center",
    // 表头对齐方式
    headerAlign?:"left" | "right" | "center",
    sortChange?:Function;
    sort?:boolean;
    // 提示内容
    tooltipContent?:string;
    // 是否为搜索表头
    search?:boolean;
    // 移除省略
    showOverflowTooltip?:boolean;
    // 点击回调
    click?:Function;
    // 类型，对应类型 会自动出路相对应的数据
    type?:"TEXT" | "MOBILE" | "AMOUNT" | "DATE" | "NUMBER" | "CHECKBOX" | "DRAG" | "IGNORE" | "INDEX" 
    // 只在type = DATE 时生效，用于格式化日期
    // YYYY-MM-DD hh:ss
    valueFormat?:String
    // 触发的回调 , 目前只有在type = CHECKBOX 会触发回调
    onInput?:Function
}

export interface Sticky {
  // 吸顶距离顶部距离
  top?:number,
  // 吸顶触发 上下偏移量
  offset?:number,
  // 父节点滚动元素
  parent?:string
}

export interface Pagination {
  // 是否浮动
  fixed?:boolean,
}

export interface TableActionButtonItem {
  label:string,
  id:string|number,
  // 点击按钮触发的事件
  click:Function,
  // 是否显示
  ifShow?:boolean|Function
}

export interface tableHeaderActionButtonItemOption {
  label:string;
  click:Function
}

export interface tableHeaderActionButtonItem {
  label?:string;
  // 顶部操作按钮的类型
  type?:"dropdown" | "button" | "custom" | "render";
  component?:any;
  render?:any;
  props?:Record<string,any>
  // 按钮位置，例如 设置左侧 或者右侧
  align?:"left" | "right";
  // 点击触发的回调, 只有在type = "button" 时有效
  click?:Function;
  // 是否显示 ， 不传的话 默认展示
  ifShow?:Boolean|Function,
  // 如果 type=dropdown 此项为下拉数据源
  options?:tableHeaderActionButtonItemOption[],
  // 按钮样式
  style?:Record<string,any>
  // 对应的icon图标
  icon?:string;
}

export type TableContextStore = {
  isDynamicTableHeader:Boolean;
  tableHeaderActionButtons: tableHeaderActionButtonItem[],
  tableHeader: TableHeader,
  tableData: TableData,
  sticky: boolean,
  emptyText: string,
  actionButtons: TableActionButtonItem[],
  requestFn: null | Function,
  loading: boolean,
  showPagination: boolean,
  selection: boolean,
  showOverflowTooltip: boolean,
  emptyHeight: number | string
  pagination: Pagination,
  showHeaderAction: boolean,
  actionFixRight:boolean | string,
  oTableLoading: boolean,
  loadingTimer:any
}

export interface TableRequestParams {
  // 列表查询接口 字段查询条件
  filters?: Record<string,any>,
  // 当前页条数限制
  limit?:number,
  // 起始页数
  start?:number,
  // 排序规则
  sorts?:Record<string,any>[],
  withDisabled?:boolean,
  withDeleted?:boolean,
  withTotal?:boolean,
}