import { Component, Vue, Prop, Ref, Watch, Inject } from "vue-property-decorator";
import { FORM_PROVIDER } from "./form";
@Component({})
export default class extends Vue {
  @Prop() readonly!: boolean | string;
  @Prop() disabled!: boolean;
  
  @Inject(FORM_PROVIDER)
  oForm: any;

  get isReadonly(){
    // 如果不是在oForm 组件下 ， 直接用组件内部的 readonly
    if(!this.oForm) return this.readonly
    // 组件内部 readonly = true , 优先级高于oForm.readonly
    if(this.readonly) return true
    return !!this.oForm.props.readonly
  }

  get isDisabled(){
    // 如果不是在oForm 组件下 ， 直接用组件内部的 disabled
    if(!this.oForm) return this.disabled
    // 组件内部 disabled = true , 优先级高于oForm.disabled
    if(this.disabled) return true
    return !!this.oForm.props.disabled
  }

  get textModel(){
    return this.oForm?.props?.textModel
  }

  @Watch("valueText")
  onWatchValue(value){
    this.$emit("valueTextChange",value)
  }
}