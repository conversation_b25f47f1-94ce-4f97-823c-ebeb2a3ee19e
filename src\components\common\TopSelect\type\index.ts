import { Rule } from "../../form";

export type TFormJson = FormJsonItem[]

// 目前支持的组件类型
type FormJsonItemType = "input" | "select" | "datePicker" | "cascader" | "custom" | "member" | "dept" | "citySelect"| "remoteSearchSelect" | "dateTime" | "selectUser"

interface FormJsonItem {
    // 组件类型
    type: FormJsonItemType;
    // 组件属性
    formItem: FormItem;
    // 是否在外显示 , 筛选组件收起的时候会展示机
    isOut?:boolean;
}

export interface FormItem {
    // 日期组件会用到 详见 element-ui datePick 文档
    type?: string;
    remoteMethod?:Function;
    labelWidth?:string;
    // 字段对应的属性
    prop: string;
    dialogTitle?: string;
    limit?: Number;
    tabField?: "user" | "dept" | "role";
    // 是否去除左右空格
    trim?:boolean;
    // 是否多选
    multiple?:boolean;
    // 值的类型
    valueType?:"int" | "string",
    // 最大长度
    maxlength?:number;
    // 是否显示
    ifShow?:boolean;
    // 弹框标题 member , dept 
    popupTitle?:string;
    // 表单对应的label
    label: string | number;
    // 根据fieldCode 会自动调用枚举接口，获取字段对应的数据源
    // fieldCode?:string;
    // 校验规则
    rules?:Rule[],
    onInput?:Function,
    // 占位符
    placeholder?: string;
    // select label 最多展示多少个字符
    labelMaxLength?: Number;
    value?: string;
    // 字段转换处理
    field?:string;
    // datePick 组件会用到 详见 element-ui datePick 文档
    valueFormat?: string;
    // datePick 组件会用到 详见 element-ui datePick 文档
    format?:string;
    // 默认值，点击重置按钮的时候 会使用该默认值进行 赋值
    defaultValue?: string|number;
    // datePick 组件会用到 详见 element-ui datePick 文档
    rangeSeparator?: string;
    // 动态组件，自定义业务组件的时候 会传入组件的实例
    component?:any,
    // 组件额外属性，传递这个 会透传给 type 对应的 组件prop 上
    props?:Record<string,any>,
    // 日期范围选择组件 前边占位符字段
    startPlaceholder?: string;
    // 日期范围选择组件 后边占位符字段
    endPlaceholder?: string;
    // 下拉选项数据源
    options?: FormItemOption[] | Function;
    // 日期开始时间对应的字段
    startField?:string;
    // 日期结束时间对应的字段
    endField?:string;
}

interface FormItemOption {
    // 下拉选项数据源 对应的label
    label: string;
    // 下拉选项数据源 对应的value
    value: string | number;
}