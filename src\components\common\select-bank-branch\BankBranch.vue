<template>
  <span>{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class BankBranch extends Vue {
  @Prop() value!: string;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let branch = await store.loadBankBranch(this.value);
      if (branch) {
        this.text = branch.name;
        this.$emit("update:change", branch);
      }
    }
  }
}
</script>