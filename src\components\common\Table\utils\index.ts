import _ from 'lodash'

/**
 * @description: 将对象 使用path路径进行解析
 * @param { Object } object 要进行处理的对象数据
 * @param { String | Array } path 解析的路径
 * @param { String } tableNotValue 如果解析路径以后 没有数据 占位返回的标识符
 * @return { String | Object } 解析返回的数据 
 */
export const parsePath2Value = (object, path, tableNotValue = "-") => {
  const result = _.get(object,path)
  if(result === 0) return result
  return result || tableNotValue
}

export const getCache = (key:string)=> {
  const result = JSON.parse(localStorage.getItem(key) || "{}")
  if(result.type === "object") return JSON.parse(result.data)
  return result.data
}

export const setCache = (key:string,value:any)=> {
  const type = typeof value
  localStorage.setItem(key,JSON.stringify({
    type,
    data:type === 'object' ? JSON.stringify(value) : value
  }))
}

export const noop = ()=>{}

/**
 * @description: 金额千分符转换
 * @param { string } s 要格式化的数字
 * @param { number } n 保留几位小数
 * @param { string } p 千分位符号
 * @return { string } - 1,111
 */
export const toAmount = (s, n = 2, p = ",") => {
  n = n > 0 && n <= 20 ? n : 2
  s = `${Number.parseFloat(`${s}`.replace(/[^\d.-]/g, "")).toFixed(n)}`
  const l = s.split(".")[0].split("").reverse()
  const r = s.split(".")[1]
  let t = ""
  for (let index = 0; index < l.length; index++) {
    t += l[index] + ((index + 1) % 3 == 0 && index + 1 != l.length ? p : "")
  }
  return `${t.split("").reverse().join("")}.${r}`
}

export const apiParamsFilterNull = (params,isDeleteNullApiParams)=>{
  if(!isDeleteNullApiParams) return params
  for(let key in params.filters) {
    let value = params.filters[key]
    if(value==="" || value === undefined || value === null) {
      delete params.filters[key]
    }
  }
  return params
}
