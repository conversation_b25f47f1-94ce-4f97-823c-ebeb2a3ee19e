<template>
  <o-dialog
    :title="title"
    :titleRender="titleRender"
    :value="value"
    @input="onClose"
    class="o-json-form-dialog"
    :closeOnClickModal="false"
    :width="dialogWidth"
  >
    <slot name="header" />

    <slot name="main" />
    <o-json-form
      ref="json-form"
      :class="['json-form-size-'+size,'json-form-columns-'+columns]"
      v-bind="$attrs"
      :validateTrigger="validateTrigger"
      v-on="$listeners"
      :hideFootButton="true"
      :save="onSave"
    />
    <slot name="footer" />
    <div slot="footer" class="dialog-footer-button" v-if="showFootButton">
      <div class="flex-1">
        <slot name="footer-button-left" />
      </div>
      <button
        v-if="showCancelButton"
        type="info"
        class="cancel"
        @click="onClose"
      >{{ cancelButtonText }}</button>
      <el-button
        :loading="loading"
        v-if="showSaveButton"
        @click="onSubmit"
        class="confirm"
        type="primary"
      >
        {{ saveButtonText }}
      </el-button>
    </div>
  </o-dialog>
</template>

<script lang="ts">
import { Component, Prop, Ref, Watch, } from "vue-property-decorator";
import JsonForm from "./JsonForm.vue";
import emitter from "../../../mixins/emitter"
import { JsonFormDialogSize } from "./type";

@Component({
  inheritAttrs:false,
  mixins:[emitter]
})
export default class JsonFormDialog extends emitter {
  // 显示隐藏
  @Prop() value!: boolean;
  // 弹框宽度
  @Prop() width!: string | number;
  @Prop() validateTrigger!: string;
  
  // 标题
  @Prop() title!: string;
  // 标题渲染render函数
  @Prop() titleRender!: Function;
  // 保存
  @Prop() save!: Function;
  // 是否关闭弹框重置表单
  @Prop() isCloseReset!:Boolean;
  // 是否展示底部 按钮组
  @Prop({ default:true }) showFootButton!:Boolean;
  // 师傅展示底部 取消按钮
  @Prop({ default:true }) showCancelButton!:Boolean;
  // 师傅展示底部 确定按钮
  @Prop({ default:true }) showSaveButton!: boolean;
  // 确定按钮文字
  @Prop({ default:"确定" }) saveButtonText!:string
  // 取消按钮文字
  @Prop({ default:"取消" }) cancelButtonText!:string
  // 弹框大小
  @Prop({ default:"big" }) size!:JsonFormDialogSize
  // 列
  @Prop({ default:1 }) readonly columns!: number;
  
  @Ref("json-form") 
  JsonForm: JsonForm | undefined;

  loading = false;

  protected onClose() {
    this.$emit("input", false);
  }
  
  @Watch("value")
  onWatchValue(){
    this.closeReset()
  }

  // 关闭时候 重置数据
  closeReset(){
    if(this.isCloseReset && !this.value) {
      setTimeout(() => {
        this.broadcast("o-form-scene","handleResetButtonClick")
      }, 300);
    }
  }

  // 弹框宽度
  get dialogWidth (){
    const map = {
      big:"840px",
      medium:"560px"
    }
    // 如果用户主动设置宽度 优先使用用户设置的宽度
    if(this.width) return this.width
    // 否则使用 size 对应的宽度
    return map[this.size]
  }

  // 保存按钮
  async onSave(formData){
    if(!this.value) return
    this.loading = true 
    try {
      await this.save(formData)
    }finally{
      this.loading = false
    }
  }

  // 动态设置下拉选项 数据源
  setOptions(data) {
    return this.JsonForm?.setOptions(data)
  }

  // 获取选项的数据源
  getOptions(prop?:string){
    return this.JsonForm?.getOptions(prop)
  }

  // 设置是否显示隐藏字段
  setIsShowField(map:Record<string,boolean>){
    return this.JsonForm?.setIsShowField(map)
  }

  // 设置分组显示隐藏
  setIsShowGroup(map:Record<string|number,boolean>){
    return this.JsonForm?.setIsShowGroup(map)
  }

  // 设置字段禁用状态
  setDisabled(map){
    return this.JsonForm?.setDisabled(map)
  }

  getOptionsItem(field,value){
    return this.JsonForm?.getOptionsItem(field,value)
  }

  // 重置
  resetForm(){
    return this.JsonForm?.resetForm()
  }

  // 设置分组或者字段 显示隐藏
  setIfShow(map:Record<string,any>){
    return this.JsonForm?.setIfShow(map)
  }

  // 获取表单数据
  getFormData(noValidate?:boolean) {
    return this.JsonForm?.getFormData(noValidate)
  }

  // 获取表单数据
  setFormJson(formJson) {
    return this.JsonForm?.setFormJson(formJson)
  }

  // 填充表单数据
  setFormValue(formData) {
    return this.JsonForm?.setFormValue(formData)
  }
  
  protected onSubmit() {
    if(!this.value) return 
    this.broadcast("o-form-scene","onSubmit")
  }
}
</script>

<style lang="less">
@import "./styles/o-json-form-dialog-global.less";
</style>