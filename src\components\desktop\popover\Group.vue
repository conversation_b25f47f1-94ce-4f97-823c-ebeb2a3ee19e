<!-- 分组弹出层 -->
<template>
  <div class="o-nav_popover_wrapper">
    <template v-for="(item, i) in list">
      <!-- 分组start   判断是否有组，或者存在组的情况（可能是有分组未命名的数据） -->
      <div
        :key="i"
        class="o-nav_popover_wrapper_group"
        v-if="item.groupName || item.children.length > 0"
      >
        <div class="o-nav_popover_wrapper_group_title">
          {{ parseType === "GROUP" ? item.groupName : item.name }}
        </div>
        <!-- 组列表 分为一行三列 -->
        <div class="o-nav_popover_wrapper_group_list">
          <div
            class="o-nav_popover_wrapper_group_list_column"
            v-for="(seletor, i) in 3"
            :key="i"
          >
            <div
              class="o-nav_popover_wrapper_group_list_item nav_item_hover"
              @click="openAppEvent(group)"
              v-for="(group, j) in item.children"
              v-if="(j + 3 - i) % 3 === 0"
              :key="j"
            >
              <div class="o-nav_popover_wrapper_group_list_item_icon">
                <img :src="group.icon" />
              </div>
              <div class="o-nav_popover_wrapper_group_list_item_name">
                {{ group.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分组end -->
      <div
        class="o-nav_popover_wrapper_item nav_item_hover"
        @click="openAppEvent(item)"
        v-else
      >
        <div>
          <img :src="item.icon" />
        </div>
        <div>{{ item.name }}</div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { Navigation } from "../../../model/type";
@Component({
  name: "o-group",
})
export default class Group extends Vue {
  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  /**
   * 导航数据
   */
  @Prop()
  private list?: Navigation;

  /**
   * @description 解析类型   GROUP:按照正常groupName组来取值   NO:按照正常name来取值
   */
  @Prop({
    default: "GROUP",
  })
  private parseType?: string;

  /**
   * @description 点击触发事件
   * @param item
   */
  private openAppEvent(item): void {
    this.$emit("on-select", item);
  }
}
</script>