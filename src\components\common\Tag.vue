<template>
  <el-tag class="o-tag" :class="objectClass" v-bind="$props" v-on="$listeners">
    <slot></slot>
  </el-tag>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class Tag extends Vue {
  @Prop() type!: string;
  @Prop() closable: any;
  @Prop() size!: string;

  get objectClass() {
    let type = this.type ?? "info";
    let o = {} as any;
    o["o-tag-" + type] = true;
    return o;
  }
}
</script>
<style scoped lang="less">
.o-tag {
  height: 20px;
  background: #F7FAFD;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  padding:0 8px !important;
  font-size: 12px;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  border-radius: 12px;
}
</style>