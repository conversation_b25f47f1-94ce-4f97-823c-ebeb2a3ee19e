<template>
  <div class="container">
    <!-- 文本查看模式 -->
    <div v-if="textModel">
      <div v-if="checkBoxValue">
        {{checkBoxLabel}}
      </div>
      <div
        v-else
        class="flex"
      >
        <span class="mr-10">{{value[0]}}-{{value[1]}}</span>
        <div class="unit" v-if="value[0] && value[1]">
          <div v-if="unitName">
            {{unitName}}
          </div>
          <o-select-field
            v-else-if="options.length"
            :value="unitSelectValue"
            @input="onInput3"
            :options="newOptions"
          />
        </div>
      </div>
    </div>
    <div v-else>
      <div class="flex mb-10">
        <o-input
          ref="input1"
          :value="value ? value[0] : null"
          :rules="[{ type: 'int', message: '请输入正确的数据' }]"
          :placeholder="startPlaceholder"
          @input="onInput1"
          :maxlength="maxlength"
          :disabled="checkBoxValue"
          validateTrigger="realtime"
          class="flex-1"
        />
        <span class="my-10 flex items-center">-</span>
        <o-input
          ref="input2"
          :value="value ? value[1] : null"
          :rules="[{ type: 'int', message: '请输入正确的数据' }]"
          :disabled="checkBoxValue"
          @input="onInput2"
          :maxlength="maxlength"
          :placeholder="endPlaceholder"
          validateTrigger="realtime"
          class="flex-1 mr-10"
        />
        <span
          v-if="unitName"
          class="unitName  flex items-center"
        > {{ unitName }} </span>
        <o-select-field
          v-if="!unitName && options.length"
          :value="unitSelectValue"
          @input="onInput3"
          :options="newOptions"
          style="width:100px"
        />
      </div>
      <el-checkbox
        v-if="checkBoxLabel"
        :value="checkBoxValue"
        :label="checkBoxLabel"
        @input="onCheckBoxInput"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue } from "vue-property-decorator";

import { FieldContext, registerField, validateRules } from "../../../../components/common/form";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";
import _ from "lodash";

const placeholder = "请输入";

@Component({
  mixins: [formFieldMixins],
})
export default class  JInputRange extends Vue {
  @Prop() value!: string[];
  @Prop() readonly!: any;
  @Prop() readonly formItem!: FormItem;
  @Prop({ default: placeholder }) placeholder!: string;
  @Prop({ default: () => [] }) options!: any;
  @Prop() rules!: any[];
  @Prop() trim!: boolean;
  @Prop({ default:99999 }) maxlength!: boolean;

  get checkBoxLabel() {
    return this.formItem?.checkBoxLabel;
  }

  get checkBoxValue() {
    return this.value ? this.value[3] : null;
  }

  get startPlaceholder() {
    if (_.isArray(this.placeholder)) return this.placeholder[0] ?? placeholder;
    return this.placeholder;
  }

  get endPlaceholder() {
    if (_.isArray(this.placeholder)) return this.placeholder[1] ?? placeholder;
    return this.placeholder;
  }

  get newOptions() {
    return this.options.map((item) => {
      if (item.label) item.text = item.label;
      return item;
    });
  }

  @Ref("input1")
  protected input1;

  @Ref("input2")
  protected input2;

  field!: FieldContext;
  error = false;

  get unitSelectValue() {
    if (Array.isArray(this.value) && !_.isNil(this.value[2])) return this.value[2];
    return "";
  }

  created() {
    this.initUnitSelectValue();
  }

  initUnitSelectValue() {
    if (this.options.length && !this.value[2])
      this.onInput3(this.options[0].value);
  }

  get unitName() {
    // 只有一个选项 才显示单位 div ， 否则会显示 下拉选项
    if (this.options.length === 1) return this.options[0].label;
    return false;
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let required = this.field.requiredRule;

    if (this.value[3]) return true;

    if (required && (!this.input1.value || !this.input2.value)) {
      return required.message;
    }

    return this.validateCore(this.value, false);

  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }


  async validateCore(v: any, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }

  onFieldReset() {
    this.$emit("input", ["", "", this.value[2]]);
  }

  protected onInput1(v) {
    let value = [...(this.value ?? [])];
    if(this.trim) v = String(v).trim()

    value[0] = v;
    this.$emit("input", value);
  }

  protected onInput2(v) {
    let value = [...(this.value ?? [])];
    if(this.trim) v = String(v).trim()
    value[1] = v;
    this.$emit("input", value);
  }

  protected onInput3(v) {
    let value = [...(this.value ?? [])];
    value[2] = v;
    this.$emit("input", value);
  }

  onCheckBoxInput(check) {
    let value = [this.value[0], this.value[1], this.value[2], check];
    this.$emit("input", value);
  }
}
</script>

<style scoped>
.unitName {
  padding-left: 10px;
}
.flex {
  display: flex;
}
.mr-10 {
  margin-right: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.my-10{
  margin:0 10px;
}
.flex-1{
  flex: 1;
}
.items-center{
  align-items: center;
}
</style>