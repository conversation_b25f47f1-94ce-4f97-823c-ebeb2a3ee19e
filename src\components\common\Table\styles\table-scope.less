@main-primary:#5374F6;

.flex {
  display: flex;
}

.m-r-8 {
  margin-right: 8px;
}

.pointer {
  cursor: pointer;
}

.flex-1 {
  flex: 1;
}

.opacity {
  opacity: 0;
}

.o-table {
  position: relative;

  &.pagination-fixed {
    padding-bottom: 60px;
  }

  .table-header-title {
    &.title-right {
      justify-content: end;
    }
    &.center{
      justify-content: center;
    }
    &.right {
      justify-content: flex-end;
    }
    &.ASCENDING {
      .sort .oi-table-sort:first-child {
        color: var(--o-primary-color);
      }
    }

    &.DESCENDING {
      .sort .oi-table-sort:last-child {
        color: var(--o-primary-color);
      }
    }
  }

  .sort {
    height: 14px;
    width: 14px;
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    margin-left: 4px;
    position: relative;
    top: -4px;
    left: 3px;

    .oi-table-sort {
      height: 10px;
      width: 10px;
      font-size: 12px;
      transform: rotate(180deg) scale(0.4) !important;
      transform-origin: 5px 10px;
    }

    .oi-table-sort:first-child {
      font-size: 12px;
      transform: rotate(0) scale(0.4) !important;
      transform-origin: -1px 13px;
    }
  }

  ::v-deep .el-table__empty-block {
    width: 100% !important;

    .el-table__empty-text {
      height: 14px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 14px;
      color: #a8acba;
      letter-spacing: 0;
      text-align: center;
      line-height: 14px;
    }
  }

  .header {
    height: 60px;
    align-items: center;
    background: #fff;
    position: initial;

    .select-num {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #777c94;
      margin-right: 12px;
    }

    &-set {
      color: #777c94;
      height: 36px;
      line-height: 36px;

      i {
        font-size: 14px;
      }
    }
  }

  .action-header-title {
    padding-left: 2px;
  }

  .oi-help {
    font-size: 14px;
    color: #a8acba;
    cursor: pointer;
    margin-left: 4px;
  }

  ::v-deep .el-table__body-wrapper,
  ::v-deep .el-table__fixed-body-wrapper {
    tr.hover-row {

      &,
      &.el-table__row--striped {

        &,
        &.current-row {
          >td.el-table__cell {
            background: #f7fafd;
          }
        }
      }
    }
  }

  ::v-deep .el-table {

    td.el-table__cell,
    th.el-table__cell.is-leaf {
      border-color: #eef0f4;
    }

    .el-button--text {
      color: var(--o-primary-color);
    }

    .el-table-column--selection .cell {
      display: flex;
      justify-content: center;

      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: var(--o-primary-color);
        border-color: var(--o-primary-color);
      }

      .el-checkbox__input .el-checkbox__inner {
        border-radius: 4px;
      }

      .el-checkbox__inner:hover {
        border-color: var(--o-primary-color);
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: flex-end;
    background: #fff;
    height: 58px;
    transition: all 0.3s;
    justify-content: flex-start;
    align-items: flex-end;
    display: flex;
    flex-direction: column;

    &.fixed {
      position: fixed;
      z-index: 99;
      bottom: 0;
      right: 0;
      left: 0;
    }

    ::v-deep {

      .el-pagination__total {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #777c94;
        letter-spacing: 0;
        text-align: right;
        margin: 0;
      }

      .el-pagination__sizes {
        margin-right: 0;

        .el-input--mini {
          margin: 0;
          margin-left: 8px;

          .el-input__inner {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #46485a;
            letter-spacing: 0;
            padding-left: 8px;
            border-color: #cbced8;
            text-align: left;

            &:hover {
              border-color: var(--o-primary-color);
            }
          }

          .el-select__caret {
            position: relative;
            left: -4px;
          }
        }
      }
    }
  }

  .index-table {
    width: 100%;
    padding-top: 20px;
  }

  .label {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #070f29;
    line-height: 12px;

    &-center{
      text-align: center;
    }

    &-right{
      text-align: right;
      padding-right: 2px;
    }

    &.click {
      cursor: pointer;
      color: @main-primary;
    }
  }

  .action-button-wrap {
    display: flex;
    padding-right: 0;
    position: relative;
    left: -6px;

    // &>div:first-child{
    //   position: relative;
    //   left: -10px;
    // }

    &.isMore {
      padding-right: 0;
    }

    &__btn {
      padding: 0 8px;
      border: 0;
    }

    .more {
      padding: 0 10px;
      width: 44px;
    }
  }
}