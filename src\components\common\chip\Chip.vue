<template>
  <box
    class="o-chip"
    :border-radius="16"
    :outlined="outlined"
    :type="type"
    hover="bg"
    @click="onClick"
  >
    <div style="flex: 1; min-width: 0; text-align: center">
      <slot />
    </div>
    <i
      v-if="closable == true || closable === ''"
      class="el-icon-close o-chip-close"
      @click.stop="$emit('close')"
    />
  </box>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";
import Box from "../box";

@Component({
  components: {
    Box,
  },
})
export default class  Chip extends Vue {
  @Prop() type!: string;
  @Prop() closable!: any;
  @Prop() outlined!: any;

  protected onClick() {
    this.$emit('click')
  }
}
</script>
<style lang='less' scoped>
</style>