import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';
import DeptChip from './DeptChip.vue';

<Meta title="业务组件/o-dept-chip" component={DeptChip} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'primary', 'success', 'warning', 'danger'],
  },
  outlined: {
    type: 'boolean'
  },
  closable: {
    type: 'boolean'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { DeptChip },
  template: '<o-dept-chip v-bind="$props">名称</o-dept-chip>',
});


按照部门ID, 回显部门的信息

<Canvas>
  <Story 
    name="默认"
    args={{
      value: 110200
    }}>
    {Template.bind({})}
  </Story>
</Canvas>