<template>
  <el-select
    :value="value"
    :multiple="multiple"
    style="width:100%"
    filterable
    remote
    clearable
    @input="onInput"
    reserve-keyword
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
    @visible-change="visibleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.key || item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { FormItem } from "../type";
import { Select } from "element-ui";

const select = {
  extends: Select,
};

@Component({
  inheritAttrs: false,
  components: {
    elSelect: select,
  },
})
export default class JRemoteSearchSelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() readonly multiple!: boolean;
  @Prop() rules!: any[];
  @Prop({ default: "请输入" }) placeholder!: string;

  field!: FieldContext;

  loading = false;
  options = [];

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  async remoteMethod(query) {
    if (query !== "") {
      this.loading = true;
      const fn = this.formItem.remoteMethod;
      try {
        if (!fn) return;
        this.options = await fn(query);
      } finally {
        this.loading = false;
      }
    } else {
      this.options = [];
    }
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  @Emit("visibleChange")
  visibleChange(value){
    document.dispatchEvent(new Event('click'))
    return value
  }

  onFieldReset() {
    this.onInput(this.formItem.defaultValue || "")
  }
}
</script>