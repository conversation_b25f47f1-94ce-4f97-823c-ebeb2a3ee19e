<template>
  <pop :value="value" class="o-dialog o-elevation-3" :closeOnClickModal="closeOnClickModal" @input="close(false)" :style="popStyle">
    <div v-if="showTitle" class="o-dialog-header">
      
      <!-- 支持render方法 -->
      <render-title v-if="titleRender"  :renderFn="titleRender" />
      <div v-else class="title">{{ title }}</div>

      <i class="close icon olading-iconfont oi-icon_close" @click="close(false)"></i>
    </div>
    <div class="o-dialog-container webkit-scrollbar-line">
      <slot></slot>
    </div>
    <div v-if="showFooter()" class="o-dialog-footer">
      <slot name="footer">
        <div v-if="confirmButton || cancelButton">
          <o-button 
            v-if="cancelButton" 
            class="cancel-button button" 
            :loading="cancelLoading" 
            type="info" 
            @click="close(false)">
            {{ cancelButton }}
          </o-button>
          <o-button 
            :loading="confirmLoading" 
            class="confirm-button button" 
            v-if="confirmButton" 
            type="primary" 
            @click="close(true)"
          >
            {{confirmButton}}
          </o-button>
        </div>
      </slot>
    </div>
  </pop>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import renderTitle from "./renderTitle.vue"
import { parse2px } from "../util";
import OButton from "../button";
import Pop from "../Pop.vue";
import _ from "lodash"

@Component({
  components: {
    Pop,
    OButton,
    renderTitle,
  },
})
export default class  Dialog extends Vue {
  @Prop() titleRender!: Function;
  @Prop() title!: string;
  @Prop() value!: boolean;
  @Prop() width!: string;
  @Prop() confirmButton!: string;
  @Prop() cancelButton!: string;
  @Prop() closeOnClickModal!: boolean;

  confirmLoading = false
  cancelLoading = false

  get popStyle(){
    const result = {}
    if(this.width) result["width"] = parse2px(this.width)
    return result
  }

  // 是否展示title 
  get showTitle(){
    return this.titleRender || this.title
  }

  protected showFooter() {
    if (this.$slots["footer"]) {
      return true;
    }
    if (this.confirmButton) {
      return true;
    }
  }

  protected async close(done) {
    if (done) {
      if(!this.value) return 
      if(_.isFunction(this.$listeners.confirm)) {
        this.confirmLoading = true
        try {
          await this.$listeners.confirm()
        }finally{
          this.confirmLoading = false
        }
      }
    } else {
      this.$emit("input");
    }
  }
}
</script>
<style lang="less">
.o-dialog {
  display: flex;
  flex-direction: column;
  max-height: 90%;
  border-radius: 8px;
  .title{
    width: 100%;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
    padding-right: 30px;
  }

  &-header {
    min-height: 48px;
    line-height: 22px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    border-bottom: 0.5px solid #eaeaea;
    position: relative;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    box-sizing: border-box;
    color: #24262A;

    .close {
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      position: absolute;
      font-size: 16px;
      font-weight: bold;
      padding-right: 24px;
      right: 0;
      top: 0;
      cursor: pointer;
      color: #bcbcbc;
    }
  }

  &-container {
    padding: 20px;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    &::-webkit-scrollbar{
      width: 6px;
    }
    &::-webkit-scrollbar-thumb{
      background: #fff;
    }
    &:hover{
       &::-webkit-scrollbar-thumb{
        background: #eee;
      }
    }
  }

  &-footer {
    display: flex;
    padding: 16px 24px;
    // border-top: 0.5px solid #eaeaea;
    flex-direction: row-reverse;
    .button{
      border-radius: 8px !important;
      width: 96px;
      height: 36px !important;
      line-height: 36px !important;
    }
    .cancel-button{
      border: 1px solid #cbced8 !important;
      color: #777c94 !important;
      &:hover{
        &::after{
          content: none !important;
        }
      }
    }
    .confirm-button{
      margin-right: 0 !important;
    }
  }
}
</style>