<template>
  <van-list :finished="finished" finished-text="没有更多了" @load="onLoad">
    <van-cell
      v-for="item in records"
      :key="item.id"
      :clickable="true"
      @click="$emit('itemClick', item, records)"
    >
      <slot :item="item"></slot>
    </van-cell>
  </van-list>
</template>
<script lang="ts">
/** 移动端通用List组件 */

import { Component, Prop, Vue, Watch } from "vue-property-decorator";

/** 分页数据加载器 */
type PagedLoader = (start: number, limit: number) => Promise<any[]>;

@Component
export default class ListView extends Vue {
  @Prop() loader!: PagedLoader;
  pageSize: number = 30;
  key: string = "id";

  records: any[] = [];
  loading: boolean = false;
  finished: boolean = false;

  @Watch("loader")
  onLoaderChanged() {
    this.refresh();
  }

  mounted() {
    this.refresh();
  }

  async refresh() {
    this.records.splice(0, this.records.length);
    // await this.loadNext();
  }

  async loadNext() {
    if (!this.loader) {
      return;
    }
    if (this.loading) {
      return
    }
    this.loading = true;
    try {
      let records = await this.loader(this.records.length, this.pageSize);

      // 按照ID去除重复数据
      records.forEach((r) => {
        let i = this.records.findIndex((o) => o[this.key] == r[this.key]);
        if (i >= 0) {
          Vue.set(this.records, i, r);
        } else {
          this.records.push(r);
        }
      });

      if (records.length < this.pageSize) {
        this.finished = true;
      }
    } finally {
      this.loading = false;
    }
  }

  onLoad() {
    this.loadNext();
  }
}
</script>