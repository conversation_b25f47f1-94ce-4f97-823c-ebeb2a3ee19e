<template>
  <span>{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class  Bank extends Vue {
  @Prop() value!: string;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let bank = await store.loadBank(this.value);
      if (bank) {
        this.text = bank.name;
        this.$emit("update:change", bank);
      }
    }
  }
}
</script>