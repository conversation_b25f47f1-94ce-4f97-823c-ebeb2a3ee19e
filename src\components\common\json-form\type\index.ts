import { Field } from "../../../../components/common";
import { Rule, ValidateTrigger } from "../../form";
import { DynamicComponentName } from "../JsonFormRender";


export type TFormJson = FormJsonItem[]

// 目前支持的组件类型
type FormJsonItemType = keyof typeof DynamicComponentName | "custom"
interface FormJsonItemChildren {
    // 组件类型
    type: FormJsonItemType;
    // 组件属性
    item: FormItem;
    slot?:Record<string,any>
    //  field 组件prop
    field?: any
}

export interface FormJsonItem {
    slot?:Record<string,any>
    // 分组名称
    groupName?: string;
    ifShow?: boolean | Function
    // 分组ID
    groupId?: number | string;
    // 子集
    children?: FormJsonItemChildren[]
    // 分组render渲染方法
    groupNameRender?:Function;
    // 组件类型
    type?: FormJsonItemType;
    // 组件属性
    item?: FormItem;
    // 一行有几列 权重大于 全局 columns
    columns?: number;
    //  field 组件prop
    field?: any
}

export interface FormItem {
    ifShow?: boolean | Function
    // 日期组件会用到 详见 element-ui datePick 文档
    type?: string;
    remoteMethod?:Function;
    labelWidth?: string;
    // inputRang 时会用到
    checkBoxLabel?:string;
    // 密码组件会用到
    showPassword?:boolean;
    // 移动端 select popup 会用到 
    title?:string;
    // 校验规则
    validateTrigger?:ValidateTrigger
    // 字段对应的属性
    prop: string;
    render?:Function;
    // 是否去除前后宫格
    trim?:boolean;
    // 表单对应的label
    label: string | number;
    // 只有在 type = member | dept 会用到
    popupTitle?: string;
    limit?: Number;
    // 校验规则
    rules?: Rule[],
    tabField?: "user" | "dept" | "role";
    // select-user 弹框标题
    dialogTitle?:string;
    
    // 占位符
    placeholder?: string|string[];
    // 是否支持选择任意一级,用于cascader组件
    checkStrictly?:Boolean
    maxSelectNum?:number;
    // 组件改变触发事件
    onInput?:Function;
     // 组件改变触发事件
    onChange?:Function;
    // 是否多选
    multiple?: boolean;
    // select label 最多展示多少个字符
    labelMaxLength?: Number;
    
    multipleLimit?: Number;
    disabled?: boolean;
    modalAppendToBody?: boolean;
    // 是否展示最大数字限制 type = input , textarea 时有效
    showWordLimit?:boolean;
    // 最大文字限制
    maxlength?:number,
    // 值
    value?: any;
    // 字段转换处理
    field?: string;
    // datePick 组件会用到 详见 element-ui datePick 文档
    valueFormat?: string;
    // datePick 组件会用到 详见 element-ui datePick 文档
    format?: string;
    // 默认值，点击重置按钮的时候 会使用该默认值进行 赋值
    defaultValue?: any;
    // datePick 组件会用到 详见 element-ui datePick 文档
    rangeSeparator?: string;
    startPlaceholder?: string;
    endPlaceholder?: string;
    inputWidth?: string;
    // 动态组件，自定义业务组件的时候 会传入组件的实例
    component?: any,
    // 组件额外属性，传递这个 会透传给 type 对应的 组件prop 上
    props?: Record<string, any>,
    // 下拉选项数据源
    options?: FormItemOption[] | Function;
    // 日期开始时间对应的字段
    startField?: string;
    // 日期结束时间对应的字段
    endField?: string;
    // 值的类型，一般用于input
    valueType?:"int" | "decimals_1" | "decimals_2"
}

interface FormItemOption {
    // 下拉选项数据源 对应的label
    label?: string;
    // 下拉选项数据源 对应的value
    value?: string | number;
    [propName: string]: any,
}

export type JsonFormDialogSize = "big" | "medium"