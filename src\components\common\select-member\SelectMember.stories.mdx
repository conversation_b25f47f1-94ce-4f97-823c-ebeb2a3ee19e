import { <PERSON><PERSON>, <PERSON>a, Story, Source, ArgsTable } from '@storybook/addon-docs';

import SelectMemberField from './SelectMemberField.vue';

<Meta title="业务组件/o-select-member-field" component={SelectMemberField} argTypes={{
  value: {
    type: 'object',
    description: '用户的ID,例如"133"; 如果maxSelectNum > 1,则是数组例如: ["133", "577"]'
  },
  maxSelectNum: {
    description: '最多可以选择的数量'
  },
  readonly: {
    type: 'boolean',
    description: '是否只读不可修改'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SelectMemberField },
  data: () => ({ dept: "" }),
  template: '<o-select-member-field v-bind="$props" v-model="dept"></o-select-member-field>',
});


# 选择企业人员

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

<ArgsTable story="基本" />
