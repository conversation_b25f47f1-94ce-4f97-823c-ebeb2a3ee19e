import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import {Comp} from './CheckGroup';

<Meta title="基础组件/o-check-group" component={Comp} argTypes={{
  horizontal: {
    type: 'boolean'
  },
  multiple: {
    type: 'boolean'
  },
  mandatory: {
    type: 'boolean'
  },
}} />

export const template = `
<o-check-group v-bind="$props" v-model="check">
  <o-check-item v-for="n in 3" :key="n" :value="n">第{{ n }}个</o-check-item>
</o-check-group>`
export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  data: () => ({ check:[] }),
  template: template,
});


纵向布局

<Canvas>
  <Story 
    name="纵向布局"
    args={{ 
    }}>
    {Template.bind({})}
   </Story>
</Canvas>



横向布局

<Canvas>
  <Story 
    name="横向布局"
    args={{
      horizontal: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

至少需要选中一个

<Canvas>
  <Story 
    name="mandatory"
    args={{
      mandatory: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


多选

<Canvas>
  <Story 
    name="multiple"
    args={{
      mandatory: true,
      multiple: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>