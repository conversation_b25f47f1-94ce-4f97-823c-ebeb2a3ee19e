.o-json-form-dialog{
  .dialog-footer-button{
    display: flex;
    width: 100%;
    align-items: center;
    .flex-1{
      flex:1;
    }
  }
  .dialog-footer-button{
    .cancel{
      width: 92px;
      height: 36px;
      display: flex;
      align-items: center;
      border: 1px solid #CBCED8;
      border-radius: 8px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      cursor: pointer;
      font-size: 14px;
      color: #777C94;
      letter-spacing: 0;
      text-align: center;
      text-align: center;
      background: #fff;
      justify-content: center;
      margin-right: 8px;
    }
    .confirm{
      height: 36px;
      line-height: 36px;
      padding: 0;
      border-radius: 8px;
      width: 92px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  // 中等弹框 ， 设置表单居中尺寸
  .json-form-size-medium{
    width: 370px;
    margin:0 auto;
  }
  // 弹框两列的布局情况
  .json-form-columns-2{
    padding: 0 20px;
    .o-form-scene{
      display: flex;
      flex-wrap: wrap;
    }
    .o-field{
      width: 50%;
      // margin:0;
      margin-left: 0;
      margin-right: 0;
      &:nth-child(2n){
        padding-left: 50px;
      }
      &:nth-child(2n+1){
        padding-right: 50px;
      }
      &.json-form-field-textarea{
        width: 100%;
        padding-left: 0;
        padding-right: 0;
      }
    }
  }
}