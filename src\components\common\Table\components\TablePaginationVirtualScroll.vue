<template>
  <div
    ref="scroll"
    class="table-pagination-scroll-wrap"
  >
    <div
      class="scroll-x"
      :style="{ width:parse2px(width) }"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from "vue-property-decorator";
import { addScrollListener, parse2px, removeScrollListener } from "../../util";

@Component({})
export default class TablePaginationVirtualScroll extends Vue {
  @Prop({ default: "" }) readonly width!: number | string;

  @Ref() readonly scroll!: HTMLElement;

  allWidth: number = 0;

  protected parse2px = parse2px

  mounted() {
    addScrollListener(this.scroll,this.scrollX)
  }

  beforeDestroy() {
    removeScrollListener(this.scroll,this.scrollX)
  }

  protected scrollX(e) {
    let left = e.target.scrollLeft;
    this.$emit("scrollChange", left);
  }

  setScrollLeft (left = 0){
    this.scroll.scrollLeft = left
  }
}
</script>