<template>
  <div class="form" :class="actualClass">
    <slot></slot>
  </div>
</template>

<script lang='ts'>
import {
  Component,
  Inject,
  Prop,
  Provide,
  Vue,
  Watch,
} from "vue-property-decorator";
import { CONTEXT_PROVIDER, defaultContext } from "./context";
import { FormContext, FORM_PROVIDER } from "./form";

@Component({})
export default class Form extends Vue {
  @Prop({ default: "left" }) labelPosition!: string;
  @Prop({ default: "90px" }) labelWidth!: string;
  @Prop({ default: "normal" }) layout!: "normal" | "compact";
  @Prop() readonly!: boolean;
  @Prop() disabled!: boolean;
  @Prop() textModel!: boolean;

  @Provide(FORM_PROVIDER)
  context = new FormContext();

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context2: any;

  get mobile() {
    return this.context2.isMobile;
  }

  get actualClass() {
    return this.mobile ? ["o-mobile"] : [];
  }

  @Watch("$props", {
    deep: true,
  })
  onPropsChange() {
    this.refresh();
  }

  beforeMount() {
    this.context.component = this;
  }

  mounted() {
    this.refresh();
  }

  refresh() {
    this.context.props = this.$props;
  }

  clearFieldsErrorMessage(){
    return this.context.clearFieldsErrorMessage();
  }

  resetFields(){
    return this.context.resetFields();
  }

  getValues() {
    return this.context.values;
  }

  validate() {
    return this.context.validate();
  }

  scrollToError() {
    this.context.scrollToError();
  }
}
</script>
<style lang='less' scoped>
</style>