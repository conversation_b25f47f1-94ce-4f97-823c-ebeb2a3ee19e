<template>
  <div class="o-avatar" :style="photoStyle">
    {{ name ? name.substring(name.length - 2) : "" }}
  </div>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class Avatar extends Vue {
  @Prop({ default: "" }) name!: String;
  @Prop({ default: 39 }) size!: Number | BigInt | any;
  @Prop({ default: 12 }) fontSize!: Number | BigInt | any;
  @Prop({ default: "#4f75ff" }) bgColor!: string;

  get photoStyle() {
    return `
      width:${this.size}px;
      height:${this.size}px;
      fontSize:${this.fontSize}px;
      background-color:${this.bgColor};
    `;
  }
}
</script>
<style lang='less' scoped>
.o-avatar {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: #fff;
}
</style>