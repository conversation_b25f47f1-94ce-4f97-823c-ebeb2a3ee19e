<template>
  <div class="upload-invoice" v-loading="uploading">
    <upload-invoice-form
      ref="upload-invoice-form"
      @confirm="invoiceFormConfirm"
    />
    <popup
      v-model="uploadInvoiceWayPopup"
      title="上传发票"
      :showMobileHeader="true"
      mobileHeight="60%"
    >
      <div class="upload-invoice-popup">
        <el-upload
          :multiple="multiple"
          class="upload"
          style="width: 100%"
          :action="uploadUrl"
          :on-success="onSuccess"
          ref="upload"
          :accept="accept"
          :on-error="onError"
          :on-exceed="onExceed"
          :on-progress="onProgress"
          :before-upload="beforeUpload"
          :limit="limit"
          :show-file-list="false"
          :httpRequest="httpRequest"
          :headers="{
            Authorization: 'Bearer ' + $app.token,
          }"
        >
          <div>
            <i class="olading-iconfont oi-icon_photograph" />
            智能拍票
          </div>
        </el-upload>

        <div @click="handleManuallyBtnClick">
          <i class="olading-iconfont oi-icon_record" />
          手录发票
        </div>
      </div>
    </popup>

    <div class="footer-action-buttons">
      <div class="flex top">
        <checkbox
          :value="isSelectAll(batch)"
          @input="onSelectAll"
          v-if="showAppSelect(batch)"
        >
          <span class="select-text">全部</span>
        </checkbox>
        <div class="flex-1"></div>
        <span class="add" @click="handleAddInvoiceBtnClick">+ 继续添加</span>
      </div>
      <div class="bottom">
        <OButton size="small" @click="$emit('close')">返回</OButton>
        <OButton size="small" type="primary" @click="onOk">绑定此费用</OButton>
      </div>
    </div>

    <div class="container" ref="container" :class="{ desktop: !mobile }">
      <div class="card-box">
        <div v-for="(batch, i) in batch" :key="i" class="card-box-item">
          <div class="header">
            <div class="select">
              <checkbox
                label="局部全部选择"
                v-show="showAreaSelect(batch.invoice)"
                :value="isBatchSelectAll(batch.invoice)"
                @input="onBatchSelectAll($event, batch.invoice)"
              />
              <span class="text">共识别 {{ batch.invoice.length }} 张发票</span>
            </div>
          </div>
          <div
            v-for="(invoice, index) in batch.invoice"
            :key="invoice.invoiceId"
          >
            <div class="item">
              <invoice-card
                :value="invoice"
                :showDelete="true"
                :disabled="showSelect(invoice)"
                :showSelect="true"
                :edit="showEdit(invoice)"
                @edit="onInvoiceCardEdit(invoice, i, index)"
                @del="onInvoiceCardDel(i, index)"
                style="flex-grow: 1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {
  Component,
  Prop,
  Vue,
  Ref,
  Watch,
  Inject,
} from "vue-property-decorator";
import { CONTEXT_PROVIDER, defaultContext } from "./context";
import UploadInvoiceForm from "./UploadInvoiceForm.vue";
import { showError } from "../../util";
import InvoiceCard from "./InvoiceCard.vue";
import OButton from "./button";
import Checkbox from "./Checkbox.vue";
import Popup from "./Popup.vue";
import { Notify } from "vant";

import { upload } from "./util"
import Compressor from 'compressorjs';

interface UploadBatch {
  invoice: any[];
}

const test = {
  invoiceType: "VAT_INVOICE",
  vatInvoiceType: "VAT_GENERAL_INVOICE_ELECTRON",
  verifyResult: "PASS",
  sellerName: "北京张三餐饮有限公司",
  invoiceDate: "2021-02-03",
  totalAmount: "300.00",
  originalInvoice: {
    type: "OTHER",
  },
};

@Component({
  components: {
    UploadInvoiceForm,
    InvoiceCard,
    OButton,
    Checkbox,
    Popup,
  },
})
export default class UploadInvoice extends Vue {
  @Prop() uploadUrl!: string;
  @Ref("upload-invoice-form") uploadInvoiceForm!: UploadInvoiceForm;
  @Ref("container") container!: HTMLElement;
  @Ref("upload") upload!: any;

  files: any[] = [];
  batch: UploadBatch[] = [];
  uploading = false;
  uploadInvoiceWayPopup = false;
  limit: number = 3;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  @Watch("uploadInvoiceWayPopup")
  onWatchUploadInvoiceWayPopup(newValue) {
    // 关闭上 传发票 弹框，向上暴露close事件
    // 场景：用户点击表单的添加发票按钮，会有两个弹框弹出，依次弹出 1.继续上传发票弹框，2.发票上传方式弹框
    // 如果未添加发票数据 ， 关闭发票上传方式弹框， 外层监听到close事件，然后通过 batch 判断是否上传过发票数据，如未上传 两个弹框通通关闭
    if (!newValue)
      this.$emit(
        "wayPopupClose",
        this.batch.length || this.uploading || this.uploadInvoiceForm.show
      );

    if (newValue) {
      this.upload?.clearFiles();
    }
  }

  get mobile() {
    return this.context.isMobile;
  }

  // 兼容安卓手机 拍照上传文件
  get accept() {
    return window.navigator.userAgent.toLocaleLowerCase().includes("android")
      ? "image/*"
      : "";
  }

  // 手机端不设置multiple为true,避免在小程序内无法唤起拍照
  get multiple() {
    return !this.mobile;
  }

  // 是否展示卡片的选择按钮
  showSelect(invoice) {
    // 发票作废
    if (
      invoice.invoiceCancellationMark &&
      invoice.invoiceCancellationMark === "CANCELLATION"
    ) {
      return false;
    }
    // 风险状态 为 FATAL ， 代表不可提交，不展示 勾选按钮
    if (
      Array.isArray(invoice.hintList) &&
      invoice.hintList.some((item) => item.level === "FATAL")
    )
      return false;

    // 如果不是 增值税发票 ，一直显示勾选按
    if ("VAT_INVOICE" !== invoice.invoiceType) return true;
    // 验真通过的才可已选
    return invoice.verifyResult === "PASS";
  }

  // 是否展示卡片的编辑按钮
  showEdit(invoice) {
    // 验真不通过的展示编辑按钮 并且 这期只有增值税发票可编辑
    return (
      invoice.verifyResult !== "PASS" && invoice.invoiceType === "VAT_INVOICE"
    );
  }

  // 是否展示局部全选按钮
  showAreaSelect(invoices) {
    // 局部内，只要包含一个可以选择的卡片，局部全选按钮 就可以展示
    return invoices.some((invoice) => this.showSelect(invoice));
  }

  // 是否展示 全局全选按钮
  showAppSelect() {
    // 局部包含可选按钮，全局可选按钮就可以用
    return this.batch.some((batch) => this.showAreaSelect(batch.invoice));
  }

  // 是否未全选状态
  isSelectAll(batch) {
    return (
      batch.length &&
      batch.every((item) => {
        if (!this.showAreaSelect(item.invoice)) return true;
        return this.isBatchSelectAll(item.invoice);
      })
    );
  }

  // 局部是否未全选状态
  isBatchSelectAll(batch) {
    return batch.every((item) => item.checked);
  }

  // 监听点击局部全选
  onBatchSelectAll(event, batch) {
    for (let item of batch) {
      // 只有未可选 状态的卡片 才设置选中状态
      if (this.showSelect(item)) {
        this.$set(item, "checked", event);
      }
    }
  }

  onExceed(field) {
    if (field.length > this.limit) {
      showError(`请最多上传 ${this.limit} 个文件。`);
    }
  }

  async onSuccess(response, file, files) {
    if (response.success) {
      this.batch.push({
        invoice: response.data.map((o) => ({
          ...o,
          checked: this.showSelect(o),
        })),
      });
    } else {
      if (response.message) {
        showError(response.message);
      }
    }
    this.uploading = false;

    await this.$nextTick();
    this.container.scrollTop = this.container.scrollHeight;
  }

  async compression (file){
    const currentSuf = file.name.substring(
      file.name.lastIndexOf(".") + 1
    ) as string;

      
    if (["jpeg", "jpg", "png"].indexOf(currentSuf.toLowerCase()) > -1) {


      const [error,result] = await new Promise((resolve,reject)=>{
        new Compressor(file, {
          quality: 0.4,
          maxWidth:3000,
          success(result) {
            console.log("压缩成功")
            resolve([null,result])
          },
          error(err) {
            resolve([err])
          },
        })
      }) as any

      if(error) {
        console.error(error)
        showError("文件大小不能超过6M！");
        throw new Error("文件大小不能超过6M！")
      }

      console.log({result},result.size/1024/1024)

      if (result.size > 6 * 1024 * 1024) {
        showError("文件大小不能超过6M");
        throw new Error("文件大小不能超过6M")
      }


      return result
   
    }

    return file
  }

  previewBlob(blob) {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(blob);
    fileReader.onload = (e) => {
      const imageUrl = (e as any).target.result;
      const image = new Image();
      image.src = imageUrl;
      document.body.appendChild(image); // 将图像添加到页面中
    };
  }

  async httpRequest (options){
    const params = options
    options.file = await this.compression(options.file)
    // this.previewBlob(options.file )
    return upload(params)
  }

  // 上传之前对文件格式做校验
  async beforeUpload(file) {
    const suffix = ["jpeg", "jpg", "png", "pdf", "ofd"];
    const currentSuf = file.name.substring(
      file.name.lastIndexOf(".") + 1
    ) as string;

    if (suffix.indexOf(currentSuf.toLowerCase()) < 0) {
      showError(`只允许上传jpg、jpeg、png、pdf、ofd格式的发票`);
      return false;
    }
    return true;
  }

  invoiceFormConfirm(invoice) {
    const checked = this.showSelect(invoice);
    if (typeof invoice.parentIndex === "number") {
      this.$set(this.batch[invoice.parentIndex].invoice, invoice.index, {
        ...invoice,
        checked,
      });
      return;
    }
    this.batch.push({
      invoice: [
        {
          ...invoice,
          checked,
        },
      ],
    });
  }

  // 删除
  onInvoiceCardDel(parentIndex, index) {
    this.$delete(this.batch[parentIndex].invoice, index);
    if (!this.batch[parentIndex].invoice.length) {
      this.$delete(this.batch, parentIndex);
    }
    Notify({
      message: "操作成功",
      type: "success",
    });
  }

  // 编辑
  async onInvoiceCardEdit(invoice, parentIndex, index) {
    const {
      invoiceCode,
      invoiceDate,
      invoiceNumber,
      pretaxAmount: amount,
      checkCode,
    } = invoice;
    this.uploadInvoiceForm.show = true;
    await this.$nextTick();
    this.uploadInvoiceForm.setFromData({
      invoiceNumber,
      invoiceCode,
      invoiceDate,
      checkCode,
      amount,
      parentIndex,
      index,
      image: invoice?.originalInvoice?.url,
    });
  }

  showUploadInvoicePopup(show = true) {
    this.uploadInvoiceWayPopup = show;
  }

  handleAddInvoiceBtnClick() {
    this.showUploadInvoicePopup();
  }

  handleManuallyBtnClick() {
    this.uploadInvoiceForm.show = true;
    this.showUploadInvoicePopup(false);
  }

  onError(err, file, files) {
    this.uploading = false;
  }

  onProgress(event, file, files) {
    this.uploading = true;
    this.showUploadInvoicePopup(false);
  }

  onOk() {
    let selected = this.batch.flatMap((o) =>
      // 卡片状态 有选择按钮 并且 是选中状态
      o.invoice.filter((i) => this.showSelect(i) && i.checked)
    );
    if (!selected.length) return showError("没有可选中的发票");
    this.$emit("save", [...selected]);
  }

  onSelectAll(v) {
    this.batch
      .flatMap((o) => o.invoice)
      .forEach((o) => {
        if (this.showSelect(o)) {
          Vue.set(o, "checked", v);
        }
      });
  }
}
</script>
<style scoped lang="less">
.upload-invoice {
  position: relative;
  background: #fafafa;

  &-popup {
    display: flex;
    padding-left: 16px;
    padding-top: 20px;
    .upload /deep/ .el-upload {
      width: 100%;
    }
    .olading-iconfont {
      font-size: 18px;
      margin-right: 8px;
      color: #4f71ff;
    }
    div {
      flex: 1;
      margin-right: 16px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #070f29;
      cursor: pointer;
      text-align: center;
      background: #f7f8fa;
      border-radius: 6px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .flex-1 {
    flex: 1;
  }
  .header {
    line-height: 67px;
    height: 67px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #070f29;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .card-box-item {
    padding: 0 16px;
    padding-bottom: 10px;
    margin-bottom: 10px;
    background: #fff;
    .select {
      display: flex;
      align-items: center;
      .text {
        padding-left: 8px;
      }
    }
    .item {
      background: #fff;
      margin-bottom: 10px;
    }
  }
  .add {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #4f71ff;
    cursor: pointer;
  }
  .container {
    height: 100vh;
    overflow: auto;
    padding-bottom: 120px;
    &.desktop {
      height: 600px;
    }
  }
  .footer-action-buttons {
    position: absolute;
    width: 100%;
    background: #fff;
    z-index: 1;
    bottom: 0;
    align-items: center;
    .top {
      height: 56px;
      padding: 0 18px;
      align-items: center;
      display: flex;
      border-top: 0.5px solid #fafafa;
    }
    .bottom {
      height: 62px;
      background: #ffffff;
      box-shadow: 0 -1px 4px 0 rgba(224, 224, 224, 0.6);
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 18px;
      .el-button--primary {
        width: 100%;
      }
    }
    .select-text {
      color: #888;
    }
  }
}
</style>