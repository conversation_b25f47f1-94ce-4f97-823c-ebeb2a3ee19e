import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';
import Switch from './Switch.vue';

<Meta title="基础组件/o-switch" component={Switch} argTypes={{
  readonly: {
    type: 'boolean'
  },
  disabled: {
    type: 'boolean'
  },
  activeText: {
    type: 'string'
  },
  inactiveText: {
    type: 'string'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Switch },
  data: () => ({ check: false }),
  template: '<o-switch v-bind="$props" v-model="check">名称</o-switch>',
});


主题

<Canvas>
  <Story 
    name="默认"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

禁用

<Canvas>
  <Story 
    name="disabled"
    args={{
      disabled: true,
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

文字

<Canvas>
  <Story 
    name="文字"
    args={{
      activeText: '男',
      inactiveText: '女'
    }}>
    {Template.bind({})}
  </Story>
</Canvas>