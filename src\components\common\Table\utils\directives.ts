import { parse2px } from "../../util"

export const emptyHeight = (el,binding)=>{
  setTimeout(() => {
    const emptyBlockEl = el.querySelector(".el-table__empty-block") as HTMLElement
    const tableWrapEl = el.querySelector(".el-table__body-wrapper") as HTMLElement
    if(emptyBlockEl){
      el.classList.add("empty")
      emptyBlockEl.style.height = parse2px(binding.value)
      if(tableWrapEl) tableWrapEl.scrollTo(0,0)
    }else{
      el.classList.remove("empty")
    }
  })
}

export const loadingHeight = (el,binding,vnode)=>{
  if(binding.value) {
    const context = vnode.context.$el
    const tableHeaderEl = context.querySelector(".el-table__header")
    const rect = tableHeaderEl.getBoundingClientRect()
    const contextRect = context.getBoundingClientRect()
    el.style.top = rect.y + "px"
    el.style.left = contextRect.left + "px"
  }
}

export default {
  emptyHeight,
  loadingHeight
}