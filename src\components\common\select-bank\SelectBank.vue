<template>
  <div class="select-bank">
    <tree-view
      class="select-bank-tree"
      :loader="loader"
      :value="selected"
      :limit="20"
      :enableSearch="true"
      rootTitle="银行信息"
      @change="onChange"
    >
      <template v-slot:default="r">
        <div class="select-bank-record">
          {{ r.item.name }}
        </div>
      </template>
    </tree-view>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { listBank } from "../../../util";
import OButton from "../button";

import TreeView from "../TreeView.vue";

@Component({
  components: {
    TreeView,
    OButton,
  },
})
export default class  SelectBank extends Vue {
  @Prop() value!: string;

  selected: string[] = [];

  loader: any = null;

  mounted() {
    this.refresh();
    this.measure();
  }

  @Watch("value")
  watchValue() {
    this.measure();
  }

  measure() {
    this.selected.splice(0, this.selected.length);
    if (this.value) {
      this.selected = [this.value];
    }
  }

  async refresh() {
    this.loader = async ({ directory, start, limit, keywords }) => {
      let data = await listBank({
        filters: {
          keywords,
        },
        start,
        limit,
      });
      return data.list.map((o) => ({
        id: o.id,
        name: o.name,
        selectable: true,
        isDirectory: false,
        data: o,
      }));
    };
  }

  onChange(item) {
    this.selected = [item.id];
    this.$emit("input", [...this.selected]);
    this.$emit("update:change", item);
  }
}
</script>

<style scoped lang="less">
.select-bank {
  display: flex;
  flex-direction: column;

  .select-bank-tree {
    // flex-grow: 1;
    height: calc(100% - 50px);
  }

  .select-bank-record {
    padding: 20px 5px 20px 5px;
  }

  .select-bank-bottom {
    display: flex;
    height: 50px;
    padding: 5px 20px 5px 20px;

    .select-bank-status {
      flex-grow: 1;
    }

    .select-bank-button {
      width: 100px;
    }
  }
}
</style>