<template>
  <div class="select-city">
    <tree-view
      class="select-city-tree"
      :loader="loader"
      :value="selected"
      :limit="20"
      rootTitle="中华人民共和国"
      @change="onChange"
    >
      <template v-slot:default="r">
        <div class="select-city-record">
          {{ r.item.name }}
        </div>
      </template>
    </tree-view>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { listCity } from "../../../util";
import OButton from "../button";

import TreeView from "../TreeView.vue";

@Component({
  components: {
    TreeView,
    OButton,
  },
})
export default class SelectCity extends Vue {
  @Prop() value!: any;
  // 显示的最大深度,默认无限制，0为第一级,
  @Prop() maxDeep!: number;
  // 可选择的深度,例如[0, 2]，只允许选择第一级和第三级, 默认无限制
  @Prop() selectableDeep!: number[];
  // 只能选择叶子节点
  @Prop() onlySelectLeaf!: any;

  selected: string[] = [];

  userCache: any = {};
  deptCache: any = {};

  loader: any = null;

  mounted() {
    this.refresh();
    this.measure();
  }

  @Watch("value")
  watchValue() {
    this.measure();
  }

  measure() {
    this.selected.splice(0, this.selected.length);
    if (this.value) {
      this.selected = [this.value];
    }
  }

  async refresh() {
    this.loader = async ({ directory, start, limit, keywords }) => {
      let parentId: any = directory ? [directory.id] : [null];
      let childrenNum: any = undefined;
      if (keywords) {
        parentId = undefined;
        childrenNum = 0;
      }
      let data = await listCity({
        filters: {
          country: ["CHN"],
          parentId,
          keywords,
          childrenNum,
        },
        start,
        limit,
      });

      let maxDeep = this.maxDeep ?? 100;
      let currentDeep = directory ? directory.data.deep + 1 : 0;

      let onlySelectLeaf =
        this.onlySelectLeaf === "" || this.onlySelectLeaf == true;
      let selectableDeep = this.selectableDeep ?? [0, 1, 2, 3];

      return data.list.map((o) => {
        let isLeaf = maxDeep == currentDeep || o.childrenNum == 0;
        let selectable = onlySelectLeaf
          ? isLeaf
          : selectableDeep.indexOf(o.deep) >= 0;
        return {
          id: o.id,
          name: o.name,
          selectable,
          isDirectory: o.childrenNum > 0 && currentDeep < maxDeep,
          data: o,
        };
      });
    };
  }

  onChange(item) {
    this.selected = [item.id];
    this.$emit("input", [...this.selected]);
  }

  async onOk() {
    this.$emit("input", [...this.selected]);
  }
}
</script>

<style scoped lang="less">
.select-city {
  display: flex;
  flex-direction: column;

  .select-city-tree {
    // flex-grow: 1;
    height: 100%;
  }

  .select-city-record {
    padding: 20px 5px 20px 5px;
  }

  .select-city-bottom {
    display: flex;
    height: 50px;
    padding: 5px 20px 5px 20px;

    .select-city-status {
      flex-grow: 1;
    }

    .select-city-button {
      width: 100px;
    }
  }
}
</style>