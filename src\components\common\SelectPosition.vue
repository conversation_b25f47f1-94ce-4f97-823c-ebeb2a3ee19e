<template>
  <div class="select-post">
    <tree-view class="select-post-tree" :loader="loader" :value="selected" :enableSearch="enableSearch" rootTitle="岗位信息"
      :searchInputProps="searchInputProps" ref="tree-view" :limit="10000" @input="onInput">
      <template v-slot:default="{ item, index }">
        <div class="select-post-record">
          <p class="post-name">{{  item.name || "-"  }}</p>
        </div>
      </template>
    </tree-view>
    <div class="select-post-bottom">
      <div class="select-count">
        <div v-if="maxSelectNum !== 1">
          <span> 已选：{{  selected.length  }} 个 </span>
        </div>
      </div>
      <OButton type="primary" class="select-post-button" @click="onOk">确定</OButton>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch, Ref } from "vue-property-decorator";
import {
  Dept,
  DeptMember,
  listDept,
  listMerchantMember,
  MerchantMember,
  listPost,
  showError,
  Position,
} from "../../util";
import OButton from "./button";

import TreeView from "./TreeView.vue";
import Avatar from "./Avatar.vue";

// 搜索输入框属性
const searchInputProps = {
  placeholder: "搜索名称",
};

// 名称卡片颜色
const avatarColor = ["#5B84F6", "#6FD6B4", "#F4AE40"];



@Component({
  components: {
    TreeView,
    OButton,
    Avatar,
  },
})
export default class UserListView extends Vue {
  @Prop() value!: any[];
  // 选择目标
  @Prop({
    default: "user",
  })
  @Prop({
    default: 1,
  })
  maxSelectNum!: number;

  @Prop() readonly enableSearch!: boolean;

  @Ref("tree-view") readonly treeView!: TreeView;

  selected: string[] = [];
  loader: any = null;
  postCache: any = {};

  searchInputProps = searchInputProps;
  avatarColor = avatarColor;

  mounted() {
    this.refresh();
    this.measure();
  }


  @Watch("value")
  watchValue() {
    this.measure();
  }

  measure() {
    this.selected.splice(0, this.selected.length);
    if (this.value) {
      this.selected = this.value.map(elem => (elem.id));
    }
  }

  handleCloseBtnClick() {
    this.$emit("close");
  }
  async refresh() {
    this.loader = async ({ directory, keywords, start, limit }) => {

      const _postToItem = data => data.list.map(o => {
        this.postCache[o.id] = o;
        return {
          id: o.id,
          name: o.name,
          selectable: true,
          isDirectory: false,
          data: o,
        };
      });

      if (keywords) {
        let data = await listPost({
          start,
          limit,
          filters: {
            keywords: keywords,
          },
        });
        return _postToItem(data);
      }
      let data = await listPost({
        filters: {
        },
        start,
        limit,
      });
      return _postToItem(data);
    };
  }
  onInput(v) {
    if (this.maxSelectNum >= 1) {
      if (this.maxSelectNum == 1) {
        // 如果只能选一个，自动删除之前选择的项
        if (v.length > 1) {
          v = v.splice(1);
        }
        // 如果不加这个 "v.length>= this.maxSelectNum" , 用户选到最大限制的岗位数以后 无法取消选中状态
      } else if (
        this.selected.length >= this.maxSelectNum &&
        v.length >= this.maxSelectNum
      ) {
        showError(`最多支持选择${this.maxSelectNum}岗位`);
        return;
      }
    }
    this.selected = v;
  }
  async onOk() {
    let value = this.selected.map(id => ({ id, name:this.postCache[id].name }));
    this.$emit("input", value);
  }
}
</script>

<style scoped lang="less">
.select-post {
  display: flex;
  flex-direction: column;

  .select-post-tree {
    flex: 1;
    min-height: 0;
  }

  .select-post-record {
    display: flex;
    align-items: center;

    .post-name {
      margin: 0;
      margin-left: 12px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
  }

  .select-dept-record {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    letter-spacing: 0;
    line-height: 16px;
    position: relative;
    padding-right: 80px;

    .el-icon-arrow-right {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      color: #d2d4db;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .select-post-bottom {
    display: flex;
    height: 74px;
    display: flex;
    background: #ffffff;
    box-shadow: 0 -2px 8px 0 rgba(224, 224, 224, 0.6);
    align-items: center;
    border-radius: 8px;

    .select-count {
      flex: 1;
      color: var(--o-primary-color);
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #4f71ff;
      padding-left: 16px;
      text-align: left;

      i {
        font-weight: bold;
        margin-left: 4px;
      }
    }

    .select-post-button {
      width: 165px;
      height: 46px;
      background: var(--o-primary-color);
      border: 0;
      // color: #ffffff;
      border-radius: 4px;
      margin-right: 16px;
    }
  }
}
</style>