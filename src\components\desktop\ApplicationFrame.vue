<template>
  <div class="o-app-frame" v-if="showNav">
    <div class="o-app-frame_nav">
      <nav-menu
        :theme="theme"
        :webConfig="webConfig"
        :currentNav="currentNav"
        :notOpenDisplayRules="notOpenDisplayRules"
        :openApp="openApp"
        :userProfile="userProfile"
        :navigation="navigation"
        :userType="userType"
        :userInfo="userInfo"
        :enterpriseLoading="bindInfoLoading"
        :logo="enterpriseLogo"
        :roles="roles"
        :hasAuthMenuSizeMap="hasAuthMenuSizeMap"
        :appMenuList="appMenuList"
        :isManager="isManager"
        :enterpriseInfo="enterpriseInfo"
        :enterpriseList="enterpriseList"
        @on-change-enterprise="onChangeEnterprise"
      />
    </div>
    <div class="o-app-frame_layout">
      <div class="o-app-frame_layout_menu" v-if="showMenu">
        <main-menu
          :openApp="openApp"
          :roles="roles"
          :appInfo="appInfo"
          :appCode="currentNav"
          :loading="enterpriseLoading"
          :theme="theme"
          :appIcon="appIcon"
          :isManager="isManager"
          :notOpenDisplayRules="notOpenDisplayRules"
          :routePath="routePath"
          :menus="menus"
        />
      </div>
      <div class="o-app-frame_layout_content">
        <div
          class="o-app-frame_layout_content_wrapper"
          :style="{ background: isBackground ? '#FFFFFF' : 'none' }"
        >
          <template v-if="$app.currentHasPrivilege">
            <keep-alive>
              <router-view v-if="$route.meta.keepAlive" />
            </keep-alive>
            <router-view v-if="!$route.meta.keepAlive" />
          </template>
          <forbidden v-if="!$app.currentHasPrivilege" />
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <router-view />
  </div>
</template>

<script lang="ts">
import MainMenu from "./MainMenu.vue";
import NavMenu from "./NavMenu.vue";
import { Component, Inject, Vue, Watch } from "vue-property-decorator";
import Page403 from "./Page403.vue";
import {
  EnterpriseData,
  MenuData,
  Navigation,
  NavigationData,
  RoleData,
} from "../../model/type";
import {
  Enterprise,
  getAppListByCodes,
  getAppMenu,
  getAppMenuList,
  getBindInfo,
  getConfigInfo,
  getIcon,
  getInfoByToken,
  getMerchant,
  getMerchantUserPrivilege,
  getProjectPath,
  isError,
  isMananger,
  merchantProfile,
  platformNav,
  profile,
  UserInfo,
  userProfile,
} from "../../util";
import axios from "axios";
import {
  getAppCodeCache,
  getEnterpriseCache,
  getHasAuthMenuSize,
  getMenuCache,
  getNavCache,
  getRoleCache,
  isWorkBench,
  mosaicMobile,
  removeNavCache,
  setEnterpriseCache,
  setMenuCache,
  setNavCache,
  setRoleCache,
} from "../../util/desktop";
import { UserType } from "../../enums/nav";

@Component({
  components: {
    MainMenu,
    NavMenu,
  },
  beforeCreate() {
    let Forbidden = (this as any).forbidden;
    if (Forbidden) {
      this.$options.components!.Forbidden = Forbidden;
    } else {
      this.$options.components!.Forbidden = Page403;
    }
  },
})
export default class ApplicationFrame extends Vue {
  @Inject("__app_forbidden__") readonly forbidden!: any;

  //导航数据
  private navigation?: Navigation[] = [];

  //用户信息
  private userInfo?: UserInfo = {
    merchant: {},
    merchantMember: {},
    user: {},
  };

  //主题
  private theme: string = (window as any).env.platform.theme || "";

  //企业列表
  private enterpriseList: Enterprise[] = [];

  //企业列表加载状态
  private enterpriseLoading: boolean = false;

  //企业logo
  private enterpriseLogo: string = "";

  //原profile接口数据
  private enterpriseInfo: any = {};

  //侧边菜单
  private menus = [];

  //用户类型   默认企业用户
  private userType: UserType = UserType.ENTERPRISE;

  //web配置
  private webConfig: any = {};

  //用户概述
  private userProfile: any = {};

  //已开通应用
  private openApp: string[] = [];

  //是否为管理员
  private isManager: boolean = false;

  //未开通应用显示规则
  private notOpenDisplayRules: object = {};

  //当前选中菜单
  private currentNav: string = "";

  //侧边菜单权限
  private roles: string[] = [];

  //有权限菜单
  private hasAuthMenuSizeMap: any = {};

  //所有带appcode的侧边菜单树
  private appMenuList: any = {};

  //appinfo
  private appInfo: any = {};

  //是否显示导航菜单
  private showNav: boolean = true;

  //是否显示侧边菜单
  private showMenu: boolean = true;

  //侧边菜单图标
  private appIcon: string[] = [];

  //路由path
  private routePath: string = "";

  //查询bindInfo接口loading状态
  private bindInfoLoading: boolean = false;

  //是否显示背景颜色
  private isBackground: boolean = true;

  /**
   * @description 获取appcode
   */
  private getAppCode(list, appCodeMap) {
    list.map((item) => {
      if (item.appCode) {
        appCodeMap[item.appCode] = true;
      }
      if (item.appMenuInfo && item.appMenuInfo.appCode) {
        appCodeMap[item.appMenuInfo.appCode] = true;
      }
      if (item.children.length > 0) {
        this.getAppCode(item.children, appCodeMap);
      }
    });
  }

  /**
   * @description 转换应用显示规则数据，便于导航解析
   */
  private convertDisplayRule(ruleList: []) {
    let ruleMap = {};
    ruleList.map((item: any) => {
      ruleMap[item && item.code] = item;
    });
    return ruleMap;
  }

  /**
   * @description 监听路由变化，能实时获取状态，判断是否显示导航与菜单
   * @param $route: 路由跳转对象
   */
  @Watch("$route")
  private watchRoute($route): void {
    // console.info("route", $route);
    
    let navCache = getNavCache();
    if(!navCache) this.init()
    
    this.setRoutePath();
    this.isShowNav($route);
  }

  /**
   * @description 监听项目路径变化
   */
  // @Watch("projectPath")
  // private watchProjectPath(projectPath) {
  //     if (!this.currentNav) {
  //         if (isWorkBench(projectPath["sso"])) {
  //             this.currentNav = "WORKBENCH";
  //         }
  //     }
  // }

  private isShowNav($route): void {
    let meta = $route.meta;
    //判断是否显示导航菜单
    if (meta && meta.nav !== undefined) {
      this.showNav = meta.nav;
    } else {
      this.showNav = true;
    }

    //判断是否显示侧边菜单
    if (meta && meta.menu !== undefined) {
      this.showMenu = meta.menu;
    } else {
      this.showMenu = true;
    }

    if (meta && meta.background === false) {
      this.isBackground = false;
    }
  }

  /**
   * @description 设置路由path
   */
  private setRoutePath() {
    this.routePath = this.$route.path;
  }

  /**
   * @description 获取平台导航数据
   * @return navList
   */
  private getPlatformNav() {
    return platformNav({
      merchantId: 0,
    });
  }

  /**
   * @description 设置顶部导航所需要的数据
   * @param data:NavigationData
   */
  private setNavigationData(data: NavigationData): void {
    //设置导航数据
    this.navigation = data.navigation;
    //设置用户信息
    this.userInfo = data.userInfo;
    //设置已开通app
    this.openApp = data.openApp;
    //设置权限数据
    this.roles = data.roles;
    //设置企业logo
    // this.enterpriseLogo = data.enterpriseLogo;
    //设置企业基本信息
    this.enterpriseInfo = data.enterpriseInfo;
    //设置是否为管理员
    this.isManager = data.isManager;
    //设置未开通应用显示规则
    this.notOpenDisplayRules = data.notOpenDisplayRules;
    //设置有权限菜单数量
    this.hasAuthMenuSizeMap = data.hasAuthMenuSizeMap;
    //设置所有带应用code的菜单树数据
    this.appMenuList = data.appMenuList;
    //设置企业列表
    this.enterpriseList = data.enterpriseList;
  }

  /**
   * @description 设置菜单数据
   * @param data MenuData
   */
  private setMenuData(data: MenuData) {
    //设置菜单
    this.menus = data.menus;
    //设置appinfo
    this.appInfo = data.appInfo;
    //设置侧边菜单顶部图标
    this.appIcon = data.appIcon;
  }

  /**
   * @description 设置企业列表数据
   */
  private setEnterpriseData(data: EnterpriseData) {
    //企业列表
    this.enterpriseList = data.enterpriseList;
  }

  /**
   * @description 设置权限数据
   */
  private setRoleData(data: RoleData) {
    this.roles = data.roles;
  }

  /**
   * @description 获取加入的企业
   * @param joinedMerchant: 加入的企业
   * @param user: 当前用户信息
   */
  private getJoinedMerchant(joinedMerchant, user) {
    //定义用户工作空间
    // let userSpace = {
    //     merchant: {
    //         userId: user.id,
    //         name: user.realName || mosaicMobile(user.cellPhone)
    //     }
    // };
    // joinedMerchant.unshift(userSpace);
    return joinedMerchant;
  }

  /************************************************   初始化加载数据start   ***********************************************************/

  /**
   * @description 加载权限数据
   */
  private loadRoles() {
    //获取权限缓存数据
    let roleCache = getRoleCache();
    if (roleCache) {
      this.setRoleData(roleCache);
    } else {
      getMerchantUserPrivilege().then((list) => {
        if (list.length > 0) {
          let data = {
            roles: list[0].usableFunctionCodes,
          };
          this.setRoleData(data);
          setRoleCache(data);
        }
      });
    }
  }

  /**
   * @description 查询用户基本信息
   */
  private loadUserProfile() {
    //查询个人基本信息
    userProfile()
      .then((data) => {
        this.userProfile = data;
      })
      .catch((data) => {
        if (isError(data.errorCode)) {
          return false;
        }
      });
  }

  /**
   * @description 加载网站配置
   */
  private loadWebsiteConfig() {
    axios
      .all([getConfigInfo(), getMerchant()])
      .then(
        axios.spread((webConfig, merchant) => {
          this.webConfig = {
            ...webConfig,
            ...merchant,
          };
          if (webConfig.tabLogo) {
            try {
              let link:any = document.querySelector("link[rel*='icon']") || document.createElement("link")
              link.type = "image/x-icon"
              link.rel = "shortcut icon"
              link.href = webConfig.tabLogo
              document.getElementsByTagName("head")[0].appendChild(link);
            } catch (e) {
              console.error(e);
            }
          }
        })
      )
      .catch((data) => {
        if (isError(data.errorCode)) {
          return false;
        }
      });
  }

  /**
   * @description 加载平台profile
   */
  private loadProfile() {
    profile({
      withPrivileges: true,
      withRoles: true,
    })
      .then((data) => {
        this.enterpriseList = this.getJoinedMerchant(
          data.joinedMerchant,
          data.user
        );
        this.userInfo = data;
      })
      .catch((data) => {
        if (isError(data.errorCode)) {
          return false;
        }
      });
  }
  /************************************************   初始化加载数据end   ***********************************************************/

  /**
   * @description 初始化方法
   */
  public async init() {
    //设置是否显示导航
    this.isShowNav(this.$route);
    //不显示导航，不执行后面的逻辑
    if (!this.showNav) {
      return;
    }
    this.setRoutePath();
    //获取链接上的参数
    let query = this.$route.query;
    //设置appcode
    let appcode;
    if (query) {
      appcode = query.app;
    }
    //如果链接上没有appcode，则去会话里面查找
    if (!appcode) {
      appcode = getAppCodeCache();
    }
    this.currentNav = appcode || "";

    this.loadUserProfile();

    this.loadWebsiteConfig();

    //首先通过token获取info信息
    let info = await getInfoByToken();
    //若merchantId为空，则代表登陆的是个人用户
    if (!info.merchantId) {
      this.loadProfile();
      this.userType = UserType.PERSONAL;
      this.openApp = [];
      this.roles = [];
      this.isManager = false;
      this.navigation = [
        {
          name: "工作台",
          event: "OPEN_WORKBENCH",
          children: [],
        },
      ];

      return false;
    } else {
      this.userType = UserType.ENTERPRISE;
    }
    // this.loadRoles();
    //获取缓存中的导航数据
    let navCache = getNavCache();
    //获取导航数据
    let navigation;
    if (navCache) {
      navigation = navCache.navigation;
      this.setNavigationData(navCache);
    } else {
      let appCodes: string[] = [];
      try {
        navigation = await this.getPlatformNav();
        //获取导航数据中的appCode
        let appCodeMap = {};
        this.getAppCode(navigation, appCodeMap);
        appCodes = Object.keys(appCodeMap);
      } catch (e) {
        console.error(e);
      }
      axios
        .all([
          profile({
            withPrivileges: true,
            withRoles: true,
          }),
          merchantProfile(),
          isMananger(),
          getAppListByCodes(appCodes),
          getAppMenuList({ appCodes: appCodes }),
          getMerchantUserPrivilege(),
        ])
        .then(
          axios.spread(
            (
              userInfo: any,
              merchantProfileInfo: any,
              isManager: any,
              rules: [],
              appMenuList: any,
              userPrivilege
            ) => {
              let data: NavigationData = {
                navigation: navigation,
                userInfo: userInfo,
                openApp: userInfo.merchant.openedBusiness,
                enterpriseInfo: merchantProfileInfo,
                isManager: isManager.mananger,
                hasAuthMenuSizeMap: {},
                appMenuList: appMenuList,
                roles: userPrivilege[0].usableFunctionCodes,
                notOpenDisplayRules: this.convertDisplayRule(rules),
                enterpriseList: this.getJoinedMerchant(
                  userInfo.joinedMerchant,
                  userInfo.user
                ),
              };
              data.hasAuthMenuSizeMap = getHasAuthMenuSize(
                appMenuList,
                data.roles,
                data.openApp,
                data.isManager
              );
              //设置导航数据
              this.setNavigationData(data);

              //设置缓存数据
              setNavCache(data);
            }
          )
        )
        .catch((data) => {
          if (isError(data.errorCode)) {
            return false;
          }
        });
    }

    if (appcode) {
      //获取缓存
      let menuCache = getMenuCache();
      if (menuCache && menuCache[appcode]) {
        this.setMenuData(menuCache[appcode]);
      } else {
        this.enterpriseLoading = true;
        axios
          .all([
            getAppMenu(appcode),
            getAppListByCodes([appcode]),
            getIcon([appcode]),
          ])
          .then(
            axios.spread((menus: any, appList: any[], appIcon: any) => {
              this.menus = menus;
              this.enterpriseLoading = false;
              //设置appinfo
              this.appInfo = appList[0] || {};
              //设置侧边菜单顶部图标
              this.appIcon = appIcon || [];

              let data: MenuData = {
                menus: menus,
                appInfo: appList[0] || {},
                appIcon: appIcon || [],
              };
              this.setMenuData(data);
              //设置缓存
              setMenuCache({
                [appcode]: data,
              });
            })
          );
      }
    }
  }

  private onChangeEnterprise() {
    this.init();
  }

  public created(): void {
    this.init();
    //监听浏览器刷新事件，刷新清除导航组件的sessionStorage缓存
    window.addEventListener("beforeunload", (e) => {
      removeNavCache();
    });

    //监听load事件
    window.addEventListener("loadNav", (e) => {
      removeNavCache();
      this.init();
    });
  }
}
</script>

<style  lang="less">
@import "../../assets/css/scrollbar.less";

.o-app-frame {
  background: #f7f7f7;
  &_nav {
    position: relative;
    z-index: 99;
  }
  &_layout {
    display: flex;
    &_menu {
    }
    &_content {
      flex: 1;
      padding: 16px 0px 0px 16px;
      box-sizing: border-box;
      height: calc(100vh - 64px) !important;
      overflow-y: scroll !important;
      //此处兼容子系统，因为此框架预留padding位置，不需要各页面预留
      &_wrapper {
        min-height: 100%;
        position: relative;
        background: #ffffff;
        box-shadow: 0 6px 12px -11px rgba(52, 61, 160, 0.16);
        border-radius: 8px 0px 0px 0px;
        /*padding-bottom: 20px;*/
        /*&>div {*/
        /*    &:nth-child(1) {*/
        /*        padding: 0;*/
        /*    }*/
        /*}*/
      }
    }
  }
}
</style>