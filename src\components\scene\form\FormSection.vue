<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import FormBlock from "../../common/FormBlock";

@Component({
  components: {
    FormBlock,
  },
})
export default class FormSection extends Vue {
  @Prop() title!: string;
  @Prop() titleRender!: Function;
  @Prop({ default: 2 }) columns!: number;

  render(h){

    const titleVNode = this.titleRender ? this.titleRender(h) : this.title ? h("span",{
      class:"text"
    },this.title) : ""

    return h("div",[
      h("div",{
        class:["o-form-section-title",this.titleRender?"title-render":"" ,titleVNode? "":"none"]
      },[titleVNode]),
      h("form-block",{
        props:{
          columns:this.columns
        }
      },this.$slots.default)
    ])
  }
}
</script>
<style lang='less' scoped>
</style>