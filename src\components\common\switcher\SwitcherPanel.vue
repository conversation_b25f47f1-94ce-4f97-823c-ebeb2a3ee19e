<template>
  <div v-show="vshow" v-if="vif">
    <slot></slot>
  </div>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class  SwitcherPanel extends Vue {
  @Prop() active!: boolean;
  @Prop() hide!: boolean;

  get vshow() {
    if (this.hide) {
      return this.active;
    }
    return true;
  }

  get vif() {
    if (!this.hide) {
      return this.active;
    }
    return true;
  }
}
</script>
