<template>
  <div class="tab">
    <el-tabs v-model="tabValue" type="card" @tab-remove="handleTabRemove" :closable="isClosable" @tab-click="handleTabClick">
      <el-tab-pane
        :key="item.title"
        v-for="item in tabs"
        :label="item.title"
        :name="item.routePath"
      />
    </el-tabs>
  </div>
</template>

<script>
import { traverseTree,ensureLeadingSlash } from '../common/util.ts'

const tabsCacheName = "__tabNavigation__"

const store = {
  get(key){
    try {
      let { type,value } = JSON.parse(sessionStorage.getItem(key))
      if(type=== "object") return JSON.parse(value)
      return value
    }catch{
      return null
    }
  },
  set(key,data){
    sessionStorage.setItem(key,JSON.stringify({
      type:typeof data,
      value:JSON.stringify(data)
    }))
  }
}

const getCurrentPath = ()=> {
  const { pathname,origin } = window.location
  return origin + pathname
}

export default {
  props:{
    slideMenu:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      tabs:[],
      tabValue: "",
      tabPathMap:{},
    };
  },
  computed:{
    isClosable(){
      return this.tabs.length > 1;
    }
  },
  created(){
    this.initTabs()
  },
  methods:{
    initTabs(){
      this.tabs = this.getTabsCache()
      this.tabValue = getCurrentPath()
      this.tabs.forEach(item=>{
        this.tabPathMap[item.routePath] = item
      })
      const currentPath = getCurrentPath()
      if(this.tabs[currentPath]) return 

      let { pathname } = window.location
      pathname = pathname.replace(this.$router.options.base,"")
      pathname = ensureLeadingSlash(pathname)

      traverseTree(this.$router.options.routes,item=>{
        const title = item.title || item?.meta?.title 
        if(item.path === pathname && title ) {
          this.insertTabMenuData({
            routePath:currentPath,
            title
          })
        }
      })
    },
    setTabsCache(){
      store.set(tabsCacheName,this.tabs)
    },
    getTabsCache(){
      const tabs = store.get(tabsCacheName) || []
      return tabs 
    },
    insertTabMenuData(item){
      if(this.tabPathMap[item.routePath]) {
        this.tabValue = item.routePath
        return 
      }
      this.tabPathMap[item.routePath] = item
      this.tabs.push(item);
      this.tabValue = item.routePath
      this.setTabsCache()
    },
    handleTabClick({ index }){
      const menu = this.tabs[index]
      this.$emit("openURL",menu)
    },
    handleTabRemove(routePath){
      delete this.tabPathMap[routePath]
      this.tabs = this.tabs.filter(item=>item.routePath !== routePath)
      if(routePath === this.$route.path) {
        this.handleTabClick({ index:0 })
      }
      this.setTabsCache()
    }
  }
};
</script>
<style scoped lang="less">
.tab {
  background-color:#f7f7f7;
  ::v-deep{
    .el-icon-close{
      width: 14px !important;
      font-size: 14px;
    }
    .el-tabs__nav-wrap{
      background-color:#fff !important;
    }
    .el-tabs__header{
      margin-bottom: 0 !important;
    }
    .el-tabs__nav{
      border-radius: 0 !important;
      border:0 !important;
    }
    .el-tabs__item{
      font-family: Microsoft YaHei,PingFang SC,Helvetica Neue,Helvetica,Hiragino Sans GB,\\5FAE\8F6F\96C5\9ED1,Arial,sans-serif!important;
      border:0;
      border-right:1px solid #dcdfe6 !important;
      border-bottom:1px solid #dcdfe6 !important;
      padding:0 8px !important;
      color:#303133;
      font-size: 12px !important;
      &.is-active{
        background-color:#f7f7f7 !important;
        border-bottom:1px solid #f7f7f7 !important;
      }
      &:nth-child(1){
        border-left:1px solid #dcdfe6 !important;
      }
    }
  }
}

</style>