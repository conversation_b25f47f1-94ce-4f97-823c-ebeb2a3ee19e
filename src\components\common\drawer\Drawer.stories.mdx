import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';

import Drawer from './Drawer.vue'

<Meta title="基础组件/o-drawer" component={Drawer} argTypes={{
  position: {
    control: { type: 'select' },
    options: ['', 'left', 'right', 'top', 'bottom'],
  },
  size: {
    type: 'number'
  }
}} />

export const template = `
<div>
  <o-button @click="show = true">打开</o-button>
  <o-drawer v-model="show" v-bind="$props">内容</o-drawer>
</div>
`
export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Drawer },
  data: () => ({ show: false }),
  template: template,
});


# 抽屉

支持四种方向

左

<Canvas>
  <Story 
    name="左"
    args={{
      title: '标题',
      position: "left"
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


右

<Canvas>
  <Story 
    name="right"
    args={{
      title: '标题',
      position: "right"
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

上

<Canvas>
  <Story 
    name="top"
    args={{
      title: '标题',
      position: "top"
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

下

<Canvas>
  <Story 
    name="bottom"
    args={{
      title: '标题',
      position: "bottom"
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

标题

<Canvas>
  <Story 
    name="标题"
    args={{
      title: '这是标题',
    }}>
    {Template.bind({})}
  </Story>
</Canvas>
