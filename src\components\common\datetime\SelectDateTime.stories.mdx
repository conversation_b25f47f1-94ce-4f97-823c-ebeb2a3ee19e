import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';
import SelectDateTimeField from './SelectDateTimeField.vue';

<Meta title="基础组件/o-select-datetime-field" component={SelectDateTimeField} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'year-month', 'date', 'datetime'],
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SelectDateTimeField },
  data: () => ({ date: "" }),
  template: '<o-select-datetime-field v-bind="$props" v-model="date"></o-select-datetime-field>',
});


# 选择日期和时间

<Canvas>
  <Story 
    name="默认"
    args={{
      value: 1
    }}>
    {Template.bind({})}
  </Story>
</Canvas>