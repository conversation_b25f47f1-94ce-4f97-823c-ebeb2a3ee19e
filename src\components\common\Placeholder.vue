<template>
  <div class="o-placeholder" :class="divClass" @click="$emit('click', $event)">
    <div class="flex-1">
      <slot >
        <span :class="objectClass">{{ text }}</span>
      </slot>
    </div>
    <i v-if="clearable && Object.keys($scopedSlots).length" @click.stop="$emit('clear')" class="icon olading-iconfont oi-icon_delete_hover"></i>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class  Placeholder extends Vue {
  @Prop() value!: string;
  @Prop() placeholder!: string;
  @Prop() border!: any;
  @Prop() hoverable: any;
  @Prop() error: any;
  @Prop({ default:false }) clearable: any;

  get text() {
    return this.value ? this.value : this.placeholder;
  }

  get divClass() {
    let v = [] as string[];
    if (this.border === "" || this.border === true) {
      v.push("border");
    }
    if (this.hoverable || this.hoverable === "") {
      v.push("o-hoverable");
    }
    if (this.error || this.error === "") {
      v.push("o-error");
    }
    return v;
  }

  get objectClass() {
    return {
      placeholder: this.value ? false : true,
    };
  }
}
</script>

<style lang='less' scoped>
.o-placeholder {
  line-height: 36px;
  min-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  border-radius: 4px;
  transition: border-color 0.2s;
  &.o-hoverable:hover:not(:focus) {
    border-color: #c0c4cc;
  }

  &.o-error {
    border-color: var(--o-red);
  }
  .flex-1{
    flex: 1;
  }
}

.placeholder {
  color: #cccccc;
}

.border {
  border: 1px solid #dcdfe6;
  padding: 0px 15px;
}

.oi-icon_delete_hover{
  color: #C5C7D1;
  &:hover{
    color: #A0A2AC;
  }
}
</style>