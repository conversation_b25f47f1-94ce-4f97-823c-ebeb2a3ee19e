<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { Table } from "element-ui"

@Component({
  inheritAttrs:false,
  extends:Table,
  computed:{
    fixedHeight(){
      const layout = (this as any).layout;
      return {
        height: layout.tableHeight ? layout.tableHeight + 'px' : ''
      };
    }
  }
})
export default class  ElTableExtends extends Vue{
}

</script>