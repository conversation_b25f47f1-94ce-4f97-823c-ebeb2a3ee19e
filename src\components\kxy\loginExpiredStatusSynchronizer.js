import { getToken } from '../../utils/token'
var timeoutHandler = null
var timeoutMS = 1000
var kxyOriginDomain = 'https://kxy.leishen.fun'
var isLogout = false
const setKXYOriginDomain = domain => {
  kxyOriginDomain = domain
}
const setTimeoutMS = ms => {
  timeoutMS = ms
}
const postMessage = message => {
  window.parent.postMessage(message, '*')
}
const parseJwt = token => {
  var base64Url = token.split('.')[1]
  var base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
  var jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map(function (c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
      })
      .join('')
  )

  return JSON.parse(jsonPayload)
}
const getKXYUserID = () => {
  const token = getToken()
  const loginInfo = parseJwt(token)
  return loginInfo.p.ex || ''
}
const loginActivated = () => {
  if (isLogout) {
    return
  }
  postMessage({
    action: 'loginActivated',
    userID: getKXYUserID()
  })
}
const logout = () => {
  postMessage({
    action: 'logout',
    userID: getKXYUserID()
  })
}

export const run = $alert => {
  window.document.addEventListener('mousemove', e => {
    if (timeoutHandler) {
      clearTimeout(timeoutHandler)
    }
    timeoutHandler = setTimeout(loginActivated, timeoutMS)
  })

  // window.addEventListener(
  //   'message',
  //   event => {
  //     const origin = event.origin
  //     if (origin !== kxyOriginDomain) {
  //       return
  //     }

  //     const message = event.data
  //     if (message.action !== 'loginExpired') {
  //       return
  //     }

  //     isLogout = true

  //     $alert('您的登录已过期, 请重新登录', {
  //       confirmButtonText: '确定',
  //       center: true,
  //       callback: action => {
  //         logout()
  //       }
  //     })
  //   },
  //   false
  // )
}

export default {
  setKXYOriginDomain,
  setTimeoutMS,
  run
}
