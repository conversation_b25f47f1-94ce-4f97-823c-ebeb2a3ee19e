import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import Comp from './SelectLegalField.vue';

<Meta title="业务组件/o-select-legal-field" component={Comp} argTypes={{
  readonly: {
    type: 'boolean'
  },
  placeholder: {
    type: 'string'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  data: () => ({ m: "" }),
  template: '<o-select-legal-field v-bind="$props" v-model="m"></o-select-legal-field>',
});


# 选择法人组件

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>
