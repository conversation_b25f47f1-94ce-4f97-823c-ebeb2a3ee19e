<template>
  <div class="search-history" >
    <div class="search-history_title">历史搜索</div>
    <div class="search-history_list" v-if="list.length > 0">
      <div class="search-history_list_item" @click="toPage(item)"  v-for="(item, index) in list">{{item.text}}</div>
    </div>
    <div class="search-history_nodata" v-else>暂无历史搜索记录</div>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Watch, Vue } from "vue-property-decorator";
  import {Search} from "../../../components/desktop/search-box/search";
  import {SearchVo} from "../../../model/type";
  @Component({
    name: "history-list"
  })
  export default class HistoryList extends Vue{

    private list:SearchVo[] = [];

    @Prop()
    private keywords?: string;

    @Watch("keywords")
    private watchKeywords(val) {
      if (val.length === 0) {
        this.init();
      }
    }

    private toPage(page) {
      let $parent:any = this.$parent;
      let resolverConfig = $parent.resolverConfig;
      Search.toPage(page, resolverConfig, this);
    }

    private init() {
      let $parent:any = this.$parent;
      let history = Search.getHistory($parent.userId, $parent.merchantId);
      if (history) {
        this.list = history;
      }
    }


    private created() {
      this.init();
    }
  }

</script>

<style lang="less">
  .default {
    .search-histroy-function(#47485A, #4F71FF, #F8F8F8);
  }
  .red {
    .search-histroy-function(#47485A, #BE0018, #F8F8F8);
  }

  .search-histroy-function(@color, @hoverColor, @background) {
    .search-history {
      font-family: PingFangSC-Regular;
      padding:  0 8px;
      &_title {
        height: 14px;
        font-weight: 400;
        font-size: 14px;
        color: #A8ACBA;
        letter-spacing: 0;
        line-height: 14px;
      }
      &_list {
        padding: 16px 0px;
        display: flex;
        flex-wrap: wrap;
        &_item {
          text-align: center;
          transition: all .3s;
          height: 30px;
          line-height: 30px;
          background: @background;
          border-radius: 4px;
          font-weight: 400;
          font-size: 14px;
          color: @color;
          letter-spacing: 0;
          cursor: pointer;
          padding: 0px 8px;
          box-sizing: border-box;
          margin-right: 12px;
          margin-bottom: 12px;
          &:hover {
            color: @hoverColor;
          }
        }
      }
      &_nodata {
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        color: #A8ACBA;
        letter-spacing: 0;
        line-height: 14px;
        margin-top: 24px;
        margin-bottom: 44px;
      }
    }
  }

</style>