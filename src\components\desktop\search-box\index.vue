<!--
  导航搜索功能
-->
<template>
  <div :class="theme">
    <div class="search-box">
      <div class="search-box_wrapper" :class="{'search-box_wrapper_open': isOpen}">
        <!-- 顶部搜索框 -->
        <div class="search-box_wrapper_top">
          <div class="search-box_wrapper_left" v-if="isOpen" :class="{'search-box_wrapper_left_show': isOpen}">
            <span class="iconfont icon-search" style="cursor: initial;"></span>
            <input v-model.trim="keyword" @input="inputChangeEvent" placeholder="搜索产品功能" :focus="isOpen"/>
          </div>

          <div class="search-box_wrapper_close" :style="{paddingLeft: isOpen ? '0px' : '14px'}">
            <span class="iconfont icon-icon_close" v-if="isOpen" @click="close"></span>
            <div class="search-box_wrapper_close_loupe" v-else @click="isOpen = true">
              <span class="iconfont icon-search" ></span>
            </div>
          </div>
        </div>
        <!-- 底部显示区域 -->
        <div class="search-box_wrapper_bottom">
          <div class="search-box_result webkit-scrollbar-line" :class="{'search-box_result_show': isOpen}">
            <result-list v-show="keyword.length > 0" :data="resultSource"/>
            <history-list :keywords="keyword" v-if="keyword.length === 0 && isOpen"/>
          </div>
        </div>

      </div>


      <div class="search-box_mask" @click="close" :class="{'show-mask': isOpen}"></div>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Watch, Vue } from "vue-property-decorator";
  import {AppMenuInfo, Navigation, NavResolverType, SearchMap, SearchVo, ZhToPinYinType} from "../../../model/type";
  import {SearchType} from "../../../enums/nav";
  import {filterMenu} from "../../../util/desktop";
  import {Menu} from "../../../model";
  import * as ZhToPinYin from "./tui-zh-pinyin.js";
  import {Search} from "../../../components/desktop/search-box/search";
  import ResultList from "../../../components/desktop/search-box/result-list.vue";
  import HistoryList from "../../../components/desktop/search-box/history-list.vue";

  @Component({
    name: "search-box",
    components: {HistoryList, ResultList}
  })
  export default class SearchBox extends Vue {

    //主题
    private theme: string = (window as any).env.platform.theme || "";
    /**
     * 用户id
     */
    @Prop()
    private userId?:number;

    /**
     * 企业id
     */
    @Prop()
    private merchantId?:number;

    /**
     * 是否展开
     */
    private isOpen:boolean = false;

    /**
     * 中文转拼音
     */
    private zhToPinYin:any = null;

    /**
     * 关键字
     */
    private keyword:string = "";

    /**
     * 数据源
     */
    private dataSource: SearchVo[] = [];

    /**
     * 结果集
     */
    private resultSource:SearchMap = {
      accurate: [],
      probably: []
    };

    /**
     * 导航所需配置项
     */
    private resolverConfig!:NavResolverType;


    /**
     * @description 打散appMenuList数据
     * @param appMenuList 应用对应菜单数据
     * @return Menu[]
     */
    private scatterAppMenuList(appMenuList):Menu[] {
      let menuList:Menu[] = [];
      for (let key in appMenuList) {
        //查找level等于1的节点
        let list:Menu[] = appMenuList[key].filter(item => item.level == 1);
        menuList.push(...list);
      }
      return menuList;
    }

    @Watch("isOpen")
    private watchIsOpen(val) {
      let nav = document.getElementsByClassName("o-app-frame_nav");
      if (nav.length > 0) {
        let frameNav:any = nav[0];
        if (val) {
          frameNav.style.zIndex = 9999;
        } else {
          frameNav.style.zIndex = 99;
        }
      }

    }

    private close() {
      this.isOpen = false;
      this.keyword = "";
    }
    /**
     * @description 提升层级，去掉分组名称层级
     */
    private promoteLevel(list:Navigation[]) {
      let children:SearchVo[] = [];
      for (let i = 0; i < list.length; i++) {
        let item:Navigation = list[i];
        if (item.groupName && item.children) {
          item.children.forEach(item => {
            children.push(this.transformNavToSearchVO(item));
          });
        }
      }
      return children;
    }

    /**
     * @description 转换导航为search实体
     * @param nav 导航数据
     * @return SearchVo
     */
    private transformNavToSearchVO(nav: Navigation):SearchVo {
      //中文转换拼音
      let zhToPinYin:ZhToPinYinType = {
        pinyin: this.zhToPinYin.getPinYin(nav.name, false).toUpperCase(),
        pinyinSpace: this.zhToPinYin.getPinYin(nav.name, true).toUpperCase(),
        initials: this.zhToPinYin.getInitials(nav.name).toUpperCase()
      };
      //定义搜索vo
      let searchVo:SearchVo = {
        id: nav.id || "",
        appCode: nav.appCode || "",
        level: nav.level,
        text: nav.name || "",
        url: nav.url,
        appMenuInfo: nav.appMenuInfo || null,
        type: SearchType.NAVIGATION,
        event: nav.event,
        zhToPinYin: zhToPinYin,
        children: null
      };
      return searchVo;
    }

    /**
     * @description 解析导航数据
     */
    private parseNavigation(navigationList: Navigation[]) {
      let navigationArray: SearchVo[] = [];
      for (let i = 0; i < navigationList.length; i++) {
        let navigation = navigationList[i];
        let children: SearchVo[] = [];
        if (navigation.children && navigation.children.length > 0) {
          //判断第一层是否有HAVE标记
          if (navigation.groupName === "HAVE" && navigation.level && navigation.level == 1) {
            children = this.promoteLevel(navigation.children);
          } else {
            children = this.parseNavigation(navigation.children);
          }
        }
        let searchVo:SearchVo = this.transformNavToSearchVO(navigation);
        navigationArray.push({
          ...searchVo,
          children: children
        });
      }
      return navigationArray;
    }

    /**
     * @description 解析菜单
     */
    private parseMenu(menuList: Menu[]):Array<SearchVo> {
      let menus:SearchVo[] = [];
      for (let i = 0; i < menuList.length; i++) {
        let m:Menu = menuList[i];
        let children:SearchVo[] = [];
        if (m.children && m.children.length > 0) {
          children = this.parseMenu(m.children);
        }
        //中文转换拼音
        let zhToPinYin:ZhToPinYinType = {
          pinyin: this.zhToPinYin.getPinYin(m.title, false).toUpperCase(),
          pinyinSpace: this.zhToPinYin.getPinYin(m.title, true).toUpperCase(),
          initials: this.zhToPinYin.getInitials(m.title).toUpperCase()
        };
        //定义搜索vo
        let searchVo:SearchVo = {
          id: m.id,
          appCode: m.appCode,
          belongProject: m.belongProject || "",
          incrementBusinessCode: m.incrementBusinessCode,
          incrementLevel: m.incrementLevel,
          privilegeCode: m.privilegeCode || "",
          notOpenRule: m.notOpenRule || null,
          level: m.level,
          text: m.title,
          url: m.routePath,
          type: SearchType.MENU,
          zhToPinYin: zhToPinYin,
          isOpen: m.isOpen,
          isAuth: m.isAuth,
          children: children
        };
        menus.push(searchVo);
      }
      return menus;
    }

    /**
     * @description 提取应用code
     */
    private extractAppCode(navigationList: Navigation[], appCodes) {
      for (let i = 0; i < navigationList.length; i++) {
        let navigation = navigationList[i];
        if (navigation.children && navigation.children.length > 0) {
          this.extractAppCode(navigation.children, appCodes);
        }
        let appCode = navigation.appCode || navigation.appMenuInfo?.appCode;
        if (appCode && !appCodes[appCode]) {
          appCodes[appCode] = true;
        }
      }
    }


    /**
     * @description 搜索初始化
     * @param resolverConfig
     */
    private init(resolverConfig: NavResolverType, navigationList: Navigation[]) {
      let appCodes = {};
      let navList = [...navigationList];

      navList.sort(function (a: any, b: any) {
        if (a.seq < b.seq) {
          return -1;
        } else if (a.seq > b.seq) {
          return 1;
        }
        return 0;
      });
      this.extractAppCode(navList, appCodes);
      this.resolverConfig = resolverConfig;
      const {appMenuList} = resolverConfig;
      let appMenus = {};
      for (let key in appCodes) {
        if (appMenuList[key]) {
          appMenus[key] = appMenuList[key];
        }
      }
      let menuList:Menu[] = this.scatterAppMenuList(appMenus);
      let menus:Menu[] = filterMenu(menuList, resolverConfig.roles, resolverConfig.openApp, resolverConfig.isManager);
      //解析菜单数据为搜索数据结构list
      let menuSearchVo:SearchVo[] = this.parseMenu(menus);
      //解析导航数据为搜索数据结构list
      let navSearchVo:SearchVo[] = this.parseNavigation(navList);
      let searchVoList:SearchVo[] = [];
      searchVoList.push(...navSearchVo);
      searchVoList.push(...menuSearchVo);
      this.dataSource = searchVoList;
    }

    private inputChangeEvent(e) {
      if (this.keyword) {
        let search = new Search(this.dataSource, this.zhToPinYin);
        this.resultSource = search.search(this.keyword);
      } else {
        this.resultSource = {
          accurate: [],
          probably: []
        };
      }

    }

    private created() {
      //初始化
      this.zhToPinYin = new ZhToPinYin(false);
    }

  }
</script>

<style lang="less">
  .search-box-function(@hoverBg) {

    .search-box {
      font-family: PingFangSC-Regular;
      margin-right: 24px;
      height: 64px;
      position: relative;
      &_result {
        position: absolute;
        width: 0px;
        height: 0px;
        max-height: 460px;
        overflow-y: scroll;
        overflow-x: hidden;
        top: 64px;
        right: 0;
        background: #ffffff;
        z-index: 9999;
        transition: all .3s;
        &_show {
          width: 420px;
          height: auto;
          box-sizing: border-box;
          border-radius: 0 0 8px 8px;
          padding: 16px 12px 12px 12px;
        }
      }
      &_wrapper {
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        background: #FFFFFF;
        transition: all .3s;
        border-radius: 8px 8px 0px 0px;
        &_top {
          display: flex;
          align-items: center;
          height: 64px;
        }
        &_bottom {

        }
        //搜索框部分
        &_left {
          width: 0px;
          height: 36px;
          overflow: hidden;
          background: #F8F8F8;
          box-sizing: border-box;
          border-radius: 8px;
          display: flex;
          align-items: center;
          padding: 11px 8px;
          input {
            margin-left: 8px;
            width: 100%;
            border: none;
            outline: none;
            background: none;
            font-weight: 400;
            font-size: 14px;
            color: #46485A;
            letter-spacing: 0;
          }
          &_show {
            width: 360px;
          }
        }
        .icon-search {
          cursor: pointer;
          color: #6a6f7f;
          font-size: 14px;
        }
        .icon-delete {
          cursor: pointer;
          font-size: 12px;
        }
        &_open {
          width: 420px;
          box-sizing: border-box;
          padding: 0px 0px 0px 12px;
          z-index: 9999;
          box-shadow: 0 4px 8px 4px rgba(203,206,216,0.16);
        }
        &_close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          transition: all .3s;
          /*padding-left: 14px;*/
          &_loupe {
            border-radius: 4px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all .3s;
            &:hover {
              background: @hoverBg;
            }
          }
          .icon-icon_close {
            font-size: 16px;
            cursor: pointer;
          }
        }
      }
      &_mask {
        width: 100%;
        height: 100%;
        position: fixed;
        visibility: hidden;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        transition: all .3s;
        background: rgba(0,0,0,0.06);
        z-index: 9990;
      }
      .show-mask {
        visibility: visible;

      }
    }
  }
  .default {
    .search-box-function(@hoverBg: #F7FAFD);
  }
  .red {
    .search-box-function(@hoverBg: #FBF0F1);
  }

</style>