<template>
  <van-checkbox
    class="o-checkbox"
    :value="actualValue"
    :disabled="disabled"
    @input="onInput"
    :shape="shape"
  >
    <slot />
  </van-checkbox>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class Checkbox extends Vue {
  @Prop() disabled!: boolean;
  @Prop() name!: string;
  @Prop() value!: boolean;
  @Prop({ default:"round" }) shape!: "square" | "round";

  get actualValue() {
    return this.value;
  }

  onInput(value: any) {
    this.$emit("input", value);
  }
}
</script>

<style scoped>
</style>