<template>
  <div class="app theme-kxy">
    <div class="fm-container">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts">
import { AppContext, AppContextState } from "../../model";
import { Component, Vue } from "vue-property-decorator";
@Component({})
export default class App extends Vue {
  state: AppContextState = new AppContextState();

  beforeMount() {
    let context = this.$parent.$app as AppContext;
    context.state = this.state;
    context.refreshToken();
    if (context.option.autoReload !== false) {
      context.reload();
    }
  }
}
</script>

<style lang="less">
//at.alicdn.com/t/font_3418054_ua9u1pv9jdk.css
@import "//at.alicdn.com/t/c/font_3418054_i9bfquo4nzn.css";
.app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100%;
  height: 100%;
  .fm-container {
    height: 100%;
  }
}
</style>