<template>
  <div>
    <div v-if="textModel">
      {{valueText}}
    </div>
    <el-select
      v-show="!textModel"
      :value="value"
      :multiple="multiple"
      style="width:100%"
      filterable
      remote
      clearable
      :disabled="isDisabled"
      @input="onInput"
      reserve-keyword
      :placeholder="placeholder"
      :remote-method="remoteMethod"
      :loading="loading"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules } from "../../form";
import formFieldMixins from "../../formFieldMixins";
import { FormItem } from "../type";

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
})
export default class JRemoteSearchSelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() readonly multiple!: boolean;
  @Prop() rules!: any[];
  @Prop({ default:"请输入" }) placeholder!: string;
  
  field!: FieldContext;

  valueText = "";
  loading = false
  options = []
  error = false;

  @Watch("value", {
    immediate: true,
  })
  onWatchValue() {
    this.setValueText();
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  async remoteMethod(query) {
    if (query !== '') {
      this.loading = true;
      const fn = this.formItem.remoteMethod;
      try {
        if(!fn) return 
        this.options = await fn(query)
      }finally{
        this.loading = false;
      }
    } else {
      this.options = [];
    }
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  async setValueText() {
    
  }

  get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v.length==0?null:v, this.actualRules);

    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }

    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }

    return r?.detail[0]?.message ?? "";
  }

  onFieldReset() {
    if (Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue);
    }
  }
}
</script>