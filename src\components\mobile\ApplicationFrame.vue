<template>
  <div class="approve">
    <div v-if="$app.currentHasPrivilege" class="right-menu">
      <router-view />
    </div>
    <forbidden v-if="!$app.currentHasPrivilege" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Inject } from "vue-property-decorator";

import Page403 from "./Page403.vue";

@Component({
  components: {},
  beforeCreate() {
    let Forbidden = (this as any).forbidden;
    if (Forbidden) {
      this.$options.components!.Forbidden = Forbidden;
    } else {
      this.$options.components!.Forbidden = Page403;
    }
  },
})
export default class ApplicationFrame extends Vue {
  @Inject("__app_forbidden__") readonly forbidden!: any;
}
</script>

<style scoped lang="less">
.approve {
  display: flex;
  .right-menu {
    flex: 1;
    background-color: #e7eef7;
    padding: 0 0 0 16px;
  }
}
</style>