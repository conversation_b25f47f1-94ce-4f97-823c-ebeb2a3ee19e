import Vue from "vue"
import MessageBox from "./MessageBox.vue"

interface Params  {
  title?: string
  message?:Function | string;
  cancelButton?:string;
  confirmButton?:string;
  // 是否可以通过点击遮罩层关闭弹框
  closeOnClickModal?:Boolean;
  confirm?:(close:Function)=>void,
  cancel?:(close:Function)=>void,
  // 是否启用html插入
  dangerouslyUseHTMLString?:Boolean
}

export function msgbox(param?: Params){
  const div = document.createElement("div");
  document.body.appendChild(div);
  new Vue({
    components: {
      "o-confirm": MessageBox
    },
    render(h) {
      const message = typeof param?.message === "function" ? param?.message(h) : param?.message

      return <o-confirm close={async (v: boolean,close) => {
        // 是否传入确定按钮回调
        if(v && param?.confirm) {
          await  param.confirm(close)
          // 是否穿去取消按钮点击回调
        }else if(!v && param?.cancel){
          await param.cancel(close)
        }else{
          // 如果没有做任何处理 ， 点击按钮直接关闭弹框即可
          close()
        }
      }} 
      title={param?.title} 
      cancelButton={param?.cancelButton} 
      confirmButton={param?.confirmButton}  
      closeOnClickModal={param?.closeOnClickModal}  
      >
        { 
          param?.dangerouslyUseHTMLString?
            <div domPropsInnerHTML={message} /> : message
        }
      </o-confirm>
    }
  }).$mount(div);
}

Vue.prototype.$oAlert = (params)=>{
  params.cancelButton = ""
  return msgbox(params)
}

Vue.prototype.$oConfirm = msgbox