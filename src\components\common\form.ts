import dayjs from "dayjs";
import Vue from "vue";


export const FORM_PROVIDER = "__o_form_context__"
export const FORM_FIELD_PROVIDER = "__o_form_field_context__"

export type ValidateTrigger = "realtime" | "blur" | "prevent" | "submit";

export const toArray = <T>(item: T | T[]): T[] =>
  Array.isArray(item) ? item : [item];

// 校验非法字符串
const isIllegalCharacter = (value) => {
  const reg =
    /[^\w !#$%&'()*,./:;<=>?@[\\\]^`{|}~—‘“”…、。【】\u4E00-\u9FA5！（），：；？￥\-]/g
  return !reg.test(String(value))
}


export interface Rule {
  type?: "text" | "int" | "number" | "email" | "cellphone" | "idno" | "date" | "datetime" | "illegalCharacter" | "mobile"
  required?: boolean
  max?: number | Date
  min?: number | Date
  // 小数点后保留多少位
  scale?: number
  regex?: string
  message?: string
  // trigger: ValidateTrigger;
  validator?: (v: any) => Promise<string | undefined>
}

export interface ValidateResult {
  ok: boolean
  detail: {
    rule: Rule
    message: string
  }[]
}

const validator = {
  int: (s: any) => (String(s ?? "")).match(/^-?\d+$/) != null,
  number: (s: any) => (String(s ?? "")).match(/^-?\d+(\.\d+)?$/) != null,
  email: (s: any) => (String(s ?? "")).match(/^[a-zA-Z0-9.!#$%&'*+/=? ^_`{|}~-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)+$/) != null,
  cellphone: (s: any) => (String(s ?? "")).match(/^1\d{10}$/) != null,
  mobile: (s: any) => (String(s ?? "")).match(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/) != null,
  idno: (s: any) => (String(s ?? "")).match(/^\d{17}[0-9xX]$/) != null,
  illegalCharacter: (s: any) => isIllegalCharacter(s)
}

async function validate(s: any, rule: Rule) {
  if (rule.validator) {
    return await rule.validator(s)
  }
  if (rule.required && (s == null || s === "")) {
    return rule.message
  }
  if (s == null || s === "") {
    return
  }
  if (rule.type && validator[rule.type] && !validator[rule.type](s)) {
    return rule.message
  }
  if (rule.type == "text") {
    let v = s?.toString() ?? ""
    if (rule.max != null && v.length > rule.max) {
      return rule.message
    }
    if (rule.min != null && v.length < rule.min) {
      return rule.message
    }
    if (rule.regex && v.match(new RegExp(rule.regex)) == null) {
      return rule.message
    }
  } else if ((rule.type == "number" || rule.type == "int")) {
    let v = parseFloat(s);
    if (rule.max != null && v > rule.max) {
      return rule.message
    }
    if (rule.min != null && v < rule.min) {
      return rule.message
    }
    if (rule.scale != null && rule.scale >= 0) {
      let v = s.toString();
      let i = v.indexOf(".");
      if (i >= 0 && v.length - i - 1 > rule.scale) {
        return rule.message
      }
    }
  } else if (rule.type == "date" || rule.type == "datetime") {
    const v = dayjs(s)
    if (rule.max != null && dayjs(rule.max).isBefore(v)) {
      return rule.message
    }
    if (rule.min != null && dayjs(rule.min).isAfter(v)) {
      return rule.message
    }
  }
  if (rule.regex) {
    let v: string = s?.toString() ?? ""
    if (v.match(new RegExp(rule.regex)) == null) {
      return rule.message
    }
  }
}

export async function validateRules(s: any, rules: Rule[]): Promise<ValidateResult> {
  let list = (rules ?? []).map(rule => {
    let o = {
      promise: validate(s, rule).then(msg => msg),
      rule,
      message: "" as string | undefined
    }
    return o
  })
  await Promise.all(list.map(o => o.promise.then(msg => o.message = msg)))
  let failed = list.filter(o => o.message != null).map(o => ({
    rule: o.rule,
    message: o.message ?? ""
  }))

  return {
    ok: failed.length == 0,
    detail: failed
  }
}


export interface FormField {
  component: any
}


export class FieldContext {
  field: any
  component?: any
  errorMessage: string = ""

  get name(): string | undefined {
    return this.field?.$options.propsData.name
  }

  get required(): boolean {
    return this.rules.find(o => o.required) != null
  }

  get requiredRule(): Rule | undefined {
    return this.rules.find(o => o.required)
  }

  get isValid() {
    if (!this.field) {
      return true
    }
    let msg = this.field?.actualErrorMessage
    return msg == null || msg === ""
  }

  get value() {
    return this.component?.$options.propsData.value
  }

  resetField(){
    const defaultValue = this?.component?.$attrs.defaultValue
    this?.component?.onInput && this.component.onInput("")
    this?.component?.onFieldReset && this.component.onFieldReset()
    defaultValue && this?.component?.onInput && this.component.onInput(defaultValue)
  }

  async verify(s: string) {
    let list = (this.rules ?? []).map(rule => {
      let o = {
        promise: validate(s, rule).then(msg => msg),
        rule,
        message: "" as string | undefined
      }
      return o
    })
    await Promise.all(list.map(o => o.promise.then(msg => o.message = msg)))
    let failed = list.filter(o => o.message != null).map(o => ({
      rule: o.rule,
      message: o.message
    }))
    return {
      ok: failed.length == 0,
      detail: failed
    }
  }
  
  get rules(): Rule[] {
    return this.field?.$props.rules ?? []
  }

  constructor(field) {
    this.field = field
  }

  clear() {
    this.component = null
  }

  scrollTo() {
    this.field.el.scrollIntoView({
      behavior: "smooth",
    });
  }
}

export class FormContext {

  props: any = {}
  labelPosition: string = "left"
  labelWidth: string = "90px"
  fields: FieldContext[] = []

  component: any = null

  get values(): { [key: string]: any } {
    let v = {} as any
    this.fields.forEach(o => {
      if (o.name) {
        v[o.name] = o.value
      }
    })
    return v
  }

  addField(context: FieldContext) {
    if (this.fields.indexOf(context) >= 0) {
      return
    }
    this.fields.push(context)
  }

  removeField(field: FieldContext) {
    let i = this.fields.indexOf(field)
    if (i >= 0) {
      this.fields.splice(i, 1)
    }
  }

  async validate() {
    let result = this.fields.map(field => {
      if (field.component && field.component.validate) {
        let r = {
          ok: false,
          promise: field.component.validate().then(v => {
            if (typeof v == "string" && v.length >= 1) {
              r.ok = false
              field.errorMessage = v
            } else {
              r.ok = true
              field.errorMessage = ""
            }
          })
        }
        return r
      }
    }).filter(o => o)
    await Promise.all(result.map(o => o?.promise))
    return result.map(o => o!.ok).reduce((a, b) => a && b, true)
  }

  scrollToError() {
    let field = this.fields.find(o => !o.isValid)
    if (field) {
      field.scrollTo()
    }
  }

  handleHintClick(field: FieldContext) {
    if (field.name) {
      this.component.$emit("hint", field.name)
    }
  }

  clearFieldsErrorMessage (){
    for(let field of this.fields){
      field.errorMessage = ""
    }
  }

  resetFields(){
    for(let field of this.fields){
      field.errorMessage = ""
      field.resetField()
    }
  }
}

export function registerField(v: Vue) {
  let field
  let parent: any = v.$parent
  if (parent == null) {
    return null
  }

  // 如果_componentTag是null，则可能是遇到了Vue内置的<component />
  while (parent && parent.$options._componentTag === undefined) {
    parent = parent.$parent
  }
  if (parent?.$options?.name === "o-field") {
    if (!parent.field || parent.field.component) {
      throw new Error("registerField failed");
    }
    field = parent.field
    field.component = v;
  }

  return field
}