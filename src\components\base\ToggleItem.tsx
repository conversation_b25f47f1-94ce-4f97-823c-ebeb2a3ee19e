
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class extends Vue {

  @Prop() value!: any;

  state = {
  };

  toggle: any = null
  active: any = null

  beforeMount() {
    const parent = this.$parent
    if (typeof parent["addToggleItem"] == "function") {
      const { toggle, active } = (this.$parent as any).addToggleItem(this.value)
      this.toggle = toggle
      this.active = active
    }
  }

  render() {
    const v = this.$scopedSlots.default!({
      active: this.active(),
      toggle: () => this.toggle()
    })
    if (Array.isArray(v)) {
      return v[0]
    }
    return v
  }
}

