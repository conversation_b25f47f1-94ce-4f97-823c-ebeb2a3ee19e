/**
 * 思路：
 * 表格距离顶部的距离
 * 设置表格距离顶部多少就吸顶---offsetTop1
 * 获取滚动条滚动的距离
 * 当滚动条滚动 offsetTop1 后，表格就自动吸顶
 */
const tableStickyObj = {}

const __STICKY_TABLE = {
  // 给固定头设置样式
  doFix(dom, top, data) {
    const { uid, domType, isExist } = data
    const uObj = tableStickyObj[uid]
    const curObj = uObj[domType]
    const headerRect = tableStickyObj[uid].headerRect

    if (!isExist) {
      dom.style.position = 'fixed'
      dom.style.zIndex = '1900'
      dom.style.top = top + 'px'
    }
    uObj.tableWrapDom.style.marginTop = headerRect.height + 'px'

    if (domType === 'fixed') {
      dom.style.left = curObj.left + 'px'
    } else if (domType === 'fixedRight') {
      dom.style.left = curObj.left + 1 + 'px'
    }
  },
  // 给固定头取消样式
  removeFix(dom, data) {
    const { uid, domType } = data
    // dom.parentNode.style.paddingTop = 0
    const uObj = tableStickyObj[uid]
    const curObj = uObj[domType]
    dom.style.position = 'static'
    dom.style.top = '0'
    dom.style.zIndex = '0'

    uObj.tableWrapDom.style.marginTop = '0'

    if (domType === 'fixed') {
      curObj.dom.style.top = '0'
    } else if (domType === 'fixedRight') {
      curObj.dom.style.top = '0'
    }
  },
  // 给固定头添加class
  addClass(dom, fixtop, data) {
    fixtop = fixtop || 0
    const isExist = dom.classList.contains('fixed')
    data.isExist = !!isExist
    if (!isExist) { // 若有，就不再添加
      dom.classList.add('fixed')
    }
    this.doFix(dom, fixtop, data)
  },
  // 给固定头移除class
  removeClass(dom, data) {
    if (dom.classList.contains('fixed')) {
      dom.classList.remove('fixed')
      this.removeFix(dom, data)
    }
  },

  /**
   * 计算某元素距离相对父元素的top距离
   * @param {Nodes} e 某元素
   * @param {String} domId 父元素id
   * @param {Boolean} isParent 是否父元素
   * @returns {Number}
   */
  getPosY(el, domId) {
    let offset = 0
    const pDom = el.offsetParent
    if (pDom != null && '#' + el.id !== domId) {
      offset = el.offsetTop
      offset += this.getPosY(pDom, domId)
    }
    return offset
  },

  // 获取元素的横坐标（相对于窗口）
  getPosX(e) {
    var offset = e.offsetLeft
    if (e.offsetParent != null) offset += this.getPosX(e.offsetParent)
    return offset
  },
  fixHead(scrollDom, el, uid, binding) {
    this.fixHead1(this, { scrollDom, el, uid, binding })
  },
  // 具体判断是否固定头的主函数
  fixHead1: stickyThrottle((_this, { scrollDom, el, uid, binding }) => {
    const top = binding.value.top
    /**
     * myTop 当前元素距离滚动父容器的高度，
     * fixtop 当前元素需要设置的绝对定位的高度
     * parentHeight 滚动父容器的高度
     */

    const tableSticky = tableStickyObj[uid]
    
    // 表头DOM节点
    const headerWrapDom = el.children[1]

    scrollDom = scrollDom === document ? scrollDom.documentElement : scrollDom

    const { actionButton } = binding.value

    const oTableEl = el.parentNode
    let oTableOffsetTop = oTableEl.offsetTop 
    const scrollTop = scrollDom.scrollTop || scrollDom.scrollTop
    
    // 表格顶部距离滑动区域的距离
    const oTableHeaderEl = oTableEl.querySelector(".o-table-header")

     // 如果当前元素是 定位，获取元素距离顶部高度会为0 ， 这个时候 还使用上次缓存的 距离顶部元素的距离即可
     if(oTableHeaderEl?.style.position=="fixed") {
      oTableOffsetTop = tableSticky.oTableOffsetTop 
    }else{
      tableSticky.oTableOffsetTop = oTableOffsetTop
    }
    
    
    const setTableHeaderTitle = tableSticky.setTableHeaderTitle = (fix=true)=>{
      const fixedHeadDom = tableSticky.fixed.headerDom
      const fixedHeadRightDom = tableSticky.fixedRight.headerDom
      if(fix) {
        const fixtop = top + scrollDom.getBoundingClientRect().top
        // 如果表头滚动到 父容器顶部了。fixed定位
        _this.addClass(headerWrapDom, fixtop, { domType: 'mainBody', uid })
        fixedHeadDom && _this.addClass(fixedHeadDom, fixtop, { domType: 'fixed', uid })
        fixedHeadRightDom && _this.addClass(fixedHeadRightDom, fixtop, { domType: 'fixedRight', uid })
      }else{
        // 如果表格向上滚动 又滚动到父容器里。取消fixed定位
        _this.removeClass(headerWrapDom, { domType: 'mainBody', uid })
        fixedHeadDom && _this.removeClass(fixedHeadDom, { domType: 'fixed', uid })
        fixedHeadRightDom && _this.removeClass(fixedHeadRightDom, { domType: 'fixedRight', uid })
      }
    }

    // 设置表头当前状态是否吸顶
    const setIsTableHeaderSticky = (fix=true)=>  {
      tableStickyObj[uid].isSticky = fix
      tableStickyObj[uid].oTable.isTableHeaderSticky = fix ;
      if(!el.parentNode) return 
    }

    if(!tableSticky.isOnTableHeaderStick) {
      tableSticky.isOnTableHeaderStick = true
      tableSticky.oTable.$on("setIsTableHeaderSticky",setIsTableHeaderSticky)
    }

    // 如果 操作按钮要跟着 也设置了一期滚动
    if(actionButton) {
      oTableOffsetTop = oTableOffsetTop + tableSticky.offset
      // oTable记录 吸顶位置
      tableStickyObj[uid].oTable.oTableOffsetTop = oTableOffsetTop

      if(scrollTop>oTableOffsetTop) {
        setTableHeaderActionFix(tableSticky)
        setTableHeaderTitle()
        setIsTableHeaderSticky()
      }else {
        setTableHeaderActionFix(tableSticky,false)
        setTableHeaderTitle(false)
        setIsTableHeaderSticky(false)
      }
      return
    }

    // 操作按钮不跟着 一起滚动的话 ， 需要加上 操作按钮的高度 ， 避免 提前 吸顶
    const oTableHeaderElHeight = oTableHeaderEl ? oTableHeaderEl.clientHeight : 0
    oTableOffsetTop = oTableOffsetTop + oTableHeaderElHeight + tableSticky.offset
    tableStickyObj[uid].oTable.oTableOffsetTop = oTableOffsetTop

    if (scrollTop >= oTableOffsetTop) {
      setIsTableHeaderSticky()
      setTableHeaderTitle()
    } else {
      setIsTableHeaderSticky(false)
      setTableHeaderTitle(false)
    }
  }, 30, { eventType: 'fixHead111' }),
  setHeadWidth(data) {
    this.setHeadWidth1(this, data)
  },
  // 设置头部固定时表头外容器的宽度写死为表格body的宽度
  setHeadWidth1: debounce((_this, data) => {
    const { el, uid, binding, eventType } = data
    if (!tableStickyObj[uid]) return false
    const tableSticky = tableStickyObj[uid]
    
    try {
      if(tableSticky.scrollDom.scrollTop<=tableSticky.oTable.oTableOffsetTop) {
        tableSticky.setTableHeaderFixFn && tableSticky.setTableHeaderFixFn(false)
      }
    }catch(err){
      console.error(err)
    }

    // 如果不是吸顶状态
    // if(!tableStickyObj[uid].isSticky) return false

    const { scrollDom } = tableStickyObj[uid]
    const headerWrapDom = el.children[1] 
    // 如果表头当前就是 浮动状态 不重新执行计算表头距离顶部位置
    if (headerWrapDom.className.includes("fixed")) return false
    const headerH = headerWrapDom.offsetHeight
    const distTop = _this.getPosY(headerWrapDom, binding.value.parent)
    // 滚动条距离顶部的距离
    const scrollDistTop = _this.getPosY(scrollDom) 
    // console.log({scrollDistTop,headerH,distTop})

    // 表头距离顶部的距离 - 表头自身高度 - 滚动条距离顶部的距离
    tableStickyObj[uid].headerRect.top = distTop + headerH - scrollDistTop / 3 
    tableStickyObj[uid].headerRect.height = headerH

    tableStickyObj[uid].initFixedWrap = (reload=false)=> {
      _this.initFixedWrap({ el, uid, eventType, key: 'fixed', className: 'el-table__fixed', className1: 'el-table__fixed-header-wrapper' },reload)
      _this.initFixedWrap({ el, uid, eventType, key: 'fixedRight', className: 'el-table__fixed-right', className1: 'el-table__fixed-header-wrapper' },reload)
    }
    
    tableStickyObj[uid].initFixedWrap()

    // 获取到当前表格个表格body的宽度
    const bodyWrapperDom = el.getElementsByClassName('el-table__body-wrapper')[0]
    const width = getComputedStyle(bodyWrapperDom).width
    // 给表格设置宽度。这里默认一个页面中的多个表格宽度是一样的。所以直接遍历赋值，也可以根据自己需求，单独设置
    const tableParent = el.getElementsByClassName('el-table__header-wrapper')
    for (let i = 0; i < tableParent.length; i++) {
      // 如果不是吸顶状态 ， 宽度设置为 "auto"
      tableParent[i].style.width = data.el.__vue__.tableData.length ? width : "auto"
    }
    // debugger
    _this.fixHead(scrollDom, el, uid, binding) // 判断顶部是否已吸顶的一个过程
  }),
  

  initFixedWrap(data , reload = false) {
    const { key, el, eventType, className, className1, uid } = data
    const tableFixedDom = el.getElementsByClassName(className)

    
    // 确保每次刷新，只获取一次
    if (!tableStickyObj[uid][key].dom || reload) {
        if (tableFixedDom.length) {

        const fixedDom = tableFixedDom[0]
        const arr = fixedDom.getElementsByClassName(className1) 
        const headW = getComputedStyle(fixedDom).width

        tableStickyObj[uid][key].dom = fixedDom
          if (arr.length) {
          const distLeft = this.getPosX(fixedDom) // 距离窗口左侧的距离
          const headDom = arr[0]

          headDom.style.width = headW

          tableStickyObj[uid][key].left = distLeft // 距离窗口左边像素

          tableStickyObj[uid].updateTableHeaderRightFixed = ()=>{
            // 距离窗口左边像素
            tableStickyObj[uid][key].left = distLeft 
            headDom.style.overflow = 'auto'
            headDom.scrollLeft = headDom.scrollWidth
             // 设置了滚动
            headDom.style.overflow = 'hidden'
          }

          if (key === 'fixedRight') { // right-fixed 的特别之处
            tableStickyObj[uid].updateTableHeaderRightFixed()
          } else {
            headDom.style.overflow = 'hidden'
          }

          tableStickyObj[uid][key].headerDom = headDom // 取第一个
        }
      }      
    }
  },
}

const $ = {
  style (el,style){
    Object.keys(style).forEach(item=>{
      el.style[item] = style[item]
    })
  }
}

const getTableHeaderFixCss = (tableStickyObj)=>{


  const oTableHeaderEl = tableStickyObj.tableHeaderDom
  const scrollDomRect = tableStickyObj.scrollDomRect
  const oTableHeaderElRect = oTableHeaderEl.getBoundingClientRect()

  const css = {
    position:"fixed",
    zIndex:9,
    left:`${oTableHeaderElRect.left}px`,
    top:`${scrollDomRect.top }px`,
    right:`calc(100vw - ${oTableHeaderElRect.right}px)`,
  }
  return css
}

const setTableHeaderActionFix = (tableStickyObj,fix=true)=>{
  const { tableHeaderDom,tableHeaderDomRect,el } = tableStickyObj

  if(fix){
    const css = getTableHeaderFixCss(tableStickyObj)
    $.style(tableHeaderDom,css)
    $.style(el,{
      marginTop:`${tableHeaderDomRect.height }px`
    })
  }else{
    tableHeaderDom.style.position = "initial"
    el.style.marginTop = `0`
  }
}

/**
 * 节流函数: 指定时间间隔内只会执行一次任务
 * @param {function} fn
 * @param {Number} interval
 */
function stickyThrottle(fn, interval = 30) {
  let canRun = true
  return function () {
    if (!canRun) return
    canRun = false
    setTimeout(() => {
      fn.apply(this, arguments)
      canRun = true
    }, interval)
  }
}

// 防抖
export function debounce(fn, delay) {
  const _delay = delay || 200
  return function () {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this // 该this指向实例
    const args = arguments
    if (fn.timer) {
      clearTimeout(fn.timer)
      fn.timer = null
    }
    fn.timer = setTimeout(function () {
      fn.timer = null
      fn.apply(that, args)
    }, _delay)
  }
}

const unbind = (el, binding, vnode) => {
  const uid = vnode.componentInstance._uid
  if (!binding.value) return
  window.removeEventListener('resize', tableStickyObj[uid].setWidthFunObj)
  const scrollDom = document.querySelector(binding.value.parent) || document
  scrollDom.removeEventListener('scroll', tableStickyObj[uid].fixFunObj)
  if (binding.value.parent) {
    document.removeEventListener('scroll', tableStickyObj[uid].autoMoveFunObj)
  }
  tableStickyObj[uid].onMenuCollapse.stop()
}

const init =async (el, binding, vnode) => {

  await vnode.componentInstance.$nextTick()
  
  // 获取当前vueComponent的ID。作为存放各种监听事件的key
  const uid = vnode.componentInstance._uid
  const value = binding.value
  if (!value) return unbind(el, binding, vnode)

  let parentEl = binding.value.parent

  // 获取当前滚动的容器是什么。如果是document滚动。则可默认不传入parent参数
  let scrollDom = document.querySelector(parentEl) || document 
  
  // 如果被o-container 组件嵌套，并且没有提供 parent 名称，直接获取 o-container 滚动元素
  if(vnode.componentInstance.$parent.oConCationContext && !parentEl) {
    scrollDom = el.parentNode 
    while (!scrollDom.className.includes("o-container-main")) {
      scrollDom = scrollDom.parentNode
    }
  }

  scrollDom.style.position = "relative"

  if (!tableStickyObj[uid]) {

    tableStickyObj[uid] = {
      uid,
      isEventScroll:false,
      offset: binding.value.offset || 0,
      fixFunObj: {}, // 用于存放滚动容器的监听scroll事件
      setWidthFunObj: {}, // 用于存放页面resize后重新计算head宽度事件
      autoMoveFunObj: {}, // 用户存放如果是DOM元素内局部滚动时，document滚动时，fix布局的表头也需要跟着document一起向上滚动
      scrollDomRect: scrollDom.getBoundingClientRect(),
      headerRect: { top: 0, left: 0 },
      fixed: {}, // 表格左浮动
      oTable:vnode.componentInstance.$parent,
      fixedRight: {}, // 表格右浮动
      isOnTableHeaderStick:false,
      tableWrapDom: el.getElementsByClassName('el-table__body-wrapper')[0],
      tableHeaderDom:el.parentNode.querySelector(".o-table-header"),
      tableHeaderDomRect:el.parentNode.querySelector(".o-table-header")?.getBoundingClientRect(),
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      updateTableHeaderRightFixed:()=>{},
      // 是否吸顶
      isSticky:false,
      el,
      scrollDom,
      onMenuCollapse: {
        on:null,
        scrollTop(isCollapse){
          const tableSticky = tableStickyObj[uid]
          setTableHeaderActionFix(tableSticky,false)
          if(!tableSticky.setTableHeaderTitle) return
          tableSticky.setTableHeaderTitle(false)
          scrollDom.scrollTo(0,0)
          setTimeout(() => {
            window.dispatchEvent(new Event('resize')) 
            tableSticky.updateTableHeaderRightFixed()
            tableStickyObj[uid]["fixed"].left = __STICKY_TABLE.getPosX(el)
          } , 350);
        },
        start(){
          if(this.on) return 
          const tableSticky = tableStickyObj[uid]
          vnode.componentInstance.$customEventBus && vnode.componentInstance.$customEventBus.$on("menu-collapse",this.scrollTop)
          tableSticky.oTable.$on("updateTableHeaderRightFixed",()=>{
            window.dispatchEvent(new Event('resize'))
            tableSticky.updateTableHeaderRightFixed()
          })
          this.on = true
        },
        stop(){
          if(!this.on) return
          vnode.componentInstance.$customEventBus && vnode.componentInstance.$customEventBus.$off("menu-collapse",this.scrollTop)
        }
      }
    }


    tableStickyObj[uid].onMenuCollapse.start()

   const updateStickyTableHeader = ()=> {
      const fixed = tableStickyObj[uid].oTable.isTableHeaderSticky
      tableStickyObj[uid].initFixedWrap && tableStickyObj[uid].initFixedWrap(true)
      tableStickyObj[uid].setTableHeaderTitle && tableStickyObj[uid].setTableHeaderTitle(fixed)
    }

    tableStickyObj[uid].oTable.$watch("showActionButtonColumn",(newValue)=>{
      __STICKY_TABLE.initFixedWrap({ el, uid, eventType:"", key: 'fixedRight', className: 'el-table__fixed-right', className1: 'el-table__fixed-header-wrapper' },"reload")
    })

    tableStickyObj[uid].oTable.$watch("context.store.oTableLoading",(newValue)=>{
      if(!newValue) {
        tableStickyObj[uid].updateTableHeaderRightFixed()
        updateStickyTableHeader()
      }
    })

    tableStickyObj[uid].oTable.$watch("actionButtonColumnWidth",(newValue)=>{
      if(newValue === 1 ) return 
      setTimeout(updateStickyTableHeader ,1000);
      setTimeout(updateStickyTableHeader ,500);
    })
  }

  const resizeFn = (tableStickyObj[uid].setWidthFunObj = () => {
      __STICKY_TABLE.initFixedWrap({ el, uid, eventType:"", key: 'fixedRight', className: 'el-table__fixed-right', className1: 'el-table__fixed-header-wrapper' },"reload")
  })


  const scrollFn =  (tableStickyObj[uid].fixFunObj = () => {
    __STICKY_TABLE.fixHead(scrollDom, el, uid, binding)
  })

  if(!tableStickyObj[uid].isEventScroll) {
    scrollDom.removeEventListener("scroll",scrollFn)

    // 给滚动容器加scroll监听事件。并将监听函数存入 监听函数对象中，方便移除监听事件
    scrollDom.addEventListener('scroll',scrollFn)

    // 当window resize时 重新计算设置表头宽度，并将监听函数存入 监听函数对象中，方便移除监听事件
    window.addEventListener('resize', resizeFn)

    tableStickyObj[uid].oTable.$on("addEventContainerScroll", ()=>scrollDom.addEventListener("scroll",scrollFn))

    tableStickyObj[uid].oTable.$on("removeEventContainerScroll", ()=>scrollDom.removeEventListener("scroll",scrollFn))

    tableStickyObj[uid].isEventScroll = true

  }


}

export const sticky = {
  // 当被绑定的元素插入到 DOM 中时……
  inserted: init,
  update: init,
  // component 更新后。重新计算表头宽度
  componentUpdated(el, binding, vnode) {
    const uid = vnode.componentInstance._uid
    // console.log("update")
    if (!binding.value) return
    __STICKY_TABLE.setHeadWidth({ el, uid, binding, eventType: 'componentUpdated' })
  },
  // 节点取消绑定时 移除各项监听事件。
  unbind
}