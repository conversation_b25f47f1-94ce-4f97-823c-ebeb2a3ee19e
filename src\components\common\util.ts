import _ from "lodash"

export function asArray<T = any>(value: any): T[] {
  if (Array.isArray(value)) {
    return value
  }
  return value ? [value] : []
}

export function isSafariBrowser() {
  return /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
}

const resizeHandler = function(entries) {
  for (let entry of entries) {
    const listeners = entry.target.__resizeListeners__ || [];
    if (listeners.length) {
      listeners.forEach(fn => {
        fn();
      });
    }
  }
}

/**
 * @description: 监听窗口大小发生变化
 * @param { Element} element 监听变化的DOM元素
 * @param { Function } fn 触发的回调
 */
export function addResizeListener (element,fn:Function,wait=30){
  if (!element.__resizeListeners__) {
    element.__resizeListeners__ = []
    element.__ro__ = new ResizeObserver(_.throttle(resizeHandler,wait))
    element.__ro__.observe(element)
  }
  element.__resizeListeners__.push(fn)
}

/**
 * @description: 注销监听窗口发生变化
 * @param { Element} element 监听变化的DOM元素
 * @param { Function } fn 触发的回调
 */
export const removeResizeListener = function(element, fn) {
  if (!element || !element.__resizeListeners__) return
  element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1)
  if (!element.__resizeListeners__.length) {
    element.__ro__.disconnect()
  }
}

/**
 * @description: 监听元素滚动条发生变化
 * @param { Element} element 要监测的DOM元素
 * @param { Function } fn 触发的回调
 */
export function addScrollListener (element,fn,wait=30){
  if (!element.__scrollListeners__) {
    element.__scrollListeners__ = []
  }
  // 防止重复绑定
  if( element.__scrollListeners__.some(item=>item.fn === fn)) return 
  const throttleFn =  _.throttle(fn,wait)
  element.__scrollListeners__.push({
    throttleFn,
    fn
  })
  element.addEventListener('scroll',throttleFn, { passive: true });
}

/**
 * @description: 注销监听元素滚动条发生变化
 * @param { Element} element 监听变化的DOM元素
 * @param { Function } fn 触发的回调
 */
export const removeScrollListener = function(element,fn?:Function) {
  if (!element || !element.__scrollListenersFn__) return
  for(let item of element.__scrollListenersFn__) {
    if(item.fn === fn)  element.removeEventListener("scroll",item.throttleFn,{ passive:true })
  }
}

/**
 * @description: 数值转PX，兼容传入 number 或者 string 类型
 * @param { number|number } value 要转换的数值 或者字符串 
 * @return { string } px字符串
 */
export const parse2px = (value: number | string)=>{
  const valueIsNumber = _.isNumber(value)
  const valueIsString = _.isString(value)
  if (!valueIsNumber && !value) return ""
  if(!valueIsString && !valueIsNumber) throw new Error("parse2px value 数据格式有问题")
  if(valueIsString && (value.endsWith("%") || value.endsWith("vh"))) return value
  if (valueIsNumber || !value.includes("px")) return `${value}px`
  return value
}

function getError(action, option, xhr) {
  let msg;
  if (xhr.response) {
    msg = `${xhr.response.error || xhr.response}`;
  } else if (xhr.responseText) {
    msg = `${xhr.responseText}`;
  } else {
    msg = `fail to post ${action} ${xhr.status}`;
  }

  const err = new Error(msg) as any;
  err.status = xhr.status;
  err.method = 'post';
  err.url = action;
  return err;
}

function getBody(xhr) {
  const text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }

  try {
    return JSON.parse(text);
  } catch (e) {
    return text;
  }
}

export function upload(option) {
  if (typeof XMLHttpRequest === 'undefined') {
    return;
  }

  const xhr = new XMLHttpRequest();
  const action = option.action;

  if (xhr.upload) {
    xhr.upload.onprogress = function progress(e) {
      if (e.total > 0) {
        (e as any).percent = e.loaded / e.total * 100;
      }
      option.onProgress(e);
    };
  }

  const formData = new FormData();

  if (option.data) {
    Object.keys(option.data).forEach(key => {
      formData.append(key, option.data[key]);
    });
  }

  formData.append(option.filename, option.file, option.file.name);

  xhr.onerror = function error(e) {
    option.onError(e);
  };

  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      return option.onError(getError(action, option, xhr));
    }

    option.onSuccess(getBody(xhr));
  };

  xhr.open('post', action, true);

  if (option.withCredentials && 'withCredentials' in xhr) {
    xhr.withCredentials = true;
  }

  const headers = option.headers || {};

  for (let item in headers) {
    if (headers.hasOwnProperty(item) && headers[item] !== null) {
      xhr.setRequestHeader(item, headers[item]);
    }
  }
  xhr.send(formData);
  return xhr;
}


export const traverseTree = (tree,cb)=> {
  const result = [] as any;
  function traverse(node) {
    cb && cb(node)
    result.push(node);
    if (node.children && node.children.length > 0) {
      // 如果有子节点，递归遍历每个子节点
      node.children.forEach(traverse);
    }
  }
  tree.forEach(traverse)
  return result;
}

export const menuTreeToPathMap = (tree)=>{
  const map ={}
  traverseTree(tree,node=>{
    if(!node.routePath) return 
    map[node.routePath] = node
  })
  return map 
}

export function ensureLeadingSlash(str) {
  if (str.charAt(0) === '/') {
    return str; 
  } else {
    return '/' + str; 
  }
}
