<template>
  <div>
    <div v-if="textModel">
      {{valueText}}
    </div>
    <div v-else>
      <el-select
        v-if="!mobile"
        size="medium"
        style="width: 100%"
        :value="actualValue"
        :loading="loading"
        :disabled="isDisabled || isReadonly"
        :multiple="multiple"
        filterable
        :clearable="clearable"
        :placeholder="placeholder"
        @input="$emit('input', $event)"
      >
        <el-option
          v-for="item in actualOptions"
          :key="item.value"
          :disabled="item.disabled"
          :label="item[labelProp]"
          :value="item[valueProp]"
        >
        </el-option>
      </el-select>
      <input
        v-if="mobile"
        @click="onClick"
        readonly
        style="width: 100%; border: none"
        :placeholder="placeholder"
        :value="text"
      />
      <o-popup
        v-if="mobile"
        :value="actualShow"
        :title="title"
        @input="onPopupInput"
        mobileHeight="70%"
      >
        <tree-view
          :loader="loader"
          :value="treeViewValue"
          :limit="100000"
          ref="tree-view"
          :hideNavBar="true"
          rootTitle="选择"
          @input="onInput"
          style="height: 100%"
        >
          <template v-slot:default="r">
            <div>
              {{ r.item.name }}
            </div>
          </template>
        </tree-view>
      </o-popup>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import Popup from "../Popup.vue";
import TreeView from "../TreeView.vue";
import formFieldMixins from "../formFieldMixins";
import { FieldContext, registerField, validateRules, ValidateTrigger } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";
import { request } from "../../../util";
import { asArray } from "../util";
import _ from "lodash"

@Component({
  components: {
    Popup,
    TreeView,
  },
  inheritAttrs:false,
  mixins:[formFieldMixins]
})
export default class  SelectField extends Vue {
  @Prop() value!: any;
  @Prop() placeholder!: string;
  @Prop() readonly!: any;
  @Prop() options!: any;
  @Prop() multiple: any;
  @Prop() title!: string;
  @Prop({default:false}) clearable!: boolean;
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;
  
  /**控制是否弹出内容选择框 */
  @Prop() popup!: boolean;
  @Prop({ default: "text" }) labelProp!: any;
  @Prop({ default: "value" }) valueProp!: any;

  show = false;
  loader: any = null;
  cachedOptions: any[] | null = null;
  loading = false;
  error = false;

  field!: FieldContext;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  clearCache() {
    this.cachedOptions = null
  }

  @Watch("actualShow")
  onActualShowWatch(value){
    if(!value) return 
    (this.$refs["tree-view"] as any).refresh()
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  get valueText(){
    const isNullValue = value => value === "" || value === undefined || value === null

    // 如果为 null 或者 undefined  返回空字符串
    if(isNullValue(this.value) ) return "-"

    const emptyText = (id)=> isNullValue(id) ? `(${id})已删除` : "-"

    // 如果 option 选项没有数据 并且 value 有值
    if(!this.actualOptions.length && this.value) return emptyText(this.value)
    
    const valueMap = this.actualOptions.reduce((acc,item)=>{
      acc[item[this.valueProp]] = item[this.labelProp] || item.label
      return acc
    },{})

    const getValueText = (id)=> valueMap[id] || emptyText(id)

    if(Array.isArray(this.value)) {
      return this.value.map(id=>getValueText(id)).join(",")
    }else{
      return getValueText(this.value)
    }
    
  }
  

  protected get actualValue() {
    if (this.multiple || this.multiple === "") {
      return asArray(this.value)
    } else {
      return this.value
    }
  }

  protected get actualOptions() {
    if (this.cachedOptions) {
      return this.cachedOptions;
    }
    if (Array.isArray(this.options)) {
      return this.options;
    }
    return [];
  }

  protected get actualShow() {
    return this.show;
  }

  protected get mobile() {
    return this.context.isMobile;
  }

  protected get isMultiple() {
    if (this.multiple || this.multiple === "") {
      return true;
    } else {
      return false;
    }
  }

  protected get treeViewValue() {
    if (this.isMultiple) {
      return this.value;
    } else {
      return this.value ? [this.value] : [];
    }
  }

  protected get text() {
    let value: any[];
    if (this.isMultiple) {
      value = this.value ?? [];
    } else {
      value = this.value ? [this.value] : [];
    }

    let s = value.map(
      (o) => this.actualOptions.find((p) => p.value == o)?.text
    );
    return s.join(", ");
  }

  beforeMount() {
    this.field = registerField(this);
    this.refresh();
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let rule = this.field.requiredRule;
    // 多选状态 ， 用户传入空数组， 也需要必填提示
    if (rule && (this.value === null || this.value === "" || (Array.isArray(this.value) && !this.value.length))) {
      return rule.message ?? "请选择";
    }

    return null;
  }

  @Watch("value")
  valueChange() {
    if (this.validateTrigger == "realtime") {
      this.validateCore(this.value, false);
    }
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }

  protected async refresh() {
    this.loader = async () => {
      return this.actualOptions.map((o) => ({
        id: o.value,
        name: o.text,
        selectable: true,
        isDirectory: false,
      }));
    };

    try {
      this.loading = true;
      if (typeof this.options == "function") {
        this.cachedOptions = await this.options();
      } else if (typeof this.options == "string") {
        this.cachedOptions = (await request(this.options, {})).list;
      }
    } finally {
      this.loading = false;
    }
  }

  protected onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    if (this.loading) {
      return;
    }
    this.show = true;
    this.$emit("update:popup", true);
  }

  protected onPopupInput(v: boolean) {
    this.show = false;
    this.$emit("update:popup", v);
  }

  protected onInput(v) {
    if (this.multiple || this.multiple === "" || typeof v === 'string' || typeof v === 'number') {
      this.$emit("input", v);
    } else {
      this.show = false;
      this.$emit("input", v.length == 0 ? null : v[v.length - 1]);
      this.$emit("update:popup", false);
    }
  }
}
</script>