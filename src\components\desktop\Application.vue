<template>
  <div v-loading="$app.loading">
      <div class="fm-container">
<!--      <keep-alive>-->
<!--        <application-frame v-if="$route.meta.keepAlive" />-->
<!--      </keep-alive>-->
<!--      <application-frame v-if="!$route.meta.keepAlive" />-->
          <application-frame/>
    </div>
  </div>
</template>

<script lang="ts">
import ApplicationFrame from "./ApplicationFrame.vue";
import { Component, Vue } from "vue-property-decorator";

@Component({
  components: {
    ApplicationFrame,
  },
})
export default class Application extends Vue {
}
</script>