<template>
  <div>
    <el-steps
      class="o-steps-panel-steps"
      :active="value"
      finish-status="success"
    >
      <el-step
        v-for="(item, i) in $slots.default"
        :key="i"
        title="步骤 1"
      ></el-step>
    </el-steps>
    <switcher :value="value">
      <slot></slot>
    </switcher>
    <div class="o-steps-panel-bottom">
      <OButton style="min-width: 70px;" @click="prev()">上一步</OButton>
      <OButton type="primary" style="min-width: 70px;" @click="next()">确定</OButton>
    </div>
  </div>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";
import Switcher from "../switcher";
import OButton from "../button";

@Component({
  components: {
    OButton,
    Switcher,
  },
})
export default class  StepsPanel extends Vue {
  @Prop() value!: number;

  prev() {
    const value = this.value ?? 0;
    if (value > 0) {
      this.$emit("input", value - 1);
    }
  }

  next() {
    const value = this.value ?? 0;
    const length = this.$slots.default?.length ?? 0;
    if (value + 1 < length) {
      this.$emit("input", value + 1);
    }
  }
}
</script>
<style lang='less' scoped>
.o-steps-panel {
  &-steps {
    max-width: 600px;
    margin: auto;
  }

  &-bottom {
    display: flex;
    justify-content: center;
  }
}
</style>