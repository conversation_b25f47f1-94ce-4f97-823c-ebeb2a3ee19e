<template>
  <div
    class="o-table"
    :class="{ pb60: isPaginationFixed }"
  >
    <!-- {{tableReloadKey}} -->
    <!-- {{tableKey}} -->
    <!-- {{sortData}} -->
    <!-- {{context.store.oTableLoading}} -->
    <!-- {{actionButtonColumnWidth}} -->
    <el-row
      type="flex"
      class="header o-table-header"
      justify="space-between"
      v-if="isTopTableHeaderArea"
    >
      <div class="flex-1 table-header-left">
        <span
          class="select-num"
          v-if="selection && tableSelectList.length"
        >
          已选 {{ tableSelectList.length }} 项
        </span>

        <TableHeaderButtonRender
          v-for="item in context.tableHeaderActionButtonData.left"
          class="m-r-8"
          :key="item.label"
          v-bind="item"
        />
      </div>

      <div class="table-header-right flex">
        <TableHeaderButtonRender
          v-for="item in context.tableHeaderActionButtonData.right"
          :class="[showHeaderSet?'m-r-8':'']"
          :key="item.label"
          v-bind="item"
        />
        <div
          class="pointer header-set"
          @click="tableHeaderSetClick"
          v-if="showHeaderSet"
        >
          <i class="icon olading-iconfont oi-set " />
        </div>
      </div>
    </el-row>

    <div class="table-loading" v-loading="context.store.oTableLoading" v-if="context.store.oTableLoading" v-loading-height="context.store.oTableLoading"  />

    <!-- 表格 -->
    <el-table-extends
      :data="context.store.tableData"
      :header-cell-style="context.headerCellStyle"
      :key="tableReloadKey"
      :class="{ opacity : context.store.oTableLoading }"
      ref="el-table"
      @selection-change="onSelectionChange"
      @select-all="onSelectAll"
      :row-key="rowKey"
      :emptyText="context.emptyText"
      v-sticky="sticky"
      :showHeader="showHeader"
      v-empty-height="context.store.emptyHeight"
    >
      <!-- 多选按钮 -->
      <el-table-column
        type="selection"
        fixed="left"
        :selectable="selected"
        v-if="selection && context.store.tableHeader.length"
        width="52"
      >
      </el-table-column>

      <!-- 表格内容列 -->
      <el-table-column
        v-for="item in context.store.tableHeader"
        :key="item.id || (item.prop+'_'+item.label)"
        :prop="item.prop"
        :fixed="isTableColumFixed(item)"
        :label="item.label"
        :width="getColumnLabelWidth(item,context.store.tableHeader).width"
        :align="item.align"
        :min-width="getColumnLabelWidth(item,context.store.tableHeader).minWidth"
        :show-overflow-tooltip="getShowOverflowTooltip(item,context.store.showOverflowTooltip) "
      >

        <!-- 自定义表头 -->
        <template slot="header">
          <div
            class="flex table-header-title"
            @click="handleHeaderTitleClick(item,$event)"
            :class="[ item.sort?'pointer':'' , sortData[item.prop],item.headerAlign ]"
          >
            <span class="table-column-title" v-if="item.type!=='IGNORE'">
              {{ item.label }}
            </span>

            <div
              class="sort"
              v-if="item.sort"
            >
              <!-- desc asc -->
              <i
                class="icon olading-iconfont oi-table-sort"
                :class="{ active:item.order === 'desc' }"
              />
              <i
                class="icon olading-iconfont oi-table-sort"
                :class="{ active:item.order === 'asc' }"
              />
            </div>

            <el-tooltip
              effect="dark"
              placement="top-start"
            >
              <div slot="content">
                <div v-html="item.tooltipContent" />
              </div>
              <i
                class="icon olading-iconfont oi-help"
                v-if="item.tooltipContent"
              />
            </el-tooltip>
          </div>
        </template>
        <template slot-scope="scope">

          <template v-if="item.render">
            <table-render class="label" :renderFn="item.render" :scope="scope" />
          </template>
          
          <!-- 使用格式化 -->
          <template v-else-if="item.formatter">
            <span
              class="label"
              v-html="item.formatter(scope.row)"
            ></span>
          </template>

          <!-- 根据type使用自定义内置组件 -->
          <template v-else-if="hasTableHeaderTypeComponent (item.type)">
            <component
              v-bind="item"
              :is="'table-'+tableHeaderTypeComponentName[item.type]"
              :scope="scope"
              :value="scope.row[item.prop]"
            />
          </template>

          <!-- 使用插槽 -->
          <template v-else-if="item.slotName">
            <slot
              :name="item.slotName"
              :prop="item.prop"
              :label="item.label"
              :headerItem="item"
              v-bind="scope"
            />
          </template>

          <!-- 使用config 对应的type 进行数据转换 -->
          <span
            v-else
            style="white-space: pre;"
            class="label"
            :class="{ click:item.click ,[`label-${item.align}`]:true }"
            @click="handleTableLabelClick(item,scope)"
            :style="item.labelStyle"
          >{{context.getTableRowValue(scope,item)}}</span>
        </template>
      </el-table-column>

      <!-- 操作按钮列占位 -->
      <template>
        <el-table-column :width="tableOperationButtonWidth">
          <template slot="header">
            <!-- 操作占位符 -->
          </template>
          <template slot-scope="scope">
            <!-- 操作按钮计算宽度占位元素 -->
            <TableActionPlaceholderHtml
              ref="tableActionPlaceholderHtml"
              :list="getActionButtons(context.store.actionButtons,scope.row)"
              :more="scope.row._moreButton"
            />
          </template>
        </el-table-column>
      </template>
      <!-- 操作按钮列 -->
      <template v-if="showActionButtonColumn">
        <el-table-column
          :width="parse2px(actionColWidth || actionButtonColumnWidth) "
          :fixed="context.isActionFixRight"
        >

          <template slot="header">
            <div>
              <span class="action-header-title">操作</span>
            </div>
          </template>

          <template slot-scope="scope">
            <div v-if="!getActionButtons(context.store.actionButtons,scope.row).length">
              <span class="action-button-empty">-</span>
            </div>
            <div
              v-else
              class="action-button-wrap"
              :class="{ isMore:scope.row._moreButton }"
            >
              <div
                v-for="(item) in getActionButtons(context.store.actionButtons,scope.row)"
                :key="item.id"
              >
                <el-button
                  v-if="ifActionButton(item,scope.row)"
                  class="action-button-wrap__btn"
                  type="text"
                  v-bind="item.prop"
                  size="small"
                  :style="item.styleProps"
                  @click="item.click(scope.row,scope)"
                >{{ item.label }}
                </el-button>
              </div>
              <el-dropdown trigger="click">
                <el-button
                  v-show="scope.row._moreButton"
                  class="more"
                  icon="icon olading-iconfont oi-icon_other_"
                  type="text"
                />
                <el-dropdown-menu
                  slot="dropdown"
                  class="o-table-action-dropdown-menu"
                  :offset="10"
                >
                  <el-dropdown-item
                    v-for="(item) in getActionButtons(context.store.actionButtons,scope.row,'more')"
                    :key="item.id"
                    :disabled="item.disabled"
                    @click.native="item.click(scope.row,scope)"
                  >{{ item.label }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table-extends>

    <!-- 小于15条数据时候 才需要设置占位控件 -->
    <div style="height:40px;display:none;" v-if="context.store.tableData.length<15" class="placeholder-block"/>

    <!-- 底部分页器 -->
    <div
      class="pagination"
      v-if="context.showPagination"
      :style="paginationStyle"
      :class="{ fixed:isPaginationFixed }"
    >
      <TablePaginationVirtualScroll
        ref="table-pagination-virtual-scroll"
        :width="virtualScrollWidth-14"
        @scrollChange="onScrollChange"
        :class="{ opacity : context.store.oTableLoading }"
      />
      <el-pagination
        :page-size="context.pagination.limit"
        :layout="context.pagination.layout"
        :total="context.pagination.total"
        background
        popperClass="o-table-pagination-select-popup"
        class="paging"
        :page-sizes="context.pagination.pageSize"
        @size-change="handleSizeChange"
        :current-page="context.pagination.currentPage"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Component,
  Vue,
  Prop,
  Emit,
  Ref,
  Provide,
  Inject,
  Watch,
} from "vue-property-decorator";
import {
  TableHeader,
  tableHeaderActionButtonItem,
  Pagination,
  Sticky,
  TableActionButtonItem,
} from "./type";

import {
  addResizeListener,
  removeResizeListener,
  addScrollListener,
  removeScrollListener,
  parse2px,
} from "../util";
import TablePaginationVirtualScroll from "./components/TablePaginationVirtualScroll.vue";
import TableActionPlaceholderHtml from "./components/TableActionPlaceholderHtml.vue";
import Sortable from "sortablejs";


const files = import.meta.globEager("./components/*.vue")
const components = {};

for(var key in files){
  const file = files[key]
  const tmp = key.split('/')
  components[tmp[2].replace(/(\.\/|\.vue)/g, "")] = file.default
}



import { sticky } from "./utils/new-table-sticky";
import directives from "./utils/directives";
import { TABLE_CONFIG } from "./config";
import { TableContext } from "./table";
import * as utils from "./utils";
import _ from "lodash";
import { isSafariBrowser } from "../../../components/common/util"

enum TableHeaderTypeComponentName {
  CHECKBOX = "checkbox",
  SWITCH = "switch",
  DRAG = "drag",
}

@Component({
  inheritAttrs: false,
  components,
  directives: {
    sticky,
    ...directives,
  },
})
export default class indexTable extends Vue {
  // 表格表头设置
  @Prop({ default: () => [] }) readonly tableHeader!: TableHeader;
  // 表格文字移除是否展示省略号
  @Prop({ default: true }) readonly showOverflowTooltip!: boolean;
  // 是否返回到上次滚动的位置 只有在requestFn 的时候 才生效
  @Prop({ default: false }) readonly isToLastScrollPosition!: boolean;
  // 操作按钮是否浮动
  @Prop({ default: true }) readonly actionFix!: boolean;
  // 是否显示分页
  @Prop({ default: false }) readonly showPagination!: boolean;
  // 操作列按钮配置
  @Prop({ default: () => [] }) readonly actionButtons!: TableActionButtonItem[];
  // 表格数据
  @Prop({ default: () => [] }) readonly tableData!: Record<string, any>[];
  // 是否出现左侧多选列
  @Prop({ default: false }) readonly selection!: boolean;
  // 总页数，不传requestFn 时候有用，一般用于用户手动调用接口，并设置表格 tableData tableHeader 时会设置
  @Prop({ default: 0 }) readonly total!: Number;
  // 暂无数据提示
  @Prop({ default: "暂无数据" }) readonly emptyText!: String;
  // rowKey
  @Prop({ default: "id" }) readonly rowKey!: String;
  // 暂无数据时表格的高度
  @Prop({ default: 400 }) readonly emptyHeight!: Number;
  // 是否显示顶部操作按钮
  @Prop({ default: true }) readonly showTableHeaderAction!: boolean;
  // 吸顶配置
  @Prop({ default: false }) readonly sticky!: boolean | Sticky;
  // 分页配置
  @Prop({ default: () => {} }) readonly pagination!: Pagination;
  @Prop({ default: () => {} }) readonly loadingPosition!: string;
  // 是否删除api列表中的空参数
  @Prop({ default: false }) readonly deleteNullApiParams!: boolean;
  
  @Prop({ default: () => {} }) readonly beforeSetTableHeader!: Function;
  @Prop({ default: () => {} }) readonly beforeSetTableData!: Function;
  @Prop({ default: () => {} }) readonly selected!: Function;
  
  // 请求API函数
  @Prop({
    default: null,
    validator(value) {
      return typeof value === "function";
    },
  })
  readonly requestFn!: Function | null;
  // 操作按钮宽度
  @Prop({ default: 0 }) readonly actionColWidth!: Number | string;
  // 顶部操作按钮配置
  @Prop({ default: () => [] })
  readonly tableHeaderActionButtons!: tableHeaderActionButtonItem[];

  @Inject({ from: "oConCationContext", default: null })
  readonly oConCationContext!: any;

  @Ref("tableActionPlaceholderHtml")
  readonly tableActionPlaceholderHtml!: TableActionPlaceholderHtml;

  @Ref("el-table") readonly elTable!: any;
  @Ref("table-pagination-virtual-scroll")
  readonly TablePaginationVirtualScroll!: TablePaginationVirtualScroll;

  componentName = "oTable";

  tableHeaderTypeComponentName = TableHeaderTypeComponentName;

  context = new TableContext(this);
  actionButtonColumnWidth: number = 0;
  virtualScrollWidth: number = 0;
  actionButtonDom = {};
  utils = utils;
  tableSelectList = [];
  showHeader = true;
  sortData = {};
  fixRight = false;
  resizeState = {
    width: 0,
    height: 0,
    left: 0,
    right: 0,
  };
  tableReloadKey = "0";
  paginationScrollState = {
    tableWrapScrollTimer: 0,
    customScrollTimer: 0,
  };
  tableOperationButtonWidth = isSafariBrowser()?0:1


  parse2px = parse2px;

  @Provide() tableContext = this.context;

  @Emit("selection-change")
  onSelectionChange(value) {
    this.tableSelectList = value;
    return value;
  }

  @Emit("table-header-set-click")
  tableHeaderSetClick() {}

  get tableSortData() {
    return Object.keys(this.sortData)
      .map((field) => {
        return {
          field,
          direction: this.sortData[field],
        };
      })
      .filter((item) => item.direction);
  }

  @Watch("sortData", {
    deep: true,
  })
  onWatchSortData() {
    if (this.requestFn) return;
    this.tableReloadKey = _.uniqueId("o-table");
  }

  // 获取表格类型对应的组件名称
  hasTableHeaderTypeComponent(typeName) {
    for (let componentName of Object.keys(components)) {
      if (
        componentName.toLocaleLowerCase() ===
        "table" + TableHeaderTypeComponentName[typeName]
      )
        return true;
    }
    return false;
  }

  // 是否有更多按钮
  get isMoreButton() {
    if (!this.actionButtons) return false;
    for (let row of this.context.store.tableData) {
      this.getActionButtons(this.actionButtons, row);
      if ((row as any)._moreButton) return true;
    }
    return false;
  }

  // 是否显示 操作按钮
  get showActionButton() {
    const { actionButtons, tableData } = this.context.store;
    return actionButtons.length && tableData.length;
  }

  get showHeaderSet() {
    return this.$listeners["table-header-set-click"];
  }

  get paginationStyle() {
    const { left, width } = this.resizeState;
    const result = {
      left: `${left}px`,
      right: `calc(100vw - ${left}px - ${width}px)`,
    };
    return result;
  }

  // 是否展示顶部操作区域
  get isTopTableHeaderArea() {
    const result =
      this.selection ||
      this.context.store.tableHeaderActionButtons.length ||
      this.showHeaderSet;
    if (!result) return result;
    return this.showTableHeaderAction;
  }

  // 是否显示操作按钮
  get showActionButtonColumn() {
    // 如果用户没有设置操作按钮列的配置 隐藏即可
    if (!this.actionButtons.length) return false;
    return this.actionButtonColumnWidth != 1;
  }

  get isPaginationFixed() {
    // 如果不展示分页， 无需设置分页的样式
    if (!this.context.showPagination) return false;
    return this.pagination?.fixed;
  }

  get elTableWidth() {
    const el = this.$el;
    const fixedWidth = el.querySelector(".el-table__fixed")?.clientWidth || 0;
    const fixedLeftWidth =
      el.querySelector(".el-table__fixed-right")?.clientWidth || 0;
    const elWidth = el.clientWidth;

    return Math.floor(elWidth - fixedLeftWidth - fixedWidth);
  }

  get TableHeaderWidthTotal() {
    return this.context.store.tableHeader.reduce((acc, item) => {
      if (typeof item.width === "string" && item.width.includes("px")) {
        item.width = Number(item.width.replace("px", ""));
      }
      return acc + (Number(item.width) || 0);
    }, 0);
  }

  protected getShowOverflowTooltip(item, appShowOverflowTooltip) {
    // item设置的showOverflowTooltip 优先级高于 最外层的appShowOverflowTooltip 属性
    if (typeof item.showOverflowTooltip !== "boolean")
      return appShowOverflowTooltip;
    if (!item.showOverflowTooltip) return item.showOverflowTooltip;
    return item.showOverflowTooltip || appShowOverflowTooltip;
  }

  protected getColumnLabelWidth(item, tableHeader) {
    let width = item.width;
    let minWidth = 0;

    if(item.minWidth) minWidth = item.minWidth

    // 如果用户没有设置宽度 , 并且也没有设置最小宽度 ， 进行自动计算最小宽度
    if (!width && !minWidth) {
      minWidth = item.label.length * 12 + 24;
      // 如果有排序 ， 再加上排序的宽度
      if (item.sort || item.tooltipContent) minWidth += 20;
    }

    // 如果只有1列 ， 宽度自动撑开全屏即可
    if (tableHeader.length === 1) width = "";

    // 如果当前列 加起来的宽度 小于当前表格的宽度 ， 需要做一下适配
    if (this.TableHeaderWidthTotal < this.elTableWidth && !width) {
      // 获取 平均宽度
      const averageWidth = this.elTableWidth / tableHeader.length;
      // 如果当前设置的列的宽度 小于平均宽度 ， 就取消他的width  让他平均自适应即可
      if (item.width < averageWidth) width = "";
    }

    return {
      width: parse2px(width),
      minWidth: parse2px(minWidth),
    };
  }

  // 获取总条数
  getTotal() {
    return this.context.pagination.total;
  }

  // 是否展示当前按钮
  protected ifActionButton(item, row) {
    // eslint-disable-next-line no-prototype-builtins
    if (!item.hasOwnProperty("ifShow")) return true;
    if (typeof item.ifShow === "boolean") return item.ifShow;
    return item.ifShow(row);
  }

  // 获取操作按钮列宽
  protected getActionButtonColumnWidth() {
    let isEmpty = false;

    this.actionButtonColumnWidth = Math.max(
      ...[...this.$el.querySelectorAll(".contrast-action-dom")].map((el) => {
        const text = el.textContent;
        if (text) isEmpty = true;
        return el.clientWidth;
      })
    );
    this.actionButtonColumnWidth =
      this.actionButtonColumnWidth < 60 ? 60 : this.actionButtonColumnWidth;
    if (!isEmpty) this.actionButtonColumnWidth = 1;
  }

  handleTableLabelClick(item, scope) {
    item.click && item.click(scope.row);
  }

  // 获取操作按钮渲染的个数
  protected getActionButtons(buttons, row, type?: string) {
    buttons = buttons.filter((item) => this.ifActionButton(item, row));

    if (buttons.length > TABLE_CONFIG.actionButtonMaxLen) {
      const [b1, b2, ...moreBtn] = buttons;
      this.$set(row, "_moreButton", true);
      if (type !== "more") return [b1, b2];
      return moreBtn;
    }

    this.$set(row, "_moreButton", false);
    return buttons;
  }

  // 点击标题
  async handleHeaderTitleClick(item, event) {
    if (!item.sort) return;

    if (!(item.prop in this.sortData)) this.sortData = {};

    const sortType = this.sortData[item.prop];

    switch (sortType) {
    case "ASCENDING":
      this.$set(this.sortData, item.prop, "DESCENDING");
      break;
    case "DESCENDING":
      this.$set(this.sortData, item.prop, "");
      break;
    default:
      this.$set(this.sortData, item.prop, "ASCENDING");
      break;
    }
    if (!this.requestFn) return this.$emit("sort-change", this.tableSortData);

    let params = this.context.requestParams.filters
      ? this.context.requestParams.filters
      : {};

    params.noBackLeaveScrollPosition = true;

    await this.context.setRequestParams(params);
  }

  // 分页-条数
  protected async handleSizeChange(value: number) {
    this.context.pagination.limit = value;
    this.context.setPageSizeCache(value);
    await this.$nextTick();
    this.handleCurrentChange(1);
    this.context.pagination.currentPage = 1;
  }

  // 分页-开始位置
  protected handleCurrentChange(value: number) {
    const { pagination } = this.context;
    pagination.currentPage = value;

    if (this.requestFn) {
      this.context.setPaginationParams();
      return;
    }

    this.$emit("paginationChange", {
      start: pagination.currentPage,
      limit: pagination.limit,
    });
  }

  // 刷新列表 参数不改变
  async reload() {
    await this.context.getList();
  }

  // 获取列表请求参数
  getRequestParams() {
    return this.context.requestParams;
  }

  // 设置列表请求参数，会触发分页回到第一页
  setRequestParams(params={}, currentPage) {
    return this.context.setRequestParams(params, currentPage);
  }

  // 追加请求参数，想保留原有参数，并使用新的参数覆盖原有参数
  appendRequestParams(params, currentPage?: number) {
    return this.context.appendRequestParams(params, currentPage);
  }

  protected imgLoad(row) {
    this.$set(row, "hideImgLoading", true);
  }

  // 绑定事件
  protected bindEvents() {
    addResizeListener(this.$el, this.resizeListener);
  }

  // 记录表格或者自定义滚动条 滚动行为状态
  protected setPaginationState(type) {
    clearTimeout(this.paginationScrollState[type]);
    // 记录当前滑动状态
    this.paginationScrollState[type] = setTimeout(
      () => (this.paginationScrollState[type] = 0),
      300
    );
  }

  // 监听自定义滚动条发生滚动行为
  protected onScrollChange(left) {
    // 如果用户正在滑动饿了么 表格的滚动条 。
    // 此时会对自定义滚动条 scrollLeft 进行设置
    // 然后自定义滚动条 又会触发 滚动行为发生变化的回调
    // 然后继续调用 设置饿了么滚动条的 scroll 就会造成死循环
    // 所以这里判断 如果用户正在滑动饿了么滚动条，阻止以下行为的发生
    if (this.paginationScrollState.tableWrapScrollTimer) return;
    // 设置饿了么Table 滚动条位置
    this.elTable.bodyWrapper.scrollLeft = left;

    // console.log("自定义滚动条变化")

    this.setPaginationState("customScrollTimer");
  }

  // 设置自定义滚动条width
  protected setVirtualScrollWidth() {
    if(!this.elTable || !this.elTable.bodyWrapper) return 
    const tableBody = this.elTable.bodyWrapper.querySelector(".el-table__body");
    if (!tableBody) return;
    const { scrollWidth } = tableBody;
    this.virtualScrollWidth = scrollWidth;
    // 重新设置 自定义滚动条位置
    this.onElTableBodyScroll();
  }

  // 监听elTableBody 滚动条位置发生变化，联动自定义滚动条
  protected bindElTableBodyScroll() {
    if(this.elTable.bodyWrapper) {
      addScrollListener(this.elTable.bodyWrapper, this.onElTableBodyScroll);
    }
  }

  // 监听了么表格滚动条发生变化
  protected onElTableBodyScroll() {
    // 如果用户正在滑动自定义滚动条， 避免递归，取消以下事件的执行
    if (this.paginationScrollState.customScrollTimer) return;

    // 找不到滚动组件 ， 这种场景可能是暂无数据 ， 或者不展示分页的情况
    if (!this.TablePaginationVirtualScroll) return;

    // console.log("饿了么滚动条发生变化")

    const bodyWrapper = this.elTable.bodyWrapper;
    this.TablePaginationVirtualScroll.setScrollLeft(bodyWrapper.scrollLeft);

    this.setPaginationState("tableWrapScrollTimer");
  }

  // 移除所有监听的事件
  protected unbindEvents() {
    removeResizeListener(this.$el, this.resizeListener);
    removeScrollListener(this.$el, this.onElTableBodyScroll);
  }

  // 监听isPaginationFixed 变化设置main容器大小
  @Watch("isPaginationFixed")
  onIsPaginationFixedWatch(newValue) {
    const el = this.$el,
      containerMainEl = this.oConCationContext?.$refs["o-container-main"],
      className = "pagination-fixed";

    // 移除class
    if (!newValue) {
      if (containerMainEl) {
        containerMainEl.classList.remove(className);
      } else {
        el.classList.remove(className);
      }
      return;
    }

    // 添加class
    if (containerMainEl) {
      containerMainEl.classList.add(className);
    } else {
      el.classList.add(className);
    }
  }

  destroyed() {
    this.unbindEvents();
  }

  // 监听窗口大小发生变化
  protected resizeListener() {
    // console.log("窗口大小发生变化")
    const el = this.$el as HTMLElement;
    const rects = el.getClientRects()[0] as any;
    if (!rects) return;
    const { width: oldWidth } = this.resizeState;
    const { width } = rects;
    // 宽度发生改变
    if (width !== oldWidth) {
      this.resizeState = rects;
      (this as any).$customEventBus && (this as any).$customEventBus.$emit("menu-collapse");
      this.setVirtualScrollWidth();
    }
  }

  onSelectAll(val) {
    if (!val.length && !this.tableData.length) {
      this.elTable.clearSelection();
    }
  }

  // 初始化resize状态
  protected initResizeState() {
    const el = this.$el as HTMLElement;
    const rects = el.getClientRects()[0] as any;
    if(rects) this.resizeState = rects;
    
  }

  get isDrag (){
    return this.tableHeader.some(item=>item.type === "DRAG")
  }

  initSortTable() {
    const el = this.$el.querySelector(
      ".el-table__body-wrapper > table > tbody"
    );
    if(!this.isDrag) return 
    // 根据具体需求配置options配置项
    const sortable = new Sortable(el, {
      // draggable: ".drag", 
      draggable:".el-table__row",
      handle:".drag",
      onEnd: (evt) => {

        if(evt.oldIndex === evt.newIndex) return 

        const removeItem = this.tableData.splice(evt.oldIndex,1)[0]
        this.tableData.splice(evt.newIndex,0,removeItem)

        this.$emit("drag",evt)
      },
    });
  }

  isTableColumFixed(item){
    if(!this.context.store.tableData.length) return false
    return item.fixed
  }

  async mounted() {
    this.context.init(this);
    this.bindEvents();
    this.initResizeState();

    this.initSortTable()
  }
}
</script>

<style lang="less" scoped>
@import "./styles/table-scope.less";
</style>
<style lang="less">
@import "./styles/table-global.less";
</style>