<template>
  <div class="result-list">
    <div class="result-list_item" v-if="list.length > 0" v-for="(item, index) in list" :key="index" @click="toPage(item)">
      <div class="text">
        <template v-if="item.html"><span v-html="item.html"></span></template>
        <template v-else>{{item.text}}</template>
      </div>
      <div class="level" v-if="item.parentNodes.length > 0">(
        <template v-for="(node, nodeIndex) in item.parentNodes">
          {{node.text}}
          <span v-if="nodeIndex < item.parentNodes.length - 1" class="iconfont icon-right_arrow"></span>
        </template>
        )</div>
    </div>
    <div class="no-data" v-if="list.length === 0">未搜索到相关功能</div>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Watch, Vue } from "vue-property-decorator";
  import {SearchMap, SearchVo} from "../../../model/type";
  import {Search} from "../../../components/desktop/search-box/search";
  @Component({
    name: "result-list"
  })
  export default class ResultList extends Vue {
    //接收的searchMap数据
    @Prop()
    private data!:SearchMap;

    //显示的数据
    private list:SearchVo[] = [];

    //历史搜索数据
    private historicalSearch:SearchVo[] = [];

    //历史数据最大数量
    private maxHistorySize = 12;

    @Watch("data")
    private watchData(val) {
      if (this.data.accurate.length > 0) {
        this.list = this.data.accurate;
      } else if (this.data.probably.length > 0) {
        this.list = this.data.probably;
      } else {
        this.list = [];
      }
    }

    private toPage(page) {
      this.pushHistory(page);
      let $parent:any = this.$parent;
      let resolverConfig = $parent.resolverConfig;
      Search.toPage(page, resolverConfig, this);
    }

    /**
     * @description 设置缓存
     * @param page: SearchVo对象
     */
    private pushHistory(page: SearchVo) {

      let $parent:any = this.$parent;
      let historyCache = Search.getHistory($parent.userId, $parent.merchantId);
      if (historyCache) {
        this.historicalSearch = historyCache;
      }
      if (this.historicalSearch.length >= this.maxHistorySize) {
        this.historicalSearch.pop();
      }
      //查看历史数据中是否存在当前数据
      let historyIndex = this.historicalSearch.findIndex(item => item.type === page.type && item.text === page.text);
      console.info("historyIndex", historyIndex);
      //不存在，将此数据放到第一个
      if (historyIndex == -1) {
        this.historicalSearch.unshift(page);
        Search.setHistory(this.historicalSearch, $parent.userId, $parent.merchantId);
      } else {//存在，将此数据放第一个，重新缓存
        this.historicalSearch.splice(historyIndex, 1);
        this.historicalSearch.unshift(page);
        Search.setHistory(this.historicalSearch, $parent.userId, $parent.merchantId);
      }
    }


  }
</script>

<style lang="less">
  .default {
    .result-list-function(@hoverBg:#F7FAFD, @highlightColor:#4F71FF);
  }
  .red {
    .result-list-function(@hoverBg:#FBF0F1, @highlightColor:#BE0018);
  }
  .result-list-function(@hoverBg, @highlightColor) {
    .result-list {
      font-family: PingFangSC-Regular;
      &_item {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
        background: #FFFFFF;
        border-radius: 4px;
        transition: all .3s;
        cursor: pointer;
        padding: 0 8px;
        font-weight: 400;
        font-size: 14px;
        letter-spacing: 0;
        margin-bottom: 4px;
        .highlight {
          color: @highlightColor;
        }
        &:last-child {
          margin-bottom: 0;
        }
        .level {
          font-weight: 400;
          font-size: 12px;
          color: #777C94;
          letter-spacing: 0;
          margin-left: 8px;
          display: flex;
          align-items: center;
          .icon-right_arrow {
            font-size: 10px;
            margin: 0 4px;
          }
        }
        &:hover {
          background: @hoverBg;
        }
      }
      .no-data {
        text-align: center;
        height: 14px;
        font-weight: 400;
        font-size: 14px;
        color: #A8ACBA;
        letter-spacing: 0;
        line-height: 14px;
        margin: 40px 44px;
      }
    }
  }

</style>