import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';
import Chip from './Chip.vue';

<Meta title="基础组件/o-chip" component={Chip} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'primary', 'success', 'warning', 'danger'],
  },
  outlined: {
    type: 'boolean'
  },
  closable: {
    type: 'boolean'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Chip },
  template: '<o-chip v-bind="$props">名称</o-chip>',
});


主题

<Canvas>
  <Story 
    name="默认"
    args={{
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="primary"
    args={{
      type: 'primary',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="success"
    args={{
      type: 'success',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="warning"
    args={{
      type: 'warning',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="danger"
    args={{
      type: 'danger',
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


outlined

<Canvas>
  <Story 
    name="outlined-默认"
    args={{
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-primary"
    args={{
      type: 'primary',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-success"
    args={{
      type: 'success',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-warning"
    args={{
      type: 'warning',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-danger"
    args={{
      type: 'danger',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

closable

<Canvas>
  <Story 
    name="closable"
    args={{ 
      closable: true
    }}>
    {Template.bind({})}
   </Story>
  <Story 
    name="closable-primary"
    args={{ 
      type: 'primary',
      closable: true
    }}>
    {Template.bind({})}
   </Story>
  <Story 
    name="closable-outlined-primary"
    args={{ 
      type: 'primary',
      closable: true,
      outlined: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>