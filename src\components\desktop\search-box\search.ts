/**
 * @description 搜索类
 */
import {Navigation, NavResolverType, SearchMap, SearchVo} from "../../../model/type";
import {EventType, OpenType, QueryType, SearchType} from "../../../enums/nav";
import {MenuContext, Navigate} from "../../../util/desktop";
import {Menu} from "../../../model";

export class Search {

  //本地存储key
  private static LOCALSTORAGEKEY = "__SEARCHHISTORY__";
  /**
   * 数据源
   */
  private dataSource: SearchVo[];

  /**
   * 中文转拼音
   */
  private zhToPinYin: any;

  public constructor(dataSource: SearchVo[], zhToPinYin) {
    this.dataSource = dataSource;
    this.zhToPinYin = zhToPinYin;
    return this;
  }

  /**
   * @description 设置历史数据
   * @param historyList
   */
  public static setHistory(historyList: SearchVo[], userId: number, merchantId: number) {
    //获取所有的搜索历史
    let allHistory = this.getAllHistory();
    console.info("allHistory...",  allHistory);
    let historyMap = {};
    //判断是否有搜索历史
    if (allHistory) {
      let userHistory = allHistory[userId];
      if (!userHistory) {
        allHistory[userId] = {};
      }
      allHistory[userId][merchantId] = historyList;
      historyMap = allHistory;
    } else {
      historyMap = {
        [userId]: {
          [merchantId]: historyList
        }
      };
    }
    localStorage.setItem(this.LOCALSTORAGEKEY, JSON.stringify(historyMap));
  }

  public static toPage(page:SearchVo, resolverConfig: NavResolverType, $this) {
    //导航跳转
    if (page.type === SearchType.NAVIGATION) {
      let app: Navigation = {
        event: page.event,
        url: page.url,
        openType: OpenType.CURRENT_TAB,
        appMenuInfo: page.appMenuInfo,
        appCode: page.appCode,
        children: []
      };
      new Navigate($this)
        .setRoles(resolverConfig.roles || [])
        .setManager(resolverConfig.isManager || false)
        .setOpenApp(resolverConfig.openApp || [])
        .openAppEvent(app);
    } else if (page.type === SearchType.MENU) {
      let currentPage:SearchVo;
      if (page.children && page.children.length > 0 && page.level === 1 && !page.url) {
        currentPage = {
          ...page.children[0]
        };
      } else {
        currentPage = {...page};
      }
      let menu: Menu = {
        appCode: currentPage.appCode,
        routePath: currentPage.url,
        title: currentPage.text,
        privilegeCode: currentPage.privilegeCode,
        notOpenRule: currentPage.notOpenRule,
        incrementBusinessCode: currentPage.incrementBusinessCode,
        incrementLevel: currentPage.incrementLevel,
        isAuth: currentPage.isAuth,
        isOpen: currentPage.isOpen,
        belongProject: currentPage.belongProject,
        id: Number(currentPage.id),
        children: []
      };
      new MenuContext($this).setNewTab(false).openPage(menu);
    }
  }

  /**
   * @description 获取所有的历史搜索记录缓存
   */
  public static getAllHistory() {
    let allHistory = localStorage.getItem(this.LOCALSTORAGEKEY);
    if (allHistory) {
      return JSON.parse(allHistory);
    }
    return null;
  }

  /**
   * @description 通过userId & merchantId获取搜索历史记录
   * @param userId
   * @param merchantId
   */
  public static getHistory(userId: number, merchantId: number): SearchVo[] | null {
    let history:any = this.getAllHistory();
    if (history) {
      let userHistory = history[userId];
      if (userHistory) {
        return userHistory[merchantId] || null;
      }
    }
    return null;
  }

  /**
   * @description 搜索方法
   * @param keyword
   */
  public search(keyword: string) {
    keyword = keyword.toUpperCase();
    let resultMap:SearchMap = {
        accurate: [],
        probably: []
    };
    if (keyword === "") {
      return resultMap;
    }
    this.searchByKeyword(keyword, this.dataSource, resultMap);
    return resultMap;
  }

  private searchByKeyword(keyword: string, dataSource: SearchVo[], resultMap?: SearchMap):SearchVo[] {
    keyword = keyword.toUpperCase();
    //结果集
    let result:SearchVo[] = [];
    for (let i = 0; i < dataSource.length; i++) {
      let data:SearchVo = dataSource[i];
      let ok = false;
      let children:SearchVo[] = [];
      if (data.children && data.children.length > 0) {
        children = this.searchByKeyword(keyword, data.children);
      }
      if (data.event === EventType.OPEN_WORKBENCH) {
        continue;
      }
      //转换拼音
      let pinyin = this.zhToPinYin.getPinYin(keyword, false).toUpperCase();
      //转换首字母
      let initials = this.zhToPinYin.getInitials(keyword).toUpperCase();
      //转换分割线
      let pinyinSpace = this.zhToPinYin.getPinYin(keyword, true);
      let isEn = /^[A-Za-z]*$/.test(keyword);
      //先通过关键字查找
      if (data.text.indexOf(keyword) !== -1) {
        ok = true;
        let reg = new RegExp('(' + keyword + ')', 'gi');
        data.html = data.text.replace(reg, '<span class="highlight">$1</span>');
        data.queryType = QueryType.KEYWORD;
      } else if (data.zhToPinYin.pinyin.indexOf(pinyin) !== -1) {//通过拼音查找
        let splitArr = data.zhToPinYin.pinyinSpace.split(" ");
        splitArr.shift();
        for (let i = 0; i < splitArr.length; i++) {
          let index1 = splitArr[i].indexOf(pinyin);
          let index2 = pinyin.indexOf(splitArr[i]);
          if (index1 === 0 || index2 == 0) {
            ok = true;
            let reg = new RegExp('(' + data.text.split("")[i] + ')', 'gi');
            data.html = data.text.replace(reg, '<span class="highlight">$1</span>');
            data.queryType = QueryType.PINYIN;
            continue;
          }
        }
      } else if (data.zhToPinYin.initials.indexOf(initials) !== -1 && isEn) {//通过首字母查找
        let index = data.zhToPinYin.initials.indexOf(initials);
        let highlightText = data.text.substr(index, keyword.length);
        let reg = new RegExp('(' + highlightText + ')', 'gi');
        data.html = data.text.replace(reg, '<span class="highlight">$1</span>');
        ok = true;
        data.queryType = QueryType.INITIALS;
      } else if (data.zhToPinYin.pinyinSpace.indexOf(pinyinSpace) !== -1) {//通过拼音空格查找
        ok = true;
        data.html = "";
        data.queryType = QueryType.PINYINSPACE;
      } else {
        ok = false;
        data.queryType = QueryType.NOT;
      }
      if (data.event === EventType.EXPAND_MENU) {
        ok = false;
      }
      if (resultMap && (ok || children.length > 0)) {
        let parentNode:SearchVo[] = this.getParentNodes(data);
        data.parentNodes = parentNode.slice(0, parentNode.length - 1);
        if (ok) {
          if (data.queryType === QueryType.KEYWORD) {
            resultMap.accurate.push(data);
          } else if (data.queryType !== QueryType.NOT) {
            resultMap.probably.push(data);
          }
        }

        for (let i = 0; i < children.length; i++) {
          let item = children[i];
          item.parentNodes = parentNode;
          console.info(item);
          if (item.queryType === QueryType.KEYWORD) {
            resultMap.accurate.push(item);
          } else {
            resultMap.probably.push(item);
          }
        }
      }
      if (ok || children.length > 0) {
        result.push({
          ...data,
          children
        });
      }
    }
    return result;
  }

  /**
   * @description 通过appCode递归查询导航
   * @param appCode
   * @param navigationList
   */
  private recursionQueryNav(appCode, navigationList: SearchVo[]) {
    let result:SearchVo[] = [];
    for (let i = 0; i < navigationList.length; i++) {
      let nav = navigationList[i];
      let ok = false;
      let children:SearchVo[] = [];
      if (nav.children && nav.children.length > 0) {
        children = this.recursionQueryNav(appCode, nav.children);
      }
      if (nav.appCode === appCode) {
        ok = true;
      }
      if (ok || children.length > 0) {
        result.push({
          ...nav,
          children
        });
      }
    }
    return result;
  }

  /**
   * @description 平铺导航菜单
   * @param navigationList
   */
  private spreadNavigationNodes(navigationList: SearchVo[], spreadList: SearchVo[]) {
    for (let i = 0; i < navigationList.length; i++) {
      let item = navigationList[i];
      spreadList.push(item);
      if (item.children && item.children.length > 0) {
        this.spreadNavigationNodes(item.children, spreadList);
      }
    }
  }

  private getNavigationNodes(data) {
    let navigationList:SearchVo[] = this.dataSource.filter(item => item.type === SearchType.NAVIGATION);
    let spreadList:SearchVo[] = [];
    let queryList = this.recursionQueryNav(data.appCode, navigationList);
    this.spreadNavigationNodes(queryList, spreadList);
    return spreadList;
  }

  /**
   * @description 通过data获取父节点数据
   * @param data
   */
  private getParentNodes(data: SearchVo) {
    let parentNodes:SearchVo[] = [];
    //当前为菜单
    if (data.type === SearchType.MENU) {
        let appCode = data.appCode;
        parentNodes.push(data);
        parentNodes.unshift(...this.getNavigationNodes(data));
    } else if (data.type === SearchType.NAVIGATION) {//当前为导航
      //当前level不为1级，则寻找父节点
      if (data.level !== 1) {
        parentNodes.unshift(...this.getNavigationNodes(data));
      } else {
        parentNodes.push(data);
      }
    }
    return parentNodes;
  }
}
