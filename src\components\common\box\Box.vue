<template>
  <div
    class="o-box"
    :class="actualClass"
    :style="actualStyle"
    @click="$emit('click')"
  >
    <slot />
    <div class="o-box-bar" :class="barClass" :style="barStyle" />
  </div>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class  Box extends Vue {
  @Prop({ default: 0 }) elevation!: number;
  @Prop() type!: string;
  @Prop() borderRadius!: any;
  @Prop() outlined: any;
  @Prop() hover!: "bg" | "shadow" | "" | boolean;
  @Prop() bar!: "left" | "right" | "top" | "bottom";
  @Prop() disabled: any;

  get actualBorderRadius() {
    const r = parseInt(this.borderRadius as any);
    if (!isNaN(r)) {
      return r + "px";
    }
  }

  get barClass() {
    const v = [] as string[];
    if (this.bar) {
      v.push("o-" + this.bar);
    }
    return v;
  }

  get actualStyle() {
    let v = {} as any;
    v["border-radius"] = this.actualBorderRadius;
    return v;
  }

  get barStyle() {
    let v = {} as any;
    if (this.bar == "left" || this.bar == "right") {
      v["width"] = this.actualBorderRadius;
    } else if (this.bar == "top" || this.bar == "bottom") {
      v["height"] = this.actualBorderRadius;
    }
    return v;
  }

  get actualClass() {
    const elevation = this.elevation ?? 0;
    const type = this.type ?? "";
    const outlined = this.outlined || this.outlined === "";
    const disabled = this.disabled || this.disabled === "";
    const v = ["o-elevation-" + elevation];
    if (type) {
      v.push("o-" + type);
    }
    if (outlined) {
      v.push("o-outlined");
    }
    if (this.hover == "shadow") {
      v.push("o-hoverable-shadow");
    } else if (this.hover == "bg") {
      v.push("o-hoverable");
    }
    if (disabled) {
      v.push("o-disabled");
    }
    return v;
  }
}
</script>
<style lang='less' scoped>
</style>