<template>
  <div class="o-select-member-field">
    <div v-if="textModel">
      <span v-if="!innerValue.length">-</span>
      <template v-for="(item,index) in innerValue">
        <o-member :key="item.id" :value="item.id" :showDeleteText="true" />
        <span :key="item.id" v-if="innerValue.length>1 && index!==(innerValue.length-1)">,</span>
      </template>
    </div>
    <placeholder
      v-else
      :border="!mobile"
      class="o-select-member-field-body"
      :hoverable="true"
      @click="onClick"
      :placeholder="placeholder"
    >
      <o-member-chip
        v-for="(item, i) in innerValue"
        :key="item.id"
        :value="item.id"
        :closable="isCloseable"
        @change="onMemberChange"
        @close="onCloseTag(i)"
        style="max-width: 140px; margin: 2px 0"
      />
      <o-chip
        v-if="showAddBtn && mobile"
        type="primary"
        outlined
        class="o-select-member-field-addbtn"
        @click="onClick()"
      >
        +
      </o-chip>
    </placeholder>
    <popup v-model="show" :title="popupTitle" mobileHeight="80%">
      <select-user
        style="height: 100%"
        v-if="show"
        @close="closePopup"
        :enableSearch="true"
        selectTarget="user"
        :maxSelectNum="maxSelectNum"
        @input="onInput"
        :value="innerValue"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import Popup from "../Popup.vue";
import SelectUser from "../SelectUser.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, registerField } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";
import { asArray } from "../util";
import formFieldMixins from "../formFieldMixins";

@Component({
  mixins:[formFieldMixins],
  components: {
    SelectUser,
    Popup,
    Placeholder,
  },
})
export default class  SelectMemberField extends formFieldMixins {
  @Prop() value!: string[];
  @Prop({ default:"选择部门" }) popupTitle!: string;
  @Prop({ default: 10000 }) maxSelectNum!: number;
  @Prop() placeholder!: string;

  get innerValue() {
    let v = asArray(this.value);
    return v.map((o) => ({
      type: "user",
      id: o,
    }));
  }

  show = false;

  field!: FieldContext;
  valueTextMap:any = {}


  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get mobile() {
    return this.context.isMobile;
  }

  get valueText(){
    return Object.keys(this.valueTextMap).reduce((cur,id)=>{
      if(this.valueTextMap[id]) cur.push(this.valueTextMap[id])
      return cur
    },[] as any).join(",")
  }

  @Watch("innerValue")
  onWatchInnerValue(newValue){
    const valueTextMap = {}
    newValue.forEach(item=>{
      valueTextMap[item.id] = this.valueTextMap[item.id] || ""
    })
    this.valueTextMap = { ...valueTextMap }
  }

  onMemberChange(newValue){
    this.valueTextMap[newValue.userId] = newValue.name
  }



  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && rule && (this.value == null || this.value.length == 0)) {
      return rule.message ?? "请选择";
    }
    return null;
  }

  get showAddBtn() {
    // 禁用状态
    if(this.isDisabled) return false
    //可编辑 && 单选 && 已选择 情况下隐藏加号
    let readonly = this.readonly || this.readonly === "";
    if (readonly) {
      return false;
    }

    let maxSelectNum = this.maxSelectNum ?? 1;
    let valueLength = this.value?.length ?? 0;
    return valueLength < maxSelectNum;
  }

  onClick() {
    // 复用原来显示添加按钮逻辑
    // if(!this.showAddBtn) return 
    this.show = true;
  }

  get isCloseable (){
    if(this.isDisabled) return false
    return !this.isReadonly
  }

  closePopup() {
    this.show = false;
  }

  onInput(v) {
    this.closePopup();
    if(Array.isArray(v)) {
      this.emitInput(v.map((o) => o.id));
    }else{
      this.emitInput([]);
    }
    this.$emit("change",v)
  }
  

  onCloseTag(index) {
    if (this.readonly || this.readonly === "") {
      return;
    }
    let array = [...this.innerValue];
    array.splice(index, 1);
    
    this.$emit("change",array)
    this.emitInput(array.map((o) => o.id));
  }

  private emitInput(v: string[]) {
    if (this.maxSelectNum == 1) {
      this.$emit("input", v.length == 0 ? null : v[0]);
    } else {
      this.$emit("input", v);
    }
  }
}
</script>

<style lang="less" scoped>
.o-select-member-field {
  &-addbtn {
    height: 25px;
    width: 50px;
    cursor: pointer;
  }

  &-body {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>