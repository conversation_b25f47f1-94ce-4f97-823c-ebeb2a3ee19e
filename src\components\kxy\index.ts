
import { AppContextOption } from '../../model';

import App from "../desktop/App.vue"
import Page404 from "../desktop/Page404.vue"
import Page403 from "../desktop/Page403.vue"
import ApplicationFrame from "./ApplicationFrame.vue";

import { createApp } from '../../model/kxyCreate';

export function createKxyDesktopApp(option: AppContextOption, selector) {
  createApp(option, selector, Page404, App, ApplicationFrame, Page403)
}

