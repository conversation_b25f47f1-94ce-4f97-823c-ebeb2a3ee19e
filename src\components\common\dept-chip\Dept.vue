<template>
  <span>{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class  Dept extends Vue {
  @Prop() value!: string;
  @Prop({ default:false }) showDeleteText!: boolean;
  

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      try {
        let dept = await store.loadDept(this.value);
        if (dept) {
          this.text = dept.name;
          this.$emit("update:change", dept);
        }else if(this.showDeleteText){
          const name = `(${this.value})已删除`;
          this.text = name;
          this.$emit("update:change", name);
        }
      } catch (e) {
        this.text = "[错误]"
        throw e
      }
    }
  }
}
</script>