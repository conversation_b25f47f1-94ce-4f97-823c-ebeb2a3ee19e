<template>
  <o-form
    :readonly="readonly"
    :disabled="disabled"
    :labelPosition="_labelPosition"
    :labelWidth="labelWidth"
    :textModel="textModel"
    class="o-form-scene"
    ref="form"
    layout="compact"
  >
    <slot />
    <div
      v-if="!hideButton && hideButton !== ''"
      class="bottom"
    >
      <o-button
        :disabled="loading"
        @click="$emit('cancel')"
      >取消</o-button>
      <o-button
        type="primary"
        :loading="loading"
        style="margin: 0 0 0 10px"
        @click="onSubmit"
      >{{ saveButtonText }}</o-button>
      <o-button
        :disabled="loading"
        v-if="showResetButton"
        @click="handleResetButtonClick"
      >重置</o-button>
    </div>
  </o-form>
</template>

<script lang="ts">
import { request } from "../../../util";
import { Component, Prop, Ref, Vue } from "vue-property-decorator";
import FormBlock from "../../common/FormBlock";
import FormSection from "./FormSection.vue";

@Component({
  components: {
    FormBlock,
    FormSection,
  },
})
export default class FormScene extends Vue {
  @Prop() save!: any;
  @Prop() load!: any;
  @Prop() hideButton!: any;
  @Prop({ default:"保存" }) saveButtonText!: String;
  // 是否显示重置按钮
  @Prop({ default: false }) showResetButton!: boolean;
  // 左右模式或者上下模式。top 为上下，left/right 为左右（指左右时，label 是左对齐或右对齐）
  @Prop({ default:"top" }) readonly labelPosition!: "top" | "left" | "right";
  @Prop() readonly readonly!: boolean;
  @Prop() readonly disabled!: boolean;
  // 文本查看模式
  @Prop({ default:false }) readonly textModel!: boolean;
  // 左右模式时，label 的宽度
  @Prop() readonly labelWidth!: string

  get _labelPosition(){
    if(this.textModel) return "left"
    return this.labelPosition
  }

  @Ref("form")
  form: any;

  loading = false;

  componentName = "o-form-scene"

  async getValues() {
    const ok = await this.form.validate();
    if (!ok) {
      this.form.scrollToError();
      return;
    }

    return this.form.getValues();
  }

  async submit() {
    if (typeof this.save === "string") {
      this.submitCore((values) => request(this.save, values));
    } else if (typeof this.save === "function") {
      this.submitCore(this.save);
    }
  }

  protected handleResetButtonClick() {
    this.form.resetFields();
  }

  protected clearFieldsErrorMessage() {
    return this.form.clearFieldsErrorMessage();
  }

  protected async onSubmit() {
    this.submit();
  }

  protected async submitCore(f: (values: any) => Promise<void>) {
    let v = await this.getValues();

    if (v == null) {
      return;
    }

    try {
      this.loading = true;
      this.$emit("update:loading", this.loading);
      await f(v);
      this.$emit("submit");
    } finally {
      this.loading = false;
      this.$emit("update:loading", this.loading);
    }
  }
}
</script>
<style lang='less' scoped>
.o-form-scene {
  .bottom {
    display: flex;
    justify-content: center;

    button {
      min-width: 100px;
    }
  }
}
</style>