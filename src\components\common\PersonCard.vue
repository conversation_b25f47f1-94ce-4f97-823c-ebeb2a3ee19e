<template>
  <div class="comp-personal-card">
    <section class="personal-card-body" >
      <avatar :name="name ? name.slice(0,1) : ''" :size="25" />
      <van-popover v-model="showPopover" trigger="click">
        <div class="body-text-pop">
          <span>{{name}}</span>
        </div>
        <template #reference>
          <div class="body-text">
            <span>{{name}}</span>
          </div>
        </template>
      </van-popover>
      <van-icon v-if="closable" name="cross" style="cursor: pointer" @click="$emit('close')" />
    </section>
  </div>
</template>

<script lang='ts'>
import Avatar from "./Avatar.vue"
import { Component,Prop, Vue } from 'vue-property-decorator';
@Component({
  components:{
    Avatar
  },
})
export default class PersonCard extends Vue {
  @Prop({default:''}) name: String|undefined;
  @Prop({default: false}) closable!: boolean;
  showPopover:<PERSON>olean =  false;

}
</script>

<style lang="less" scoped>
.body-text-pop{
  padding:15px;
}
.comp-personal-card{
  padding:5px;
  display: inline-block;
  .personal-card-body{
    background-color: #F1F1F1;
    display: flex;
    align-items: center;
    min-height: 20px;
    min-width: 70px;
    max-width: 100px;
    padding:0 5px;
    border-radius: 25px;
    .body-img{
      display: flex;
      justify-content: center;
      align-items: center;
      color:#fff;
      font-size: 12px;
      border-radius: 50%;
      span{
        font-size: 12px;
        padding:0 10px;
      }
    }
    /deep/.van-popover__wrapper{
      flex: 1;
      overflow: hidden;
      .body-text{
        min-height: 30px;
        min-width: 50px;
        max-width: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        padding:0 5px;
        span{
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    
  }
}
</style>