import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import Comp from './SelectBankBranchField.vue';

<Meta title="业务组件/o-select-bank-branch-field" component={Comp} argTypes={{
  readonly: {
    type: 'boolean'
  },
  placeholder: {
    type: 'string'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  data: () => ({ m: "" }),
  template: '<o-select-bank-branch-field v-bind="$props" v-model="m"></o-select-bank-branch-field>',
});


# 选择银行分行组件

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>
