<!--
退出登陆
-->
<template>
  <el-dialog
    :custom-class="customClass"
    top="37vh"
    :before-close="close"
    :append-to-body="true"
    title="提示"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="visible"
    width="420px"
  >
    <div class="el-dialog__body_content">
      <span class="iconfont icon-unauthorized"></span>
      <span>是否退出登录？</span>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";

@Component({
  name: "o-logout",
})
export default class Logout extends Vue {
  /**
   * 是否显示
   */
  @Prop({
    default: false,
  })
  private visible!: boolean;

  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  /**
   * @description 获取客户定义class
   */
  private get customClass() {
    const { theme } = this;
    return `o-nav-logout-dialog o-nav-logout-dialog-${theme}`;
  }

  /**
   * @description 点击取消触发事件
   */
  private close(): void {
    this.$emit("on-close");
  }

  /**
   * @description 点击确认触发事件
   */
  private confirm(): void {
    this.$emit("on-confirm");
  }
}
</script>

<style lang="less">
.o-nav-logout-dialog {
  width: 420px !important;
  background: #ffffff;
  box-shadow: 0 8px 16px -14px rgba(52, 61, 160, 0.16) !important;
  border-radius: 8px !important;
  font-family: PingFangSC-Medium;
  .el-dialog__header {
    border: none !important;
    .el-dialog__title {
      height: 16px;
      font-weight: 500;
      font-size: 16px;
      color: #24262a;
      letter-spacing: 0;
      line-height: 16px;
    }
  }
  .el-dialog__body {
    padding: 10px 24px;
    &_content {
      display: flex;
      align-items: center;
      span {
        &:nth-child(1) {
          margin-right: 12px;
          color: #ffac04;
        }
        &:nth-child(2) {
          height: 14px;
          font-weight: 400;
          font-size: 14px;
          color: #46485a;
          letter-spacing: 0;
          line-height: 14px;
        }
      }
    }
  }
  .el-dialog__footer {
    padding: 0 20px 20px !important;
    //确认按钮
    .el-button--primary {
      background: #4f71ff !important;
    }
    //取消按钮
    .el-button--default {
      &:hover {
        background: #f7fafd;
        color: #4f71ff;
        border-color: #4f71ff;
      }
    }
  }
}
/*************************    红色皮肤start    ****************************/
.o-nav-logout-dialog-red {
  .el-icon-close,
  .el-dialog__headerbtn {
    &:hover {
      color: #d6342a;
    }
  }
  .el-dialog__footer {
    .dialog-footer {
      button.el-button--primary {
        background: #d6342a !important;
        border: none;
      }
      button.el-button--default {
        &:hover {
          border: solid 1px #d6342a;
          background: #fcf2f2;
          color: #d6342a;
        }
      }
    }
  }
}
/*************************    红色皮肤end    ****************************/
</style>