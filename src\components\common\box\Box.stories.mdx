import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';

import Box from './Box.vue';

<Meta title="基础组件/o-box" component={Box} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'primary', 'success', 'warning', 'danger'],
  },
  elevation: {
    type: 'number'
  },
  outlined: {
    type: 'boolean'
  },
  "borderRadius": {
    type: 'number'
  },
  hover: {
    control: { type: 'select' },
    options: ['', 'bg', 'shadow'],
  },
  bar: {
    control: { type: 'select' },
    options: ['', 'left', 'right', 'top', 'bottom'],
  },
  disabled: {
    type: 'boolean'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Box },
  template: '<o-box v-bind="$props" style="width: 100px; height: 100px; padding: 10px">例子</o-box>',
});


# o-box

o-box是一个简单的容器组件，支持主题色，阴影，hover，禁用，圆角等样式设置。
可以直接使用，或者作为其他组件的内部组成部分

这是一个主题色的示例

<Canvas>
  <Story 
    name="主题色"
    args={{
      type: 'primary',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="success"
    args={{
      type: 'success',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="warning"
    args={{
      type: 'warning'
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="danger"
    args={{
      type: 'danger',
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


outlined

<Canvas>
  <Story 
    name="outlined-primary"
    args={{
      type: 'primary',
      outlined: true,
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-success"
    args={{
      type: 'success',
      outlined: true,
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-warning"
    args={{
      type: 'warning',
      outlined: true,
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="outlined-danger"
    args={{
      type: 'danger',
      outlined: true,
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

阴影

<Canvas>
  <Story 
    name="阴影"
    args={{
      elevation: 3,
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


圆角

<Canvas>
  <Story 
    name="border-radius"
    args={{
      borderRadius: 8,
      outlined: true,
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

可以设置色带, bar属性必须和borderRadius同时设置

<Canvas>
  <Story 
    name="bar-left"
    args={{
      bar: "left",
      type: 'primary',
      outlined: true,
      borderRadius: 4
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="bar-right"
    args={{
      bar: "right",
      type: 'success',
      outlined: true,
      borderRadius: 4
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="bar-top"
    args={{
      bar: "top",
      type: 'warning',
      outlined: true,
      borderRadius: 4
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="bar-bottom"
    args={{
      bar: "bottom",
      type: 'danger',
      outlined: true,
      borderRadius: 4
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="bar-large"
    args={{
      bar: "top",
      type: 'primary',
      outlined: true,
      borderRadius: 8
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

悬浮效果

<Canvas>
  <Story 
    name="hover-primary"
    args={{
      hover: 'bg',
      type: 'primary',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="hover-default-outlined"
    args={{
      hover: 'bg',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="hover-primary-outlined"
    args={{
      hover: 'bg',
      type: 'primary',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="hover-shadow-outlined"
    args={{
      hover: 'shadow',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

悬浮阴影