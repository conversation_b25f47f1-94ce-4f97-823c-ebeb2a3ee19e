import { Component, Prop } from 'vue-property-decorator'
import * as tsx from 'vue-tsx-support'

import Step from "./SwitcherPanel.vue"

@Component({
  components: {
    Step
  }
})
export default class extends tsx.Component<any, any, any> {

  // 当前显示的子组件
  @Prop() value!: number;
  // true使用v-show切换，false使用v-if切换,默认为false
  @Prop() hide!: boolean

  protected render() {

    let active = this.value ?? 0
    let children = this.$slots.default ?? []
    children.forEach((o, i) => {
      if (!o.key) {
        o.key = i
      }
    })

    return <div>
      {children.map((o, i) => <step active={active == i} hide={this.hide}>{o}</step>)}
    </div>
  }
}