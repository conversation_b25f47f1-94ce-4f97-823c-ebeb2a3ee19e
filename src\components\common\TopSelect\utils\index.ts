import _ from "lodash"

const setOptionKey = (options,keyMaps)=>{
  for(let item of options) {
    for(let keyName of Object.keys(keyMaps)) {
      const value = item[keyMaps[keyName]]
      if (!_.isNil(value)) item[keyName] = value
    }
  }
  return options
}

// 通过field字段重新映射options数组并返回
export const field2Options = (options:Record<string,any>[],field:string = "") =>{
  const fields = field.split(",")
  const keyMaps = fields.reduce((cur,item)=>{
    const [oldKey,newKey] = item.split("as")
    cur[newKey.trim()] = oldKey.trim()
    return cur
  },{})

  return setOptionKey(options,keyMaps)
}