import { setCache,getCache,parsePath2Value,noop,apiParamsFilterNull } from "./utils"
import { TABLE_CONFIG,TABLE_HEADER_TYPE_CONFIG } from "./config"
import { TableContextStore,TableRequestParams } from "./type"
import _ from 'lodash'



export class TableContext {

  table!: any
  tableLoadingStartTime: number = 0
  oldTableProp:any = null
  getListDebounce:Function = noop

  constructor(table) {
    this.table = table
  }

  headerCellStyle = {
    ...TABLE_CONFIG.headerCellStyle
  }

  pagination = {
    ...TABLE_CONFIG.pagination
  }

  store: TableContextStore = {
    tableHeader: [],
    tableData: [],
    sticky: false,
    emptyText: "",
    // 是否未动态表头
    isDynamicTableHeader:false,
    showHeaderAction: true,
    actionButtons: [],
    requestFn: null,
    loading: false,
    actionFixRight:false,
    pagination: {
      fixed: false
    },
    showPagination: false,
    showOverflowTooltip: false,
    selection: false,
    emptyHeight: 0,
    tableHeaderActionButtons: [],
    oTableLoading: false,

    loadingTimer:0
  }

  requestParams:TableRequestParams = {}

  init(table) {
    this.table = table
    this.oldTableProp = _.cloneDeep(this.table.$props)

    this.setStore()
    this.onWatchProps()
    this.getPageSizeCache()

    this.getListDebounce = (_.debounce as any)(this.getList,TABLE_CONFIG.getListDebounceDelay,{},true)

  }

  // 获取顶部 按钮，并根据用户传入的 left right 重新组合数据
  get tableHeaderActionButtonData() {
    return this.store.tableHeaderActionButtons.reduce((acc, cur) => {
      if (cur.align === "right") {
        acc.right.push(cur)
      } else {
        acc.left.push(cur)
      }
      return acc
    }, {
      left: [],
      right: []
    } as any)
  }

  get isActionFixRight(){
    if(!this.table.actionFix) return false
    return this.store.actionFixRight
  }

  get emptyText() {
    if (this.store.oTableLoading) {
      return TABLE_CONFIG.emptyLoadingText
    }
    return this.store.emptyText
  }

  // 是否展示分页
  get showPagination() {
    const total = this.pagination.total;
    // const limit = this.pagination.limit;
    
    const { tableHeader, showPagination, tableData } = this.store
    // 如果没有表头 || 没有列表数据 不展示分页
    if (!tableHeader.length || !tableData.length || !total) return false
    // 如果不足一页也不展示分页
    // if( total / limit < 1) return false
    // 否则根据用户传入的prop配置 设置是否展示
    return showPagination
  }

  // 监听Props 发生变化，重新设置 Table store 状态
  onWatchProps() {
    
    this.table.$watch(
      () => this.table.$props,
      (newValue) => {
        let newProp = newValue
        let oldProp = this.oldTableProp
        // 如果是利用请求函数获取数据 ， 有些数据在内部处理，无需监听prop
        if(this.table.requestFn)  {
          const remove = ["tableData","total","showPagination","requestFn"]
          newProp = _.omit(newProp,remove)
          oldProp = oldProp?_.omit(oldProp,remove):{}
        }
      
        // 对比新旧值
        if(!_.isEqual(newProp,oldProp)) {
          this.oldTableProp = _.cloneDeep(newValue)
          if(!this.table.requestFn) this.setStore();
        }

      },
      {
        deep: true,
      }
    );
  }


  // 根据用户传入的 fetch 请求函数，进行列表数据获取
  async getList() {
    const fetch = this.store.requestFn
    if (!fetch) return
    
    // 显示加载loading
    this.toggleTableLoading()
    
    this.table.oConCationContext?.disabledMainScroll()

    // 回到表格 吸顶的位置
    const isStick = this.table?.oConCationContext?.backToTableHeaderStickPosition()

    const params = apiParamsFilterNull({ ..._.omit(this.requestParams,["noBackLeaveScrollPosition"]) },this.oldTableProp.deleteNullApiParams)

    try {
      const data = await fetch(params)

      const list = data?.list || []
      const total = data?.total || 0

      let tableHeader = data?.tableHeader || this.table.tableHeader
      this.store.isDynamicTableHeader = "tableHeader" in data 

      // 如果当前数据为空 ， 并且当前页面不是第一页， 再自动跳转到上一页再进行数据获取
      if(!list.length && this.requestParams.start!==1 ) {
        this.pagination.currentPage--
        this.requestParams.start = this.pagination.currentPage
        await this.getList()
        return
      }
      
      if(tableHeader) this.setTableHeader(tableHeader)


      if(list) this.setTableData(list)

      // 设置分页总条数
      this.pagination.total = total

      // 如果用户点击的不是排序按钮 并且 是吸顶状态
      if(!this.requestParams.filters?.noBackLeaveScrollPosition && isStick && this.table.isToLastScrollPosition) {
        // 回到离开页面时候的位置
        this.table?.oConCationContext?.backLeaveScrollPosition()
      }
      
      // 移除用户点击 排序的标识，用户点击排序回到表头吸顶位置，当点击别的查询条件查询时 ，就可以设置滚动条滚动上次历史位置
      delete this.requestParams.filters?.noBackLeaveScrollPosition

      // 重新计算操作按钮栏宽度
      this.table.$nextTick(()=>this.table.getActionButtonColumnWidth())

      setTimeout(() => this.table?.$emit("updateTableHeaderRightFixed"));

      setTimeout(() => this.table.oConCationContext?.enableMainScroll() , TABLE_CONFIG.loadingCloseDelay);

    } catch(err){
      console.error(err)
    }finally {
      if(this.table.tableHeader.length) this.setTableHeader(this.table.tableHeader)
      // 异常 或者 成功 关闭loading
      await this.toggleTableLoading(false)
    }
  }

  // 设置请求参数
  async setRequestParams(params,currentPage = 1){

    this.pagination.currentPage = currentPage

    // 空值请求参数
    params = _.pickBy(params,value=>!_.isNil(value))

    const withDisabled =  params.withDisabled === undefined ? true : params.withDisabled
    const withDeleted =  params.withDeleted === undefined ? true : params.withDeleted
    const withTotal =  params.withTotal === undefined ? true : params.withTotal

    delete params.withDeleted
    delete params.withDeleted
    delete params.withTotal

    delete params.withDeleted
    delete params.withDeleted
    delete params.withTotal
    
    this.requestParams = { 
      // 如果查询字段为空 去掉改字段
      filters: {
        ...params,
      },
      // 过滤分页数据
      ..._.omit(this.pagination,["layout","pageSize","total","currentPage"]), 
      start:currentPage,
      withDisabled,
      withDeleted,
      withTotal,
    }
    

    // 设置排序数据
    if(this.table.tableSortData.length) {
      this.requestParams["sorts"] = this.table.tableSortData
    }else{
      // 没有排序 删除sort 字段 
      delete this.requestParams["sorts"]
    }

    await this.getList()
    // await this.getListDebounce()
  }

  async appendRequestParams (params,currentPage=1){
    await this.setRequestParams({
      ...this.requestParams?.filters,
      ...params
    },currentPage)
  }

  // 设置分页参数
  async setPaginationParams(){

    Object.assign(this.requestParams,{
      start:this.pagination.currentPage,
      limit:this.pagination.limit,
      filters:{
        ...this.requestParams.filters,
        noBackLeaveScrollPosition:true
      }
    })

    await this.getList()
    // await this.getListDebounce()
  }


  getPageSizeCache (){
    const path = this.table.$route?.path
    const size = getCache(TABLE_CONFIG.pageSizeCachePrefix+path)
    if(size) this.pagination.limit = size
  }

  // 设置分页缓存
  setPageSizeCache(value){
    const path = this.table.$route?.path
    setCache(TABLE_CONFIG.pageSizeCachePrefix+path,value)
  }

  // tableLoading 加载时长 （从开始到结束）
  get oTableLoadingDuration() {
    return Date.now() - this.tableLoadingStartTime
  }

  toggleTableLoading(show = true) {
    if (!show) {
      // 如果 关闭loading 大于 300 毫秒，直接关闭loading 动画即可
      if (this.oTableLoadingDuration > TABLE_CONFIG.loadingCloseDelay) {
        if(this.store.loadingTimer) clearTimeout(this.store.loadingTimer)
        // 延时关闭
        this.store.loadingTimer = setTimeout(() => {
          this.store.oTableLoading = false
        }, TABLE_CONFIG.loadingCloseDelay);
      } else {
        // 如果 关闭loading < 300 毫秒，等待300 毫秒以后 再关闭loading， 避免页面 loading 闪动
        return new Promise((resolve,reject)=>{
          if(this.store.loadingTimer) {
            resolve(false)
            clearTimeout(this.store.loadingTimer)
          }
          this.store.loadingTimer = setTimeout(() =>{
            this.store.oTableLoading = show
            resolve(show)
          }, TABLE_CONFIG.loadingCloseDelay);
        })
      }
      return
    } else {
      this.store.oTableLoading = show
      this.tableLoadingStartTime = Date.now()
    }
    return show
  }

  getParsePath2Value (data,path){
    return parsePath2Value(data,path,TABLE_CONFIG.tableNotValue)
  }

  getTableRowValue (scope,tableHeaderItem){
    const { row } = scope
    const { prop,type } = tableHeaderItem
    let value = this.getParsePath2Value(row,prop)
    if(type==="IGNORE") return ""
    if(value === TABLE_CONFIG.tableNotValue && type!=="INDEX" ) return value
    if(TABLE_HEADER_TYPE_CONFIG[type]?.value) return TABLE_HEADER_TYPE_CONFIG[type].value(value,tableHeaderItem,scope)
    return value
  }

  setStore() {
    this.store = { ...this.store, ..._.cloneDeep(_.omit(this.table.$props, ["tableData","tableHeader","tableHeaderActionButtons"])) }
    this.pagination.total = this.table.$props.total
    if(!this.table.requestFn) this.setTableHeader(this.table.$props.tableHeader)
    // 这里通过调用自己的方法 设置数据，方便后期做一些 数据处理等操作
    this.setTableData(this.table.$props.tableData)
    // this.setTableHeader(this.table.$props.tableHeader)
    this.setTableHeaderActionButtons(this.table.$props.tableHeaderActionButtons)

  }

  setTableHeader(tableHeader) {
    if(!Array.isArray(tableHeader)) throw "tableHeader传入的值不合法"
    this.table.beforeSetTableHeader && this.table.beforeSetTableHeader(tableHeader)
    for(let item of tableHeader) {
      // 如果用户没有设置表格宽度 , 并且有设置表头类型， 尝试读取type的宽度
      this.setTableItemProp(item)
    }
    this.store.tableHeader = tableHeader
  }

  setTableItemProp(tableHeaderItem){
    const tableHeaderTypeData = _.omit(TABLE_HEADER_TYPE_CONFIG[tableHeaderItem.type] || {} , ["labelWidth","value"])
    // 如果用户没有设置表格宽度 , 并且有设置表头类型， 尝试读取type的宽度
    if(!tableHeaderItem.width && tableHeaderItem.type) this.setTableHeaderWidth(tableHeaderItem)
    Object.assign(tableHeaderItem,tableHeaderTypeData)
  }

  setTableHeaderWidth (tableHeaderItem){
    const data = TABLE_HEADER_TYPE_CONFIG[tableHeaderItem.type]
    tableHeaderItem.width = data?.labelWidth || ""
  }

  setTableData(tableData) {
    if(!Array.isArray(tableData) ) throw "tableData传入的值不合法"

    this.table.beforeSetTableData && this.table.beforeSetTableData(tableData)

    this.store.tableData = tableData
    
    setTimeout(() => {
      // 绑定监听事件 - table滚动发生变化
      this.table.bindElTableBodyScroll()
      // 操作按钮右侧浮动
      if(tableData.length) this.store.actionFixRight = "right"
      this.table.getActionButtonColumnWidth()
    }, 100);

    setTimeout(() => {
      this.table.setVirtualScrollWidth()
    }, 600);

  }

  setTableHeaderActionButtons(tableHeaderActionButtons){

    const ifShow = (item)=> {
      if(!("ifShow" in item)) return true
      if(_.isBoolean(item.ifShow)) return !!item.ifShow
      if(_.isFunction(item.ifShow)) return !!item.ifShow()
      return false
    }
    
    this.store.tableHeaderActionButtons = tableHeaderActionButtons.filter(tableActionButtonItem=>{
      const options = tableActionButtonItem.options

      if(!ifShow(tableActionButtonItem)) return false

      // 如果用户没有设置options 选项可能 就是个普通的点击按钮
      if(!options) return true

      tableActionButtonItem.options = options.filter(ifShow)

      return !!options.length
    })

  }
}

