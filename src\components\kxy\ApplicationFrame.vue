<template>
  <div class="o-kxy-app-frame" v-if="showMenu">
    <div class="o-kxy-app-frame_nav">
    </div>
    <div class="o-kxy-app-frame_layout">
      <div class="o-kxy-app-frame_layout_menu">
        <main-menu
          v-loading="isLoading"
          :title="mainMenuTitle"
          :slideMenu="slideMenuTree"
          @openURL="insertTabMenuData"
        />
      </div>
      <div class="o-kxy-app-frame_layout_content">
        <TabNavigation ref="tabNavigation" @openURL="openPage" :slideMenu="slideMenuTree" />
        <div
          class="o-kxy-app-frame_layout_content_wrapper"
          :style="{ background: isBackground ? '#FFFFFF' : 'none' }"
        >
          <keep-alive>
            <router-view v-if="$route.meta.keepAlive" />
          </keep-alive>
          <router-view v-if="!$route.meta.keepAlive" />
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <router-view />
  </div>
</template>

<script lang="ts">

import { Component, Inject, Vue } from "vue-property-decorator";
import TabNavigation from "./tabNavigation.vue";
import { getAppCodeCache, } from "../../util/desktop";
import MainMenu from "./MainMenu.vue";
import { functionMenu } from "../../util";
import { ensureLeadingSlash } from "../common/util";

function traverseTree(tree, parentId = null) {
  tree.forEach((node, index) => {
    const nodeId = `${parentId !== null ? parentId + '-' : ''}${index }`;

    // 在当前节点上添加 id 属性
    node.id = nodeId;

    // 如果当前节点有子节点，递归遍历子节点
    if (node.children && node.children.length > 0) {
      traverseTree(node.children, nodeId as any);
    }
  });
}

@Component({
  components: {
    MainMenu,
    TabNavigation
  },
})
export default class ApplicationFrame extends Vue {
  @Inject("__app_forbidden__") readonly forbidden!: any;

  private isLoading: boolean = false;
  private mainMenuTitle: string = "开薪易";
  private slideMenuTree:any = []

  get showMenu(){
    let meta = this.$route.meta || {};
    if(!("menu" in meta)) return true;
    return Boolean(meta.menu)
  }

  get isBackground (){
    let meta = this.$route.meta || {};
    if(!("isBackground" in meta)) return true;
    return Boolean(meta.isBackground)
  }

  created(){
    this.loadMenuTree()
  }

  async loadMenuTree(){

    if(typeof this.$app.loadMenus === "function"){
      this.isLoading = true
      this.slideMenuTree = await this.$app.loadMenus()
      this.isLoading = false
      return 
    }
    
    if(this.$app.menus.length) {
      await this.$nextTick()
      traverseTree(this.$app.menus)
      this.slideMenuTree = this.$app.menus
      return 
    }

    this.isLoading = true
    const menuData = await functionMenu(getAppCodeCache())
    this.mainMenuTitle = menuData.title
    traverseTree(menuData.children)
    this.slideMenuTree = menuData.children;
    this.isLoading = false
  }

  insertTabMenuData(menu){
    (this.$refs.tabNavigation as any).insertTabMenuData(menu);
    this.openPage(menu)
  }

  openPage(menu){
    const appCode = getAppCodeCache()
    const hrefUrl = `${menu.routePath}?app=${appCode}`
    const currentBaseURL = window.location.origin + this.$router.options.base
    const routePath = ensureLeadingSlash(menu.routePath.replace(currentBaseURL,"")  )
    const routers = this.$router.options.routes || []

    // 是否存在当前项目 
    const isCurrentProjectPath = routers.some(item=>{
      const fmtPath = routePath
      if(item.children) {
        for(let it of item.children) {
          if(it.path === fmtPath) return true
        }
      }
      return item.path === routePath
    })

    // 如果存在当前项目 用router.push 跳转
    if(isCurrentProjectPath && routePath) {
      if(routePath === this.$route.path) return 
      this.$router.push({
        path:routePath,
        query:{
          app:getAppCodeCache()
        }
      })
      return 
    }

    window.location.href = hrefUrl
  }
}
</script>

<style lang="less">
.o-kxy-app-frame {
  background: #f7f7f7;
  &_nav {
    position: relative;
    z-index: 99;
  }
  &_layout {
    display: flex;
    &_content {
      box-sizing: border-box;
      height: 100vh;
      flex: 1;
      overflow-y: scroll !important;
      display: flex;
      flex-direction: column;
      &_wrapper {
        flex: 1;
        margin-top: 20px;
        margin-left: 20px;
        overflow: auto;
        position: relative;
        background: #ffffff;
        box-shadow: 0 6px 12px -11px rgba(52, 61, 160, 0.16);
        border-radius: 4px 0px 0px 0px;
      }
    }
  }
}
</style>
