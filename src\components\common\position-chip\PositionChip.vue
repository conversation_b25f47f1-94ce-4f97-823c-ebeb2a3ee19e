<template>
  <o-chip
    :closable="closable"
    @close="$emit('close')"
    style="background-color: #f1f1f1"
  >
    <div style="display: flex; align-items: center">
      <o-avatar :size="25" :name="simpleName" style="margin: 0 3px 0 -7px" />
      <o-position class="member" :value="value" :change.sync="position" />
    </div>
  </o-chip>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class  PositionChip extends Vue {
  @Prop() value!: string;
  @Prop() closable!: any;

  get simpleName() {
    let v = (this.position?.name ?? "").substring(0, 1);
    return v;
  }

  position: any = null;
}
</script>
<style lang='less' scoped>
.position {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  font-size: 14px;
}
</style>