<template>
  <div v-loading="$app.loading">
    <div class="fm-container">
      <application-frame v-if="!$route.meta.keepAlive" />
    </div>
  </div>
</template>

<script lang="ts">
import ApplicationFrame from "./ApplicationFrame.vue";
import { Component, Vue } from "vue-property-decorator";

@Component({
  components: {
    ApplicationFrame,
  },
})
export default class ApplicationView extends Vue {}
</script>