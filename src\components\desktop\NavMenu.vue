<template>
  <div class="o-nav" id="o-nav" :class="['o-nav-' + theme]">
    <div class="o-nav_left" id="o-nav-left">
      <img :src="webConfig.platFromLogoUrl || webConfig.logo" />
    </div>
    <div class="o-nav_center" id="o-nav-center">
      <div
        class="o-nav_center_desc"
        v-if="sloganLock && (enterpriseList.length == 0 || !userInfo.merchant)"
      >
        一站式全场景人力资源科技服务平台
      </div>
      <!--      <template v-if="navigationList.length > 1 || userType === 'PERSONAL'">-->
      <template v-for="(item, index) in navigationList" v-if="index < navSize">
        <template v-if="item.children.length > 0">
          <div class="o-nav_center_item">
            <el-popover
              placement="bottom-start"
              width="auto"
              trigger="hover"
              popper-class="o-nav_popover"
            >
              <div
                class="webkit-scrollbar"
                style="
                  max-height: 400px;
                  overflow-y: scroll;
                  overflow-x: hidden;
                  padding-right: 10px;
                "
                :class="['o-nav-popover-' + theme]"
              >
                <template v-if="item.children.length > 0">
                  <!-- 分组start -->
                  <o-group
                    :list="item.children"
                    @on-select="openAppEvent"
                    v-if="item.groupName === 'HAVE'"
                  />
                  <!-- 分组end -->

                  <!-- 未分组 start -->
                  <o-tiling
                    :list="item.children"
                    @on-select="openAppEvent"
                    v-else
                  />
                  <!-- 未分组end -->
                </template>
              </div>
              <div slot="reference">
                {{ item.name
                }}<span
                  style="margin-left: 12px"
                  class="el-icon-arrow-down"
                ></span>
              </div>
            </el-popover>
          </div>
        </template>
        <!-- 判断当前选中的item与appCode是否相匹配 或者 当前event是打开工作台并且当前选中的是工作台，(主要是为了防止工作台的appCode为空) -->
        <div
          class="o-nav_center_item"
          :class="{
            'active-nav':
              currentNav === item.appCode ||
              (item.event === 'OPEN_WORKBENCH' && currentNav === 'WORKBENCH'),
          }"
          @click="openAppEvent(item)"
          v-else-if="
            (item.event === 'EXPAND_MENU' && item.children.length > 0) ||
            item.event !== 'EXPAND_MENU'
          "
        >
          {{ item.name }}
          <div
            :class="{
              'active-nav_line':
                currentNav === item.appCode ||
                (item.event === 'OPEN_WORKBENCH' && currentNav === 'WORKBENCH'),
            }"
          ></div>
        </div>
      </template>
      <!-- 收起到更多中的展示 -->
      <template v-if="navSize > 0 && navigationList.length > navSize">
        <div class="o-nav_center_item">
          <el-popover
            placement="bottom-end"
            width="auto"
            v-model="morePopover"
            trigger="click"
            popper-class="o-nav_popover o-nav_popover_more"
          >
            <div class="o-nav_center_item_more" slot="reference">
              <span>更多</span
              ><span
                :style="{ transform: morePopover ? 'rotateZ(-180deg)' : '' }"
                class="el-icon-arrow-down"
              ></span>
            </div>
            <div class="webkit-scrollbar" :class="['o-nav-popover-' + theme]">
              <!-- 只存在没有二级菜单显示 -->
              <template
                v-if="
                  moreType &&
                  moreType['NOT_NODE'] &&
                  !moreType['HAVE_GROUP'] &&
                  !moreType['HAVE_NODE']
                "
              >
                <o-tiling
                  :theme="theme"
                  :list="getMoreNav(this.navSize)"
                  @on-select="openAppEvent"
                />
              </template>
              <!-- 有二级或者有分组 -->
              <template v-else>
                <o-more-group
                  :theme="theme"
                  :refObj="$refs"
                  :list="getMoreNav(this.navSize)"
                  @on-select="openAppEvent"
                />
              </template>
            </div>
          </el-popover>
        </div>
      </template>
      <!--      </template>-->
    </div>
    <div class="o-nav_right" id="o-nav-right">
      <div v-if="enterpriseList.length > 0 && userInfo.merchant">
        <search-box
          :userId="userInfo.user.id"
          :merchantId="userInfo.merchant.id"
          ref="searchBox"
        />
      </div>
      <div
        v-if="
          enterpriseList.length > 0 && userInfo.merchant && !isWechatWorkEnv
        "
      >
        <quick-entry />
      </div>
      <div
        class="o-nav_right_msg"
        @click="toMessage"
        :style="{ marginRight: unReadMsgCount > 0 ? '24px' : '10px' }"
      >
        <el-badge
          :value="unReadMsgCount"
          :hidden="unReadMsgCount === 0"
          :max="99"
          class="o-nav_right_msg_count"
        >
          <span class="iconfont icon-o-nav-msg" />
        </el-badge>
      </div>
      <div class="o-nav_right_enterprise" v-if="isWechatWorkEnv">
        <div class="o-nav_right_enterprise_text" style="cursor: default">
          <span>
            {{
              userInfo.merchant &&
              (userInfo.merchant.introduction || userInfo.merchant.name)
            }}
          </span>
        </div>
      </div>
      <div
        class="o-nav_right_enterprise"
        v-if="
          enterpriseList.length > 0 && userInfo.merchant && !isWechatWorkEnv
        "
      >
        <template v-if="enterpriseList.length === 1">
          <template v-if="enterpriseInfo.auditStatus !== 'PASS'">
            <div class="o-nav_right_enterprise_text">
              <span>
                {{
                  userInfo.merchant &&
                  (userInfo.merchant.introduction || userInfo.merchant.name)
                }}
              </span>
            </div>
          </template>
        </template>
        <template v-else>
          <el-popover
            width="auto"
            placement="bottom-end"
            trigger="click"
            popper-class="o-nav_enterprise_popover"
            @show="showEnterprisePopover"
          >
            <!-- 切换企业弹出层内容start -->
            <div
              class="o-nav_enterprise_popover_wrapper"
              :class="['o-nav-popover-' + theme]"
            >
              <ul
                class="o-nav_right_enterprise_list webkit-scrollbar"
                id="ul"
                style="border-bottom: none"
                v-loading="enterpriseLoading"
              >
                <li
                  :class="{
                    active: isActiveEnterprise(item.merchant, userInfo),
                  }"
                  v-for="(item, index) in enterpriseList"
                  :id="'enterprise-' + item.merchant.id"
                  v-if="item.disabled == false"
                  @click="changeEnterprise(item)"
                  :data-index="index"
                  :key="index"
                >
                  <span>{{
                    item.merchant.introduction || item.merchant.name
                  }}</span>
                  <span
                    v-if="isActiveEnterprise(item.merchant, userInfo)"
                    class="iconfont icon-hook"
                  ></span>
                </li>
              </ul>
              <!--                        <div class="o-nav_right_enterprise_establish">-->
              <!--                            <span class="iconfont icon-add"></span>-->
              <!--                            <span>创建企业</span>-->
              <!--                        </div>-->
            </div>
            <!-- 切换企业弹出层内容end -->

            <!-- 切换企业 -->
            <div class="o-nav_right_enterprise_text" slot="reference">
              <span v-if="userType === 'PERSONAL'">{{
                userProfile.realName
              }}</span>
              <span v-else>{{
                userInfo.merchant.introduction || userInfo.merchant.name
              }}</span>
              <span class="iconfont icon-jiantou_zuoyouqiehuan"></span>
            </div>
          </el-popover>
        </template>
      </div>
      <!-- 认证状态：需为企业账户才显示 -->
      <template v-if="enterpriseInfo.type === 'ENTERPRISE'">
        <!-- 只显示未认证或者审核中的状态 -->
        <div
          class="o-nav_right_auth"
          @click="toAudit(enterpriseInfo.auditStatus)"
          v-if="enterpriseInfo.auditStatus !== 'PASS'"
        >
          {{ enterpriseInfo.auditStatus === "WAIT" ? "审核中" : "未认证" }}
        </div>
      </template>
      <div class="o-nav_right_user" v-if="isWechatWorkEnv">
        wechat
      </div>
      <div class="o-nav_right_user" v-if="!isWechatWorkEnv">
        <el-popover
          width="auto"
          trigger="hover"
          popper-class="o-nav_user_popover"
        >
          <!-- 用户气泡弹出层 -->
          <div
            class="o-nav_right_user_popover"
            :class="['o-nav-popover-' + theme]"
          >
            <!-- 用户头像区域 -->
            <div class="o-nav_right_user_popover_info">
              <!-- 头像 -->
              <div class="user_logo">
                头像
              </div>
              <!-- 用户名称 -->
              <div class="user_name">
                <div>
                  {{
                    userInfo.user.realName ||
                    formatMobile(userProfile.cellphone)
                  }}
                </div>
                <div
                  :class="{
                    unauthorized: !(
                      userInfo.user.isAuth || userProfile.hasAuth
                    ),
                  }"
                >
                  <span
                    class="iconfont"
                    :class="[
                      userInfo.user.isAuth || userProfile.hasAuth
                        ? 'icon-auth'
                        : 'icon-unauthorized',
                    ]"
                  ></span>
                  <span>{{
                    userInfo.user.isAuth || userProfile.hasAuth
                      ? "已认证"
                      : "未认证"
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 用户下拉选项 -->
            <ul class="o-nav_right_user_popover_option">
              <li
                @click="toAccountManagement"
                v-if="userProfile && userProfile.userType === 'PERSONAL'"
              >
                <span class="iconfont icon-account"></span>
                <span>账号管理</span>
                <span class="iconfont icon-arrow_right"></span>
              </li>
              <li @click="toMyEnterprise">
                <span class="iconfont icon-enterprise"></span>
                <span>我的企业</span>
                <span class="iconfont icon-arrow_right"></span>
              </li>
              <li
                @click="toWebSite"
                v-if="
                  serverEnv !== 'cgb' &&
                  serverEnv !== 'boc' &&
                  webConfig &&
                  !webConfig.entranceEnable
                "
              >
                <span class="iconfont icon-website"></span>
                <span>进入官网</span>
                <span class="iconfont icon-arrow_right"></span>
              </li>
              <li @click="visible = true">
                <span class="iconfont icon-signout"></span>
                <span>退出登录</span>
              </li>
            </ul>
          </div>
          <div class="o-nav_right_user_logo" slot="reference">
            <span>
             right logo
            </span>
            <span class="el-icon-arrow-down"></span>
          </div>
        </el-popover>
      </div>
    </div>
    <o-logout
      :visible="visible"
      :theme="theme"
      @on-close="visible = false"
      @on-confirm="logout"
    />
    <o-change-enterprise-dialog
      :type="changeDialogType"
      :theme="theme"
      :visible="changeEnterpriseVisible"
      @on-confirm="changeEnterpriseConfirm"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import QuickEntry from "./quick-entry/index.vue";
import SearchBox from "./search-box/index.vue";
import { Menu } from "../../model";
import { isWechatWorkEnv } from "../../util/wechat";
// const isWechatWorkEnv = true;
import {
  changeMerchant,
  Enterprise,
  getAppDefaultPage,
  getAppMenu,
  getMessage,
  getToken,
  removeCache,
  saveToken,
  toLoginPage,
  toNotAuthPage,
  UserInfo,
} from "../../util";
import {
  AuditType,
  CacheKey,
  ChangeTokenType,
  EventType,
  OpenType,
  SystemKey,
  UserType,
} from "../../enums/nav";
import OTiling from "./popover/Tiling.vue";
import OGroup from "./popover/Group.vue";
import OMoreGroup from "./popover/MoreGroup.vue";
import OLogout from "./popover/Logout.vue";
import { removeToken } from "../../util/token";
import jwtDecode from "jwt-decode";
import {
  filterHasAuthMenu,
  filterMenu,
  filterNavClassify,
  getPagePath,
  getSsoPath,
  MenuContext,
  mosaicMobile,
  Navigate,
  NavigationResolver,
  pageAdapter,
  removeNavCache,
  removeRoleCache,
  toWorkBenchPage,
} from "../../util/desktop";
import { Navigation } from "@/model/type";
import OChangeEnterpriseDialog from "./popover/ChangeEnterpriseDialog.vue";

@Component({
  name: "o-nav-menu",
  components: {
    OChangeEnterpriseDialog,
    OMoreGroup,
    OGroup,
    OTiling,
    OLogout,
    QuickEntry,
    SearchBox,
  },
})
export default class NavMenu extends Vue {
  private isWechatWorkEnv: boolean = isWechatWorkEnv;
  private show: boolean = true;

  //标语状态锁（作用为数据加载过程中，防止显示抖动问题）
  private sloganLock: boolean = false;

  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  /**
   * 导航数据
   */
  @Prop()
  private navigation!: Navigation[];

  /**
   * 用户信息
   */
  @Prop()
  private userInfo!: UserInfo;

  /**
   * 企业列表
   */
  @Prop()
  private enterpriseList!: Enterprise[];

  /**
   * 企业列表加载状态
   */
  @Prop({
    default: false,
  })
  private enterpriseLoading?: boolean;

  /**
   * 对于原有profile接口数据
   */
  @Prop()
  private enterpriseInfo?: any;

  /**
   * 企业logo
   */
  @Prop()
  private logo?: string;

  /**
   * web配置
   */
  @Prop()
  private webConfig?: any;

  /**
   * 用户概述
   */
  @Prop()
  private userProfile?: any;

  /**
   * 已开通应用
   */
  @Prop()
  private openApp?: string[];

  /**
   * 是否管理员
   */
  @Prop({
    default: false,
  })
  private isManager?: boolean;

  /**
   * @description 未开通显示规则
   */
  @Prop()
  private notOpenDisplayRules;

  /**
   * @description 当前选中菜单
   */
  @Prop()
  private currentNav?: string;

  /**
   * @description 权限(用于判断当前菜单过滤使用)
   */
  @Prop()
  private roles?: string[];

  /**
   * @description 用户类型
   */
  @Prop({
    default: UserType.ENTERPRISE,
  })
  private userType?: UserType;

  /**
   * @description 有权限的菜单
   */
  @Prop()
  private hasAuthMenuSizeMap?: any;

  /**
   * @description 带应用code
   */
  @Prop()
  private appMenuList?: any;

  /**
   * @description: 页面展示所有，当前数据经过init方法处理后所得
   */
  private navigationList: Navigation[] = [];

  /**
   * 未读消息条数
   */
  private unReadMsgCount: number = 0;

  /**
   * 激活的企业的id（主要用于切换立马选中效果，提升用户感知）
   */
  private activeMerchantId = "";

  /**
   * 切换企业提示dialog
   */
  private changeEnterpriseVisible = false;

  /**
   * 更多展开状态
   */
  private morePopover = false;

  /**
   * 对应env.js中的server_env，用于环境切换使用
   */
  private serverEnv = (window as any).env.server_env;

  /**
   * 导航最多显示多少个，0代表不限制，大于0代表限制多少个
   */
  private navSize: number = 0;

  /**
   * 更多展示类型
   */
  private moreType: any = {};

  /**
   * 退出登陆提示
   */
  private visible: boolean = false;

  /**
   * 存储当前token
   */
  private token = getToken();

  /**
   * 弹窗提示类型
   */
  private changeDialogType = ChangeTokenType.MERCHANT;

  /**
   * 搜索框
   */
  private $searchBox: any;

  /**
   * @description 选中企业
   * @param merchant：当前企业信息
   */
  private isActiveEnterprise(merchantItem, userInfo) {
    if (this.userType === "PERSONAL") {
      return merchantItem.userId == (userInfo.user as any).id;
    } else {
      return merchantItem.id == userInfo.merchant.id;
    }
  }

  /**
   * @description 监听navigation数据
   */
  @Watch("navigation")
  private watchNavigation(navigation: Navigation): void {
    this.init();
  }

  private formatMobile(phone) {
    return mosaicMobile(phone);
  }

  /**
   * @description 点击导航触发动作
   */
  private openAppEvent(app): void {
    //关闭下拉选项
    this.morePopover = false;
    //清除菜单id选中
    removeCache(CacheKey.MIDKEY);

    new Navigate(this)
      .setRoles(this.roles || [])
      .setManager(this.isManager || false)
      .setOpenApp(this.openApp || [])
      .openAppEvent(app);

    //打开工作台
    // if (app.event === EventType.OPEN_WORKBENCH) {
    //   Navigate.push({
    //     systemKey: SystemKey.SSO,
    //     path: "/home?app=WORKBENCH",
    //     newTab: app.openType === OpenType.NEWTAB
    //   });
    //   return;
    // }
    //
    // //打开外部链接
    // if (app.event === EventType.OPEN_URL) {
    //   this.openURL(app);
    //   return;
    // }
    //
    // //打开应用
    // if (app.event === EventType.OPEN_APP) {
    //   this.openApplication(app);
    //   return;
    // }
    //
    // //打开应用中的菜单
    // if (app.event === EventType.OPEN_APP_MENU) {
    //   this.openAppMenu(app);
    // }
  }

  /**
   * @description 打开外部链接
   * @param app 当前节点信息
   */
  private openURL(app): void {
    //当前页面打开
    if (app.openType === OpenType.CURRENT_TAB) {
      window.location.href = app.url;
    } else if (app.openType === OpenType.NEWTAB) {
      //新窗口打开
      window.open(app.url);
    }
  }

  /**
   * @description 打开应用中的菜单
   * @param app 应用信息
   */
  private async openAppMenu(app) {
    //获取appMenuInfo对象
    let appMenuInfo = app.appMenuInfo;
    getAppMenu(appMenuInfo.appCode).then((menu: Menu[]) => {
      let menus = filterMenu(
        menu,
        this.roles || [],
        this.openApp || [],
        this.isManager || false
      );
      let m = this.findMenuById(menus, appMenuInfo.appMenuId);
      //若当前是一级菜单，则打开有权限的第一个
      if (m.children.length > 0) {
        new MenuContext(this)
          .setNewTab(app.openType === OpenType.NEWTAB)
          .openPage(m.children[0]);
      } else if (m) {
        //否则打开当前菜单
        new MenuContext(this)
          .setNewTab(app.openType === OpenType.NEWTAB)
          .openPage(m);
      } else {
        //toast提示
        //new MenuContext(this).notAuthToast();
        toNotAuthPage(appMenuInfo.appCode);
      }
    });
  }

  /**
   * @description 通过id查找菜单
   */
  private findMenuById(menus: Menu[], id: number) {
    let menu;
    for (let i = 0; i < menus.length; i++) {
      if (menus[i].id == id) {
        return menus[i];
      }
      if (menus[i].children && menus[i]?.children.length > 0) {
        menu = this.findMenuById(menus[i].children || [], id);
      }
      if (menu) {
        return menu;
      }
    }
  }

  /**
   * @description 打开应用
   * @param app 当前节点信息
   */
  private async openApplication(app) {
    let defaultPage = await this.getDefaultPage(app.appCode);
    //获取默认页
    let defaultPageInfo = defaultPage[0] || null;
    //判断是否有默认页
    if (defaultPageInfo) {
      //存在应用已开通并且开通有默认页
      if (defaultPageInfo.defaultPageOpen && app.isOpen) {
        let page = getPagePath(defaultPageInfo.defaultPageOpen);

        //进行跳转
        Navigate.push({
          systemKey: page.belongProject,
          path: page.path,
          params: {
            app: app.appCode,
          },
          newTab: app.openType === OpenType.NEWTAB,
        });
        return;
      }

      //存在应用未开通并且未开通有默认页
      if (defaultPageInfo.defaultPageNotOpen && !app.isOpen) {
        let page = getPagePath(defaultPageInfo.defaultPageNotOpen);
        //进行跳转
        Navigate.push({
          systemKey: page.belongProject,
          path: page.path,
          params: {
            app: app.appCode,
          },
          newTab: app.openType === OpenType.NEWTAB,
        });
        return;
      }
    }

    //否则获取有权限的菜单的二级菜单
    getAppMenu(app.appCode).then((menu: Menu[]) => {
      let menus = filterMenu(
        menu,
        this.roles || [],
        this.openApp || [],
        this.isManager || false
      );
      let hasAuthMenu: Menu = filterHasAuthMenu(menus);
      if (hasAuthMenu && hasAuthMenu.belongProject && hasAuthMenu.routePath) {
        new MenuContext(this)
          .setNewTab(app.openType === OpenType.NEWTAB)
          .openPage(hasAuthMenu);
      } else {
        //toast提示
        // new MenuContext(this).notAuthToast();
        toNotAuthPage(app.appCode);
      }
    });
  }

  /**
   * @description 获取默认页信息
   * @param appCode
   */
  private async getDefaultPage(appCode: string) {
    return getAppDefaultPage(appCode);
  }

  private showEnterprisePopover(): void {
    let li = document.getElementById("enterprise-" + this.activeMerchantId);
    let index = Number(li?.dataset.index);
    if (li && index > 5) {
      setTimeout(() => {
        li?.scrollIntoView({
          behavior: "smooth",
        });
      }, 50);
    }
  }

  /**
   * @description 获取某个节点开始后都所有数据
   * @param index 索引
   * @return Array
   */
  private getMoreNav(index) {
    return this.navigationList.filter((item, i) => i >= index);
  }

  /**
   * @description 未认证、审核中点击触发逻辑
   */
  private async toAudit(auditStatus) {
    let merchantMember: any = this.userInfo.merchantMember;
    if (merchantMember && !merchantMember.isAdmin) {
      return false;
    }
    let ssoPath = await getSsoPath();
    //审核中
    if (auditStatus === AuditType.WAIT) {
      Navigate.push({
        systemKey: SystemKey.SSO,
        path: "/companyInformation?app=BASIC_FUNCTION",
        $router: this.$router,
      });
    } else {
      //未认证、未通过状态
      Navigate.push({
        systemKey: SystemKey.SSO,
        path: "/companyAuthentication?app=BASIC_FUNCTION",
        $router: this.$router,
      });
    }
  }

  /**
   * @description 初始化方法
   */
  private init(): void {
    this.navigationList = [];
    if (this.userType === UserType.PERSONAL) {
      this.navSize = 2;
      // this.navigationList.push(...this.navigation);
      this.sloganLock = true;
    } else {
      //设置选中
      this.activeMerchantId = this.userInfo.merchant["id"];
      const navigation = this.navigation;
      if (navigation.length > 0) {
        setTimeout(() => {
          this.$searchBox = this.$refs.searchBox;
          // this.navSize = pageAdapter();
          //导航解析对象
          let resolverConfig = {
            appMenuList: this.appMenuList,
            openApp: this.openApp || [],
            isManager: this.isManager || false,
            notOpenDisplayRules: this.notOpenDisplayRules,
            hasAuthMenuMap: this.hasAuthMenuSizeMap,
            roles: this.roles || [],
          };
          debugger
          //调用导航解析器
          this.navigationList = new NavigationResolver(resolverConfig)
            .setData(navigation)
            .resolver();
          setTimeout(() => {
            this.navSize = pageAdapter(this.navigationList);
          }, 50);
          this.$searchBox.init(resolverConfig, this.navigationList);
          this.moreType = filterNavClassify(this.navigationList, this.navSize);
        }, 50);
      } else {
        console.error("未接收到导航数据！");
      }
    }
  }

  /**
   * @description 浏览器resize操作
   */
  private resize(): void {
    window.addEventListener("resize", () => {
      this.morePopover = false;
      this.navSize = pageAdapter(this.navigationList);
      this.moreType = filterNavClassify(this.navigationList, this.navSize);
    });
  }

  /**
   * @description 监听storage变化
   */
  private listenStorage() {
    window.addEventListener("storage", (data: any) => {
      if (data.key === "change-enterprise") {
        this.listenChangeToken();
        // this.changeEnterpriseVisible = true;
        // removeNavCache();
        //派发切换企业通知
        // this.$emit("on-change-enterprise");
      }
    });
  }

  /**
   * @description 监听token变化
   */
  private listenChangeToken() {
    //获取当前token
    let currentToken = getToken();
    //两个token对比一致，不进行操作
    if (currentToken === this.token) {
      return;
    }
    //判断是否有token
    if (currentToken) {
      //解密原token
      const decodeOldToken: any = jwtDecode(this.token);
      //解密当前token
      const decodeCurrentToken: any = jwtDecode(currentToken);
      //判断当前是否是当前用户
      if (decodeOldToken.o === decodeCurrentToken.o) {
        //当前企业id已变化
        if (decodeCurrentToken.p.m !== decodeOldToken.p.m) {
          this.changeEnterpriseVisible = true;
          this.changeDialogType = ChangeTokenType.MERCHANT;
        } else {
          //当前企业未变化
          //关闭弹窗
          this.changeEnterpriseVisible = false;
        }
      } else {
        //已切换了用户
        this.changeEnterpriseVisible = true;
        this.changeDialogType = ChangeTokenType.USER;
      }
    }
  }
  /**
   * @description 监听浏览器tab切换事件
   */
  private listenTabChange() {
    const token = getToken();
    var vibchage =
      "visibilitychange" || "webkitvisibilitychange" || "mozvisibilitychange";
    document.addEventListener(vibchage, () => {
      //激活状态
      if ((document as any).visibilityState === "visible") {
        this.listenChangeToken();
        // let currentToken = getToken();
        // if (token !== currentToken) {
        //   this.changeEnterpriseVisible = true;
        //   removeNavCache();
        //   //派发切换企业通知
        //   // this.$emit("on-change-enterprise");
        // }
      }
    });
  }

  /**
   * @description 切换企业弹窗确认
   */
  private changeEnterpriseConfirm() {
    removeNavCache();
    this.changeEnterpriseVisible = false;
    toWorkBenchPage();
  }

  /**
   * @description 获取未读消息(此处单独请求，不占用导航与菜单渲染时间)
   */
  private unReadMessage(): void {
    getMessage().then((data) => {
      //注意：此处需对数据进行过滤，因传参数到后端，实际未起到作用
      this.unReadMsgCount = data.list.filter(
        (item) => item.read === false
      ).length;
    });
  }

  /**
   * @description 切换企业
   * @param enterprise
   */
  private changeEnterprise(merchant): void {
    let merchantId = merchant.merchantId;
    this.activeMerchantId = merchant.merchantId || null;
    changeMerchant({
      merchantId: merchantId,
      userPc: true,
    }).then((data) => {
      //切换企业，删除token
      removeRoleCache();
      //删除导航相关缓存
      removeNavCache();
      //设置token
      this.$app.setToken(data);
      //切换企业设置localStorage，页面进行监听
      localStorage.setItem(
        "change-enterprise",
        JSON.stringify({ change: new Date().getTime() })
      );
      //派发事件
      this.$app.option.listener && this.$app.option.listener(this.$app);
      //跳转到工作台
      toWorkBenchPage();
    });
  }

  /**
   * @description 跳转到公司官网
   */
  private toWebSite(): void {
    window.open("https://www.olading.com");
  }

  /**
   * @description 跳转到我的企业
   */
  private async toMyEnterprise() {
    Navigate.push({
      systemKey: SystemKey.SSO,
      path: `/my-enterprise?app=WORKBENCH`,
      $router: this.$router,
    });
  }

  /**
   * @description 跳转到账号管理
   */
  private async toAccountManagement() {
    Navigate.push({
      systemKey: SystemKey.SSO,
      path: "/personal-management?app=WORKBENCH",
      $router: this.$router,
    });
  }

  private async toMessage() {
    Navigate.push({
      systemKey: SystemKey.SSO,
      path: "/notice-management?app=WORKBENCH",
      $router: this.$router,
    });
  }

  /**
   * @description 退出登录
   */
  private logout(): void {
    saveToken("");
    //删除token
    removeToken();
    //删除其他缓存
    removeNavCache();
    this.visible = false;
    //派发事件
    this.$app.option.logoutListener &&
      this.$app.option.logoutListener(this.$app);
    setTimeout(() => {
      Navigate.push({
        systemKey: SystemKey.SSO,
        replace: true,
        path: "/login",
      });
    }, 200);
  }

  private mounted() {
    this.$searchBox = this.$refs.searchBox;
  }

  public created(): void {
    this.init();
    //获取未读消息
    this.unReadMessage();
    this.resize();
    this.listenStorage();
    this.listenTabChange();
    //监听loadMsg事件，加载消息
    window.addEventListener("loadMsg", (e) => {
      this.unReadMessage();
    });
    this.$nextTick(() => {});
  }
}
</script>

<style lang="less">
/*******************  红色主题start  ***********************/
.o-nav-red {
  .o-nav_center_item:hover {
    color: #c81930;
  }
  .o-nav_right_msg:hover {
    background: #fbf0f1 !important;
  }

  .active-nav {
    color: #c81930 !important;
    &_line {
      position: absolute;
      width: 100%;
      height: 2px;
      background: #be0018 !important;
      border-radius: 2px;
      bottom: -22px;
    }
  }

  //消息hover
  .o-nav_right_enterprise_text:hover {
    background: #fcf2f2 !important;
  }
}

//弹出层样式
.o-nav-popover-red {
  .nav_item_hover:hover {
    background: #fcf2f2 !important;
    color: #c81930 !important;
  }

  .o-nav_right_enterprise_list {
    //当前loading中svg的圆圈颜色
    .el-loading-spinner .path {
      stroke: #c81930;
    }

    //企业列表下拉hover样式
    li:hover {
      background: #fcf2f2 !important;
    }

    .active {
      span {
        color: #c81930 !important;
      }
    }
  }

  .o-nav_right_enterprise_establish {
    span {
      color: #c81930 !important;
    }
  }

  //个人头像下拉hover
  .o-nav_right_user_popover_option li:hover {
    background: #fcf2f2;
  }
}

/*******************  红色主题end  ***********************/
.o-nav {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0px 24px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(83, 97, 255, 0.05);
  font-family: "PingFang SC", "PingFangSC-Medium", "PingFangSC-Regular",
    "微软雅黑", "Avenir", Helvetica, Arial, sans-serif;

  //左侧logo区域
  &_left {
    color: #6887f0;
    font-size: 30px;
    font-weight: bold;
    padding-right: 32px;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    img {
      height: 30px;
    }
  }

  //中间导航区域
  &_center {
    display: flex;
    // flex-shrink: 0;
    white-space:nowrap;
    flex: 1;
    font-size: 14px;
    height: 100%;
    align-items: center;
    &_desc {
      font-weight: 500;
      font-size: 14px;
      color: #6a6f7f;
      letter-spacing: 0;
      margin-right: 32px;
    }

    &_item {
      font-weight: 500;
      font-size: 16px;
      color: #46485a;
      letter-spacing: 0;
      margin-right: 40px;
      transition: all 0.5s;
      cursor: pointer;
      position: relative;
      &_text {
        display: flex;
        align-items: center;
        .down {
          margin-left: 12px;
        }
      }
      &:last-child {
        margin-right: 0px;
      }

      &:hover {
        color: #4f71ff;
      }

      //更多样式
      &_more {
        display: flex;
        align-items: center;
        /*&:hover {*/
        /*    span {*/
        /*        &:nth-child(2) {*/
        /*            transform: rotateZ(-180deg);*/
        /*        }*/
        /*    }*/
        /*}*/

        span {
          &:nth-child(2) {
            margin-left: 11px;
            font-size: 10px;
            transition: all 0.3s;
          }
        }
      }
    }

    .active-nav {
      color: #4f71ff;
      &_line {
        position: absolute;
        width: 100%;
        height: 2px;
        background: #4f71ff;
        border-radius: 2px;
        bottom: -22px;
      }
    }
  }

  //右侧区域
  &_right {
    /*flex: 1;*/
    display: flex;
    height: 32px;
    align-items: center;
    justify-content: flex-end;
    //消息
    &_msg {
      width: 30px;
      height: 30px;
      border-radius: 4px;
      text-align: center;
      line-height: 30px;
      cursor: pointer;
      position: relative;
      transition: all 0.3s;
      .is-fixed {
        top: 5px;
      }
      .el-badge__content {
        line-height: 16px;
        text-align: center;
        font-size: 10px;
        background: #f63a39 !important;
      }
      &:hover {
        background: #f7fafd;
      }

      &_count {
        height: auto !important;
        .el-badge__content {
          background: #d6342a;
        }
      }

      .icon-o-nav-msg {
        font-size: 18px;
        color: #6a6f7f;
      }
    }

    //企业
    &_enterprise {
      font-weight: 400;
      font-size: 14px;
      color: #6a6f7f;
      letter-spacing: 0;

      &_establish {
        padding-top: 20px;
        box-sizing: border-box;
        cursor: pointer;

        span {
          color: #4f71ff;

          &:nth-child(1) {
            font-size: 12px;
          }

          &:nth-child(2) {
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 0;
            margin-left: 8px;
          }
        }
      }

      &_list {
        padding-bottom: 7px;
        border-bottom: 1px solid #eaeaea;
        max-height: 200px;
        width: 200px;
        overflow-y: scroll;
        overflow-x: hidden;

        .active {
          span {
            &:nth-child(1) {
              color: #4f71ff;
              font-weight: 500;
            }
          }
        }

        li {
          height: 40px;
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
          color: #555555;
          letter-spacing: 0;
          padding-left: 8px;
          box-sizing: border-box;
          cursor: pointer;
          width: 196px;
          transition: all 0.5s;

          &:hover {
            background: #f7fafd;
            border-radius: 8px;
          }

          span {
            &:nth-child(1) {
              width: 140px;
              white-space: nowrap;
              overflow: hidden;
              text-align: left !important;
              text-overflow: ellipsis;
            }

            &:nth-child(2) {
              font-size: 12px;
              color: #4f71ff;
              margin-left: 24px;
            }
          }
        }
      }

      &_text {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 8px;
        color: #6a6f7f;
        border-radius: 4px;
        white-space: nowrap;

        &:hover {
          background: #f7fafd;
        }

        span {
          &:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 130px;
            margin-right: 9px;
          }

          &:last-child {
            font-size: 16px;
          }
        }
      }
    }

    &_auth {
      width: 54px;
      height: 24px;
      line-height: 24px;
      border: 1px solid #ff9b0e;
      border-radius: 4px;
      margin-left: 10px;
      font-weight: 500;
      font-size: 14px;
      color: #ff9b0e;
      text-align: center;
      cursor: pointer;
    }

    &_user {
      margin-left: 16px;
      //用户气泡弹出层
      &_popover {
        &_info {
          display: flex;
          width: 180px;
          padding-bottom: 12px;
          box-sizing: border-box;
          margin: 0 8px;
          border-bottom: solid 1px #f2f4f6;

          .user_logo {
            img {
              width: 32px;
              height: 32px;
            }
          }

          //用户头像名称区域
          .user_name {
            margin-left: 10px;

            .unauthorized {
              color: #ff9b0e !important;
            }

            div {
              &:nth-child(1) {
                height: 14px;
                font-weight: 500;
                font-size: 14px;
                color: #555555;
                line-height: 14px;
              }

              &:nth-child(2) {
                color: #41bd5a;
                height: 12px;
                display: flex;
                margin-top: 6px;
                align-items: center;

                span {
                  &:nth-child(1) {
                    font-size: 12px;
                  }

                  &:nth-child(2) {
                    font-size: 10px;
                    margin-left: 7px;
                  }
                }
              }
            }
          }
        }

        //用户下拉选项区域
        &_option {
          li {
            height: 40px;
            display: flex;
            align-items: center;
            cursor: pointer;

            &:hover {
              height: 40px;
              background: #f7fafd;
              border-radius: 8px;
              font-weight: 500;
            }

            span {
              color: #555555;

              &:nth-child(1) {
                margin-left: 8px;
                font-size: 20px;
              }

              &:nth-child(2) {
                margin-left: 7px;
                font-weight: 400;
                font-size: 14px;
                text-align: left !important;
                flex: 1;
              }

              &:nth-child(3) {
                margin-right: 8px;
                font-size: 12px;
              }
            }
          }
        }
      }

      //用户logo
      &_logo {
        display: flex;
        align-items: center;
        cursor: pointer;

        span {
          &:nth-child(1) {
            width: 32px;
            height: 32px;
            border-radius: 50px;
            display: block;
            margin-right: 10px;

            img {
              width: 32px;
              height: 32px;
            }
          }

          //右侧头像向下图标
          &:nth-child(2) {
            font-size: 18px;
          }
        }
      }
    }
  }

  //用户弹出层
  &_user_popover {
    padding: 16px 12px 10px 12px;
    box-sizing: border-box;
  }

  &_enterprise_popover {
    padding: 12px;
    box-sizing: border-box;

    &_wrapper {
    }
  }

  //弹出框组件自定义样式
  &_popover {
    padding: 20px 0px 20px 20px;
    /*max-width: 418px;*/
    box-sizing: border-box;
    //导航item 滑过状态
    .nav_item_hover:hover {
      background: #f7fafd;
      /*font-weight: 500;*/
    }

    &_more {
      padding: 12px 0px 12px 12px !important;
      .webkit-scrollbar {
        max-height: 400px;
        overflow-y: scroll;
        overflow-x: hidden;
        .o-nav_popover_wrapper {
          padding-right: 14px;
        }
      }
    }

    &_flex {
      display: flex;
      flex-wrap: nowrap;

      &_nowrap {
        flex-shrink: 0;

        .o-nav_popover_wrapper_item {
          &:last-child {
            margin-bottom: 0px;
          }
        }
      }
    }

    &_wrapper {
      //更多中的叶子节点
      &_leafnode {
        &_item {
          min-width: 134px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 0 12px;
          margin-bottom: 14px;
          color: #555555;

          &:last-child {
            margin-bottom: 0;
          }

          span {
            &:nth-child(1) {
              width: 24px;
              height: 24px;

              img {
                width: 24px;
                height: 24px;
                border-radius: 6px;
              }
            }

            &:nth-child(2) {
              margin-left: 10px;
              font-weight: 400;
              font-size: 14px;
              letter-spacing: 0;
            }
          }
        }
      }

      &_ul {
        &_item {
          height: 40px;
          display: flex;
          align-items: center;
          border-radius: 8px;
          cursor: pointer;
          min-width: 122px;
          padding: 0 8px;
          box-sizing: border-box;
          justify-content: space-between;
          color: #555555;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0px;
          }

          span {
            &:nth-child(1) {
              font-weight: 400;
              font-size: 14px;
              letter-spacing: 0;
            }

            &:nth-child(2) {
              font-size: 12px;
              transform: rotateZ(-90deg);
            }
          }
        }
      }

      //不分组样式
      &_item {
        display: flex;
        align-items: center;
        height: 40px;
        margin-bottom: 22px;
        box-sizing: border-box;
        /*margin-right: 40px;*/
        padding: 8px 22px 8px 8px;
        border-radius: 4px;
        min-width: 120px;
        cursor: pointer;
        transition: all 0.5s;

        &:nth-child(3n + 3) {
          margin-right: 0px;
        }

        &:nth-last-child(-n + 3) {
          /*margin-bottom: 0px;*/
        }

        div {
          &:first-child {
            width: 24px;
            height: 24px;
            margin-right: 10px;

            img {
              width: 24px;
              height: 24px;
              border-radius: 6px;
            }
          }

          &:last-child {
            /*width: 84px;*/
          }
        }
      }

      //分组样式
      &_group {
        //找到最后一个组节点
        &:last-child {
          .o-nav_popover_wrapper_group_list_column {
            .o-nav_popover_wrapper_group_list_item {
              &:last-child {
                margin-bottom: 0px;
              }
            }
          }
        }

        //分组标题
        &_title {
          height: 14px;
          text-align: left;
          font-weight: 500;
          font-size: 14px;
          color: #070f29;
          letter-spacing: 0;
          line-height: 14px;
          margin-bottom: 21px;
        }

        //分组项
        &_list {
          display: flex;
          flex-wrap: nowrap;

          &_column {
            flex-shrink: 0;
          }

          &_item {
            display: flex;
            /*margin-right: 40px;*/
            align-items: center;
            margin-bottom: 22px;
            padding: 8px 22px 8px 8px;
            box-sizing: border-box;
            height: 40px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.5s;
            color: #555555;

            &:nth-child(3n + 3) {
              margin-right: 0px;
            }

            &:last-child {
              margin-right: 0px;
            }

            &_icon {
              width: 24px;
              height: 24px;
              margin-right: 10px;

              img {
                width: 24px;
                height: 24px;
                border-radius: 6px;
              }
            }

            &_name {
              min-width: 56px;
              height: 14px;
              font-weight: 400;
              font-size: 14px;
              letter-spacing: 0;
              text-align: left;
              line-height: 14px;
            }
          }
        }
      }
    }
  }
}
</style>
