import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';
import SelectDateTimeRangeField from './SelectDateTimeRangeField.vue';

<Meta title="基础组件/o-select-datetime-range-field" component={SelectDateTimeRangeField} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'year-month', 'date', 'datetime'],
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SelectDateTimeRangeField },
  data: () => ({ date: "" }),
  template: '<o-select-datetime-range-field v-bind="$props" v-model="date"></o-select-datetime-range-field>',
});


# 选择日期和时间范围

<Canvas>
  <Story 
    name="默认"
    args={{
      value: 1
    }}>
    {Template.bind({})}
  </Story>
</Canvas>