<!--
  快捷入口
-->
<template>
  <div class="quick-entry">
    <el-popover placement="bottom" width="auto" v-model="open" @show="show" :popper-class="theme" trigger="hover">
      <div slot="reference" class="quick-entry_icon">
        <span class="iconfont icon-quick-entry"></span>
      </div>
      <div class="quick-entry_container default" v-loading="loading">
        <template v-if="list.length > 0">
          <div class="quick-entry_container_columns" v-for="(seletor, n) in 4" :key="n">
            <template v-for="(item, k) in list" v-if="(k + 4 - n) % 4 === 0">
              <div class="quick-entry_container_columns_item" @click="toPage(item)">
                <div class="quick-entry_container_columns_item_img">
                  <img class="quick-entry_container_columns_item_img_image" :src="getIcon(item.icon)"/>
                </div>
                <div class="quick-entry_container_columns_item_name">{{item.name}}</div>
              </div>
            </template>
          </div>
        </template>
        <div class="no-data" v-else>
          <div class="no-data_img">
            <img :src="getEmpty()"/>
          </div>
          <div class="no-data_desc">暂无可用的快捷功能</div>
          <div class="no-data_workbench">立即前往<span class="to-workbench" @click="toHome">「 工作台 」</span>设置快捷入口</div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Watch, Vue } from "vue-property-decorator";
  import {getQuickEntry, getResource, getSelectQuickEntry, isError} from "../../../util";
  import {Navigate, toWorkBenchPage} from "../../../util/desktop";

  @Component({
    name: "quick-entry"
  })
  export default class QuickEntry extends Vue {
    //主题
    private theme: string = (window as any).env.platform.theme || "";
    //列表
    private list = [];
    //加载状态
    private loading = false;
    //是否显示
    private open = false;



    private load () {
      let $parent:any = this.$parent;
      //权限点，权限组
      let privileges:string[] = [];
      if ($parent.roles) {
        privileges.push(...$parent.roles);
      }
      if ($parent.userInfo && $parent.userInfo.merchantMember && $parent.userInfo.merchantMember.privileges) {
        privileges.push(...$parent.userInfo.merchantMember.privileges);
      }
      let list:any = [];
      this.loading = true;
      Promise.all([getSelectQuickEntry(), getQuickEntry()]).then(([selectQuickEntry, AllEntry]) => {
        this.loading = false;
        let all = AllEntry.vos;
        let quickEntry = selectQuickEntry.vo;
        if (quickEntry && selectQuickEntry.set) {
          let inletId = quickEntry.inletId;
          for (let i = 0; i < inletId.length; i++) {
            let id = inletId[i];
            let item = all.find(item => item.id == id);
            if (item) {
              list.push(item);
            }
          }
          this.list = list;
        } else {
          let list: any = [];
          for (let i = 0; i < all.length; i++) {
            let item = all[i];
            if (list.length < 4) {
              if (privileges.indexOf(item.code) != -1) {
                list.push(item);
              }
            }
          }
          this.list = list;
        }
      }).catch((data) => {
        if (isError(data.errorCode)) {
          return false;
        }
      });
    }

    /**
     * @description 获取icon
     */
    private getIcon(icon) {
      return getResource(`/images/quick-entry/icon/${icon}.png`);
    }

    /**
     * @description 获取默认图片
     */
    private getEmpty() {
      return getResource(`/images/quick-entry/empty/${this.theme}.png`);
    }

    private toPage(page) {
      this.open = false;
      setTimeout(() => {
        Navigate.push({
          systemKey: page.projectCode,
          path: page.path,
          params: {
            app: page.businessCode
          },
          newTab: true
        });
      }, 50);

    }

    private show() {
      this.load();
    }

    /**
     * @description 跳转到工作台
     */
    private toHome() {
      toWorkBenchPage();
    }

  }
</script>

<style lang="less">
  .o-nav-default, .default {
    @backgroundColor:#F7FAFD;
    .quick-entry-container-function(@backgroudColor: @backgroundColor);
    .quick-entry-function(@backgroudColor: @backgroundColor);
  }
  .o-nav-red, .red {
    @backgroundColor:#FBF0F1;
    .quick-entry-container-function(@backgroudColor: @backgroundColor);
    .quick-entry-function(@backgroudColor: @backgroundColor);
  }
  .quick-entry-container-function(@backgroudColor) {
    .quick-entry {
      &_container {
        padding: 0 4px;
        display: flex;
        &_columns {
          /*margin-right: 25px;*/
          &:last-child {
            margin-right: 0px;
          }
          &_item {
            width: 100px;
            height: 74px;
            border-radius: 4px;
            margin-bottom: 16px;
            padding: 12px 8px;
            box-sizing: border-box;
            text-align: center;
            transition: all .3s;
            cursor: pointer;
            &_img {
              margin-bottom: 12px;
              height: 24px;
              &_image {
                width: 24px;
                height: 100%;
                border-radius: 6px;
              }
            }
            &_name {
              height: 14px;
              font-weight: 400;
              font-size: 12px;
              color: #24262B;
              letter-spacing: 0;
              text-align: center;
              line-height: 14px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            &:hover {
              background: @backgroudColor;
            }
            &:last-child {
              margin-bottom: 0px;
            }
          }
        }
        .no-data {
          text-align: center;
          &_img {
            width: 160px;
            height: 96px;
            margin: 25px 68px 0px 68px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          &_desc {
            margin-top: 8px;
            height: 14px;
            font-weight: 400;
            font-size: 14px;
            color: #A8ACBA;
            letter-spacing: 0;
            line-height: 14px;
          }
          &_workbench {
            margin-top: 32px;
            margin-bottom: 44px;
            height: 14px;
            font-weight: 400;
            font-size: 14px;
            color: #6A6F7F;
            letter-spacing: 0;
            line-height: 14px;
            .to-workbench {
              color: #4F71FF;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  .quick-entry-function(@backgroudColor) {
    .quick-entry {
      font-family: PingFangSC-Regular;
      &_icon {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all .3s;
        color: #6a6f7f;
        &:hover {
          background: @backgroudColor;
        }
      }

    }
  }



</style>