<template>
    <el-select
    size="medium"
    v-bind="$attrs"
    class="custom-el-popper-wrap"
    filterable
    :popperAppendToBody="false"
    :value="value"
    @input="onInput($event)"
    no-data-text="暂无数据"
    @visible-change="visibleChange"
  >
   
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="getLabel(item.label)"
        :value="item.value"
      >
        <el-tooltip class="item" effect="dark" :disabled="getTipDisabled(item.label)" :content="item.label" placement="top">
          <span>{{getLabel(item.label)}}</span>
        </el-tooltip>
      </el-option>
  </el-select>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit, Watch } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { Select } from "element-ui";
import { FormItem } from "../type";
import _ from "lodash"

const select = {
  extends: Select,
};

@Component({
  inheritAttrs: false,
  components: {
    elSelect: select,
  },
})
export default class OelSelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop({ default:99999 }) readonly labelMaxLength!: number;
  @Prop() readonly formItem!: FormItem;
  

  field!: FieldContext;
  options:any = []

  beforeMount() {
    this.field = registerField(this);
  }

  @Watch("formItem.options")
  onOptions(){
    this.setOptions()
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  created(){
    this.setOptions()
  }

  getLabel(label){
    return label.length<this.labelMaxLength?label:(label.substring(0, this.labelMaxLength) + "...")
  }

  getTipDisabled(label){
    const showLabel = this.getLabel(label)
    return !showLabel.endsWith("...") 
  }

  async setOptions(){
    if(Array.isArray(this.$attrs.options)) {
      this.options = _.cloneDeep(this.$attrs.options)
    }else if (_.isFunction(this.$attrs.options)) {
      this.options = await this.$attrs.options()
    }
  }

  @Emit("visibleChange")
  visibleChange(value){
    document.dispatchEvent(new Event('click'))
    return value
  }

  onFieldReset(){
    this.onInput(this.formItem.defaultValue || "")
  }
}
</script>