import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';
import Comp from './MemberChip.vue';

<Meta title="业务组件/o-member-chip" component={Comp} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'primary', 'success', 'warning', 'danger'],
  },
  outlined: {
    type: 'boolean'
  },
  closable: {
    type: 'boolean'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  template: '<o-member-chip v-bind="$props">名称</o-member-chip>',
});


按照用户ID, 回显企业人员的信息

<Canvas>
  <Story 
    name="默认"
    args={{
      value: 86769
    }}>
    {Template.bind({})}
  </Story>
</Canvas>