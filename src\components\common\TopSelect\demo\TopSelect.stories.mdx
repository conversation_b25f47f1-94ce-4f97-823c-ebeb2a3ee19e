import { <PERSON><PERSON>, <PERSON>a, Story, Source } from '@storybook/addon-docs';
import { action } from "@storybook/addon-actions";

import TopSelect from "../TopSelect.vue"
import amountField from "./amountField.vue"

<Meta title="o-top-select" component={TopSelect} 
parameters={
  {
    docs:{
      source:{
        code:`<o-top-select :formJson="formJson" @search="search" />`
      }
    }
  }
}
argTypes={{
  formJson:{
    description:"筛选项JSON配置",
    require:true
  },
  immediate:{
    description:"自动触发搜索",
    control:"boolean",
  },
  labelWidth:{
    description:"设置表单label的宽度",
    control:"text",
    default:"58px"
  },
  search:{
    description:"点击搜索或者重置触发的回调",
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { TopSelect },
  template: '<o-top-select v-bind="$props" @search="search" />',
});


# 基本使用

通过给组件传递 `formJson`字段 我们可快速配置筛选项。

<Canvas>
  <Story 
    name="基本使用"
    args={{ 
      formJson:
      [
        {
          type: "input",
          formItem: {
            prop: "key",
            label: "姓名",
            placeholder: "请输入姓名/工号/手机号",
          },
        }
      ],
      search: action("search"),
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


目前 `formJson->item.type` 支持以下几种类型   
``` ts
type FormJsonItemType = "input" | "select" | "date" | "datePicker" | "cascader" | "custom"   
```
点击 `搜索` 或 `重置`按钮 会触发@search 方法 ， 详见 `canvas面板` 可查看按钮点击以后信息输出。

<Canvas>
  <Story 
    name="多种类型选项"
    args={{ 
      formJson:[
        {
          type: "input",
          formItem: {
            prop: "key",
            label: "姓名",
            placeholder: "请输入姓名/工号/手机号",
          },
        },
        {
          type: "select",
          formItem: {
            prop: "positionId",
            label: "岗位",
            placeholder: "请选择",
            options:[
              {
                "optionEnumName": "岗位1",
                "optionEnumCode": "66",
              },
              {
                "optionEnumName": "测试岗",
                "optionEnumCode": "83",
              },
            ],
            // 如果后端的数据结构 不符合 组件的所需参数的数据结构 ， 可用field 字段 来进行数据的转换
            field: "optionEnumName as label,optionEnumCode as value",
          }
        },
         {
          type: "select",
          formItem: {
            prop: "sex",
            label: "性别",
            placeholder: "请选择",
            defaultValue:"66",
            options: [
              {
                "label": "男",
                "value": "66",
              },
              {
                "label": "女",
                "value": "83",
              },
            ]
          },
        },
        {
          type: "datePicker",
          formItem: {
            prop: "entryDate",
            label: "入职日期",
            placeholder: "请输入单据编号",
            type: "daterange",
            rangeSeparator: "-",
            // 日期组件返回的是一个数组类型，但是通常后端接口需要拆开分为两个字段传递
            // 所以可通过 startField 和 endField 设置拆分以后的属性名
            startField:"entryDateStart",
            endField:"entryDateEnd",
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          type: "cascader",
          formItem: {
            prop: "city",
            label: "工作城市",
            placeholder: "请选择",
            defaultValue:["110000"],
            options:[
              {
                "dictCode": "110000",
                "dictName": "北京市",
                "children": [
                  {
                    "dictCode": "110101",
                    "dictName": "东城区"
                  }
                ]
              }
            ],
            props: {
              filterable: true,
              checkStrictly: true,
              expandTrigger: "hover",
              value: "dictCode",
              label: "dictName",
              children: "children",
            },
          }
        },
      ],
      search: action("search"),
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

有的时候 默认的提供的筛选项类型组件 不能满足我们的业务场景，用户需要自定义一些业务筛选项。  
我们可通过配置 formItem->item.type = "custom" ，并且创建一个自定义组件，来跟top-select 组件一起协作。  
自定义业务筛选项组件 模板 如下： amountField.vue  

``` html
<template>
  <div>
    <el-switch
      :value="value"
      @input="onInput"
      active-text="按月付费"
      inactive-text="按年付费">
    </el-switch>
  </div>
</template>

<script>
export default {
  name:"amountField",
  props:["value","formItem"],
  methods:{
    // 当数据发生变化 将值传递给 topSelect 组件
    onInput($event) {
      this.$emit("input", this.formItem.prop, $event);
    },
    // 当用户点击重置按钮
    onFieldReset(){
      console.log("点击重置按钮")
      this.onInput(this.formItem.defaultValue || false)
    }
  }
}
</script>
```

formJson的配置  

```js
import amountField from "./amountField.vue"
const formJson = 
  {
    type: "input",
    formItem: {
      prop: "key",
      label: "姓名",
      placeholder: "请输入姓名/工号/手机号",
    },
  },
  {
    type:"custom",
    formItem:{
      prop:"amount",
      label:"金额",
      component:amountField,
      defaultValue:false
    }
  }
]
````

<Canvas>
  <Story 
    name="插入业务组件"
    args={{ 
      formJson:[
        {
          type: "input",
          formItem: {
            prop: "key",
            label: "姓名",
            placeholder: "请输入姓名/工号/手机号",
          },
        },
        {
          type:"custom",
          formItem:{
            prop:"amount",
            label:"金额",
            component:amountField,
            defaultValue:false
          }
        }
      ],
      search: action("search"),
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


### Props

| 参数           | 说明             | 类型    | 可选值 | 默认值 |
| -------------- | ---------------- | ------- | ------ | ------ |
| formJson        | 筛选配置JSON     | FormItem[] | -      | -      |
| immediate         | 是否自动触发搜索         | boolean  | true      | false |
| labelWidth | 设置表单全局label宽度 | String | -  | 58px   |

### Events

| 参数    | 说明           | 回调参数                           |
| ------- | -------------- | ---------------------------------- |
| search | 点击确定的回调 | formData |

### Methods

| 参数    | 说明           | 参数                           |
| ------- | -------------- | ---------------------------------- |
| getFormData | 获取表单数据 | Record<string,any> |
| setFormJson | 动态设置FormJson数据 | TFormJson |
| setOptions | 动态设置下拉选项数据源 | FormItemOption[] |
| getOptions | 获取下拉选项数据源 | Record<string,any>[] |