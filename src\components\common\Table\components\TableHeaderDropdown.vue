<template>
  <el-dropdown @visible-change="onVisibleChange" placement="bottom-start">
    <div class="button" ref="table-header-drop-down-button" :class="{ active:isActive }" @click="()=>{}" @mouseenter="onMouseenter" @mouseleave="onMouseleave">
        {{ label }}
        <i class="icon olading-iconfont oi-down" />
    </div>
    <el-dropdown-menu slot="dropdown" class="o-table-dropdown-menu">
      <el-dropdown-item v-for="item in options" :key="item.label" @click.native="handleItemClick(item)">{{ item.label }}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from "vue-property-decorator";
import { tableHeaderActionButtonItemOption } from "../type";

@Component({
  inheritAttrs:false
})
export default class TableDropdown extends Vue {
  @Prop({ default: "" }) readonly label!: String;
  @Prop({ default: "" }) readonly options!: tableHeaderActionButtonItemOption[];

  @Ref("table-header-drop-down-button") readonly button!:HTMLElement

  isActive:boolean = false
  dropdownEnterTimer:number = 0

  setIsActive(active=true){
    this.isActive = active
  }

  onVisibleChange(show){
    if(!show) this.setIsActive(false)
  }

  onMouseenter(){
    this.dropdownEnterTimer = Date.now()
    this.setIsActive()
  }

  onMouseleave(){
    const now = Date.now()
    if(now - this.dropdownEnterTimer < 300) this.setIsActive(false);
    this.button.blur()
  }

  handleItemClick(item){
    item.click && item.click()
  }

}

</script>

<style lang="less" scoped>
  .button{
    height: 36px;
    border: 1px solid var(--o-table-action-button-border-color);
    display: flex;
    padding: 0 16px;
    padding-right: 14px;
    border-radius: var(--o-table-action-button-radius);
    align-items: center;

    &:focus-visible {
      outline: none;
    }

    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: var(--o-table-action-button-text);
    color: var(--o-table-action-button-color);
    cursor: pointer;

    &:hover,&.active,&:focus{
      border-style: none;
      border: 1px solid var(--o-primary-color);
      color: var(--o-primary-color);
      .oi-down{
        transform: rotate(180deg) scale(0.5);
        display: block;
      }
    }

    .oi-down{
      transform: rotate(0) scale(0.5);
      transition: all .4s;
      font-size: 12px;
      margin-left: 6px;
    }
  }
  .o-table-dropdown-menu{
    text-align: left;
    padding: 12px;
    padding-bottom: 8px;
    background: #FFFFFF;
    box-shadow: 0 10px 30px 4px rgba(182,185,196,0.20);
    border-radius: 8px;
    border:0;
    margin-top: 8px !important;


    ::v-deep .popper__arrow{
      display: none;
    }
    li{
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--o-table-dropdown-menu-item-size);
      color: #46485A;
      letter-spacing: 0;
      line-height: 40px;
      padding: 0 16px;
      border-radius: 4px;
      margin-bottom: 4px;
    }
    .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
      background-color:var(--o-table-action-button-dropdown-hover);
      color: #46485A;
    }
  }
</style>