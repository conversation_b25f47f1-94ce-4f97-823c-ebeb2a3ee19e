<script lang="ts">
import { Compo<PERSON>, Vue, <PERSON>p, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules } from "../../form";
import formFieldMixins from "../../formFieldMixins";
import { FormItem } from "../type";

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
})
export default class JRemoteSearchSelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() readonly multiple!: boolean;
  @Prop() rules!: any[];
  @Prop({ default:"请输入" }) placeholder!: string;
  
  field!: FieldContext;

  valueText = "";
  loading = false
  options = []
  error = false;

  @Watch("value", {
    immediate: true,
  })
  onWatchValue() {
    this.setValueText();
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  async setValueText() {
    
  }

  render(h){
    const render = this?.formItem?.render
    if(!render) return h("span","")
    return render.call(this,h)
  }

  get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v.length==0?null:v, this.actualRules);

    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }

    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }

    return r?.detail[0]?.message ?? "";
  }

  onFieldReset() {
    if (Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue);
    }
  }
}
</script>