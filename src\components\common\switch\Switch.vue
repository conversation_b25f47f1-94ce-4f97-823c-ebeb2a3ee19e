<template>
  <el-switch
    ref="input"
    :value="value"
    :disabled="disabled"
    :active-text="activeText"
    :inactive-text="inactiveText"
    :active-value="activeValue"
    :inactive-value="inactiveValue"
    @input="onInput"
  >
    <slot></slot>
  </el-switch>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class  Switch extends Vue {
  @Prop() value!: any;
  @Prop() readonly!: any;
  @Prop() disabled!: boolean;
  @Prop() activeText!: string;
  @Prop() inactiveText!: string;
  @Prop() activeValue!: any;
  @Prop() inactiveValue!: any;

  onInput(value: any) {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.$emit("input", value);
  }
}
</script>

<style scoped>
</style>