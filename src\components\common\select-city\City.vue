<template>
  <span>{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class City extends Vue {
  @Prop() value!: any;
  @Prop({
    default: false,
  })
  full!: boolean;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let city = await store.loadCity(this.value);
      if (city) {
        if (this.full) {
          this.text = city.path.substring(1);
        } else {
          this.text = city.name;
        }
        this.$emit("update:change", city)
      }
    }
  }
}
</script>