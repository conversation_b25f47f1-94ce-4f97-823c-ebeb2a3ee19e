<template>
  <div>
    <slot></slot>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Provide, Vue, Watch } from "vue-property-decorator";
import { Context, CONTEXT_PROVIDER } from "./model";

@Component
export default class CommonContext extends Vue {
  @Prop() device!: "mobile" | "desktop";

  @Provide(CONTEXT_PROVIDER)
  context = new Context();

  beforeMount() {
    this.context.props = this.$props
  }

  @Watch("$props")
  onPropsChange() {
    this.context.props = this.$props;
  }
}
</script>