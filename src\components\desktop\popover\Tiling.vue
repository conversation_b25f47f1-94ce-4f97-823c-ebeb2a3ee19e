<!-- 平铺 -->
<template>
  <div class="o-nav_popover_wrapper o-nav_popover_flex">
    <div class="o-nav_popover_flex_nowrap" v-for="(seletor, n) in 3" :key="n">
      <template v-for="(item, k) in list" v-if="(k + 3 - n) % 3 === 0">
        <div
          :key="k"
          class="o-nav_popover_wrapper_item nav_item_hover"
          @click="openAppEvent(item)"
        >
          <div>
            <img :src="item.icon" />
          </div>
          <div>{{ item.name }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { Navigation } from "../../../model/type";
@Component({
  name: "o-tiling",
})
export default class Tiling extends Vue {
  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  /**
   * 导航数据
   */
  @Prop()
  private list?: Navigation;

  /**
   * @description 点击触发事件
   * @param item
   */
  private openAppEvent(item): void {
    this.$emit("on-select", item);
  }
}
</script>

<style scoped>
</style>