/**
 * @description 导航组件所需枚举
 */

/**
 * @description 导航菜单打开类型枚举
 */
export enum OpenType {
    //新打开tab
    NEWTAB = "NEW_TAB",
    //当前tab中打开
    CURRENT_TAB = "CURRENT_TAB"
}

/**
 * @description 导航点击事件类型
 */
export enum EventType {
    //打开工作台
    OPEN_WORKBENCH = "OPEN_WORKBENCH",

    //打开应用
    OPEN_APP = "OPEN_APP",

    //打开应用中的菜单
    OPEN_APP_MENU = "OPEN_APP_MENU",

    //展开二级导航
    EXPAND_MENU = "EXPAND_MENU",

    //打开链接页面
    OPEN_URL = "OPEN_URL"
}


/**
 * @description 增值业务等级
 */
export enum IncrementBusinessLevel  {
    //基础版
    BASIC ,

    //高级版
    PREMIUM
}

/**
 * @description 审核状态
 */
export enum AuditType {
    /**
     * 待审核
     */
    WAIT = "WAIT",

    /**
     * 审核通过
     */
    PASS = "PASS",

    /**
     * 未认证
     */
    NOT_IDENTIFIED = "NOT_IDENTIFIED",

    /**
     * 未通过
     */
    NOT_PASS = "NOT_PASS"
}

/**
 * @description 应用未开通显示规则
 */
export enum AppNotOpenDisplayRule {
    /**
     * 未开通企业超级管理员可见
     */
    ADMIN_DISPLAY = "ADMIN_DISPLAY",

    /**
     * 未开通企业全员可见
     */
    ALL_STAFF_DISPLAY = "ALL_STAFF_DISPLAY",

    /**
     * 未开通不显示
     */
    NO_DISPLAY = "NO_DISPLAY"
}

/**
 * @description 缓存数据key枚举
 */
export enum CacheKey {
    /**
     * 菜单id key
     */
    MIDKEY= "__MIDKEY__",

    /**
     * 应用code key
     */
    APPCODEKEY = "__APPCODE__",

    /**
     * 导航缓存数据key
     */
    NAVCACHEKEY = "__NAVCACHEKEY__",

    /**
     * 菜单缓存数据key
     */
    MENUCACHEKEY = "__MENUCACHEKEY__",

    /**
     * bindinfo 接口缓存数据key
     */
    BINDINFOCACHEKEY = "__BINDINFOCACHEKEY__",

    /**
     * 企业列表缓存
     */
    ENTERPRISECACHEKEY = "__ENTERPRISECACHEKEY__",

    /**
     * 权限缓存数据
     */
    ROLESCACHEKEY = "__ROLESCACHEKEY__",

    /**
     * 系统地址缓存数据
     */
    SYSTEMPATHCACHEKEY = "__SYSTEMPATHCACHEKEY__",

    /**
     * 权限缓存key
     */
    PRIVILEGECACHEKEY = "__PRIVILEGECACHEKEY__"
}

/**
 * @description 系统标示key
 */
export enum SystemKey {
    //sso系统
    SSO = "sso"
}

/**
 * @description 用户类型
 */
export enum UserType {
    //个人用户
    PERSONAL = "PERSONAL",

    //企业用户
    ENTERPRISE = "ENTERPRISE"
}

/**
 * @description token切换提示类型
 */
export enum ChangeTokenType {
  //切换企业
  MERCHANT = 0,
  //切换用户
  USER = 1
}

/**
 * @description 搜索类型
 */
export enum SearchType {
  //菜单
  MENU = "MENU",
  //导航
  NAVIGATION  = "NAVIGATION"
}

/**
 * @description 查询类型
 */
export enum QueryType {
  //关键字查询
  KEYWORD = "KEYWORD",
  //拼音查询
  PINYIN = "PINYIN",
  //首字母
  INITIALS = "INITIALS",
  //拼音空格
  PINYINSPACE = "PINYINSPACE",
  //未查找
  NOT = "NOT"
}
