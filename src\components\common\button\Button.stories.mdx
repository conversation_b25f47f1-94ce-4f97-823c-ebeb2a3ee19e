import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import <PERSON><PERSON> from './Button.vue';

<Meta title="基础组件/o-button" component={Button} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'primary', 'success', 'warning', 'danger'],
  },
  outlined: {
    type: 'boolean'
  },
  disabled: {
    type: 'boolean'
  },
  loading: {
    type: 'boolean'
  },
  icon: {
    type: 'text'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { But<PERSON> },
  template: '<o-button v-bind="$props" style="width: 100px">按钮</o-button>',
});

export const Story1 = Template.bind({})
Story1.args = {
  type: 'success',
};
Story1.parameters = {
  docs: { source: { code: "<a></a>" } },
};

按钮主题

<Canvas>
  <Story 
    name="默认"
    args={{
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="primary"
    args={{ 
      type: 'primary',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="success"
    args={{ 
      type: 'success',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="warning"
    args={{ 
      type: 'warning',
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="danger"
    args={{ 
      type: 'danger',
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

outlined

<Canvas>
  <Story 
    name="primary_outlined"
    args={{ 
      type: 'primary',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="success_outlined"
    args={{ 
      type: 'success',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="warning_outlined"
    args={{ 
      type: 'warning',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="danger_outlined"
    args={{ 
      type: 'danger',
      outlined: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


loading

<Canvas>
  <Story 
    name="loading"
    args={{ 
      type: 'primary',
      loading: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

