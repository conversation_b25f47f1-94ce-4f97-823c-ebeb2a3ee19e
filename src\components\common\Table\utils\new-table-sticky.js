/*
 * @Date: 2022-12-05 15:35:05
 * @LastEditors: zhaoxm
 * @LastEditTime: 2023-02-13 11:30:48
 * @Description: 表格吸顶
 */

import _ from "lodash"

const utils = {
  style(element,obj){
    Object.keys(obj).forEach(prop=>{
      if(Array.isArray(element)) {
        element.forEach(el=>{
          el.style[prop] = obj[prop]
        })
      }else{
        element.style[prop] = obj[prop]
      }
    })
    return this
  },
}

const throttleAwaitTime = 50
const tableFixedZIndex = 10
const map = new Map()

// 获取oTable 距离滚动区域 顶部的距离
const getOTableOffsetTop = (div)=>{
  if(!div) return 0
  if(div.parentNode.classList.contains("o-container-main")) {
    return div.offsetTop
  }
  let offsetTop = 0 
  while (!div.parentNode.classList.contains("o-container-main")) {
    offsetTop+=div.offsetTop
    div = div.parentNode
  }
  return offsetTop
}

// 设置表格顶部操作按钮吸顶
const setTableActionButtonFixed =(isFixed,oTableUId)=>{

  const { hasTableHeaderEl,oTableHeaderEl } = map.get(oTableUId)
  
  if(!hasTableHeaderEl) return 
  // 如果表格顶部有操作栏 

  if(isFixed) {
    utils.style(oTableHeaderEl,{
      position:"sticky",
      zIndex:9,
      top:"0px",
    })
  }else{
    utils.style(oTableHeaderEl,{
      position:"static",
      zIndex:"inherit",
      top:"inherit",
    })
  }
}

const updateMap = (oTableUId,params)=>{
  const mapData = map.get(oTableUId)
  map.set(oTableUId,{
    ...mapData,
    ...params
  })

  mapData && (mapData.oTableRef._stickyMap = map.get(oTableUId))
}

window.$__updateMap = updateMap
window.$__map = map

const updateTableTitleStickyTop = (oTableUId)=>{
  const { oTableHeaderElHeight,oContainerMainClientRect } = map.get(oTableUId)
  const { top:containerMainClientTop } = oContainerMainClientRect
  const top = oTableHeaderElHeight + containerMainClientTop
  updateMap(oTableUId,{
    tableTitleStickyTop:top
  })
  return top
}

// 设置右侧 浮动表头 吸顶
const setTableRightTitleFixed = (isFixed,oTableUId)=>{
  const { oldElTableFixedRightTitleElStyle,elTableFixedRightTitleEl,elTableTitleClientRect,tableTitleStickyTop } = map.get(oTableUId)
  const { width,left } = elTableTitleClientRect
  if(!elTableFixedRightTitleEl) return console.log(`setTableRightTitleFixed -> isFixed:${isFixed} , elTableFixedRightTitleEl = null`)
  const tableHeaderWrap = elTableFixedRightTitleEl.querySelector(".el-table__fixed-header-wrapper")

  // 取消吸顶
  if(!isFixed) {
    setTimeout(() => {
      tableHeaderWrap.style.overflow = 'auto'
      tableHeaderWrap.scrollLeft = tableHeaderWrap.scrollWidth
      tableHeaderWrap.style.overflow = 'hidden'
    });
    tableHeaderWrap.setAttribute('style',oldElTableFixedRightTitleElStyle)
    window.tableHeaderWrap = tableHeaderWrap
    return
  }

  if(!oldElTableFixedRightTitleElStyle) {
    updateMap(oTableUId,{
      oldElTableFixedRightTitleElStyle:elTableFixedRightTitleEl.getAttribute("style")
    })
  }

  setTimeout(() => {
    tableHeaderWrap.style.overflow = 'auto'
    tableHeaderWrap.scrollLeft = tableHeaderWrap.scrollWidth
    tableHeaderWrap.style.overflow = 'hidden'
  });

  const elTableFixedRightTitleElWidth = elTableFixedRightTitleEl.clientWidth 
  const distanceLeft = width + left - elTableFixedRightTitleElWidth
  tableHeaderWrap.setAttribute('style',`width: ${elTableFixedRightTitleElWidth}px; overflow: hidden; position: fixed; z-index: ${tableFixedZIndex}; top: ${tableTitleStickyTop}px; left: ${distanceLeft}px;`)
}

// 设置左侧 浮动表头吸顶
const setTableLeftTitleFixed = (isFixed,oTableUId)=>{
  
  const { oldElTableFixedLeftTitleElStyle,elTableFixedLeftTitleEl,elTableTitleClientRect,tableTitleStickyTop } = map.get(oTableUId)
  const { left } = elTableTitleClientRect

  if(!elTableFixedLeftTitleEl) return console.log(`setTableLeftTitleFixed -> isFixed:${isFixed} , elTableFixedLeftTitleEl = null`)
  
  const tableHeaderWrap = elTableFixedLeftTitleEl.querySelector(".el-table__fixed-header-wrapper")
  
  // 取消吸顶
  if(!isFixed) {
    return tableHeaderWrap.setAttribute('style',oldElTableFixedLeftTitleElStyle)
  }

  if(!oldElTableFixedLeftTitleElStyle) {
    updateMap(oTableUId,{
      oldElTableFixedLeftTitleElStyle:elTableFixedLeftTitleEl.getAttribute("style")
    })
  }
  const elTableFixedLeftTitleElWidth = elTableFixedLeftTitleEl.clientWidth 
  tableHeaderWrap.setAttribute('style',`width: ${elTableFixedLeftTitleElWidth}px; overflow: hidden; position: fixed; z-index: ${tableFixedZIndex}; top: ${tableTitleStickyTop}px; left: ${left}px;`)
}

// 设置不是固定标题的吸顶
const setBaseTableTitleFixed = (isFixed,oTableUId)=>{
  const { elTableTitleEl,oldElTableFixedTitleElStyle,elTableTitleClientRect,elTableBodyWrapEl,oTableRef } = map.get(oTableUId)

  const { left } = elTableTitleClientRect
  const top = updateTableTitleStickyTop(oTableUId)
  const placeholderBlockEl = oTableRef.$el.querySelector(".placeholder-block") 

  // 取消吸顶
  if(!isFixed) {
    elTableBodyWrapEl.style.marginTop = 0;
    if(placeholderBlockEl) {
      placeholderBlockEl.style.display = "none"
    }
    return elTableTitleEl.setAttribute('style',oldElTableFixedTitleElStyle)
  }

  if(!oldElTableFixedTitleElStyle) {
    updateMap(oTableUId,{
      oldElTableFixedTitleElStyle:elTableTitleEl.getAttribute("style")
    })
  }

  const width = elTableTitleEl.clientWidth 
  elTableTitleEl.setAttribute('style',`width: ${width}px; position: fixed; z-index: ${tableFixedZIndex}; top: ${top}px;left:${left}px;`)
  
  if(placeholderBlockEl) {
    placeholderBlockEl.style.display = "block"
  }
  
  // 设置容器 marginTop , 解决吸顶以后错位的问题
  elTableBodyWrapEl.style.marginTop = elTableTitleEl.clientHeight + "px"
}

// 设置表格标题吸顶
const setTableTitleFixed = (isFixed,oTableUId,reload=false)=>{
  
  updateElTableTitleEl(oTableUId)
  const { isTitleFixed } = map.get(oTableUId)

  if(isFixed) {
    if(!isTitleFixed || reload) {
      updateMap(oTableUId,{
        isTitleFixed:true
      })
      setBaseTableTitleFixed(true,oTableUId)
      setTableLeftTitleFixed(true,oTableUId)
      setTableRightTitleFixed(true,oTableUId)
    }
  }else{
    if(isTitleFixed || reload){
      updateMap(oTableUId,{
        isTitleFixed:false
      })
      setBaseTableTitleFixed(false,oTableUId)
      setTableLeftTitleFixed(false,oTableUId)
      setTableRightTitleFixed(false,oTableUId)
    }
  }
}

// 设置表头吸顶
const setTableTitleSticky = (isFixed,oTableUId)=>{
  // 表头顶部操作按钮吸顶
  setTableActionButtonFixed(isFixed,oTableUId)
  // 表格表头吸顶
  setTableTitleFixed(isFixed,oTableUId)
}


// 监听滚动条
const onContainerScroll = (oTableUId)=>{
  const oTableData =  map.get(oTableUId)
  const { oContainerMainDom,oTableRef } = oTableData
  const containerScrollY = oContainerMainDom.scrollTop

  // 判断是否存在 操作头元素， 例如导添加等操作 
  const oTableHeaderEl = oTableRef?.$el?.querySelector(".o-table-header")
  // 操作头的高度
  const oTableHeaderElHeight = oTableHeaderEl?oTableHeaderEl.clientHeight : 0 ;
  // 操作头距离顶部的位置
  const oTableOffsetTop = getOTableOffsetTop(oTableRef.$el)

  updateMap(oTableUId,{
    hasTableHeaderEl:!!oTableHeaderElHeight,
    oTableHeaderElHeight,
    oTableHeaderEl,
    oTableOffsetTop,
    oContainerMainClientRect:oContainerMainDom.getBoundingClientRect(),
  })

  // 设置表格标题吸顶
  setTableTitleSticky(containerScrollY>oTableOffsetTop,oTableUId)

}

// 更新饿了么TableHeader 在视口的位置信息
const updateElTableHeaderClientRect = (oTableUId)=>{
  const oTableData = map.get(oTableUId)
  const { elTableRef } = oTableData

  if(!elTableRef) return console.log("updateElTableHeaderClientRect 没有找到 elTableRef元素")
  
  const elTableTitleClientRect = elTableRef.$el.querySelector(".el-table__body-wrapper").getBoundingClientRect();

  updateMap(oTableUId,{
    elTableTitleClientRect
  })

}

// 更新所需的el元素
const updateElTableTitleEl = (oTableUId)=>{
  const oTableData = map.get(oTableUId)
  const { elTableRef } = oTableData

  const elTableEl = elTableRef.$el
  
  // elTable 标题 ， 未fixed 元素
  const elTableTitleEl = elTableEl.querySelector(".el-table__header-wrapper")
  // elTable 左侧fixed 标题
  const elTableFixedLeftTitleEl = elTableEl.querySelector(".el-table__fixed")
  // elTable 右侧fixed 标题
  const elTableFixedRightTitleEl = elTableEl.querySelector(".el-table__fixed-right")

  const elTableBodyWrapEl = elTableEl.querySelector(".el-table__body-wrapper")

  updateMap(oTableUId,{
    elTableTitleEl,
    elTableBodyWrapEl,
    elTableFixedLeftTitleEl,
    elTableFixedRightTitleEl,
  })
}

const mutationObserverOTableWidth = (oTableUId)=> {
  const obOTableWidth = {
    observer:null,
    oladWidth:null,
    start(oTableUId){
      const { oTableRef } = map.get(oTableUId) 
      const oTableEl = oTableRef.$el
      this.oladWidth = oTableEl.clientWidth
      updateElTableHeaderClientRect(oTableUId)

      const onChangOTableWidth = _.throttle(()=>{
        const { isTitleFixed,elTableRef } = map.get(oTableUId) 
        console.log("表格宽度发生变化")

        if(!elTableRef) return 
        // 如果没有找到表格元素， 可能是动态加载的表头 ， 此时不设置是否吸顶
        updateElTableHeaderClientRect(oTableUId)
        setTableTitleFixed(false,oTableUId,true)
        setTableTitleFixed(isTitleFixed,oTableUId,true)
      },100)
      
      const callback = ()=>{
        if(this.oladWidth!==oTableEl.clientWidth) {
          this.oladWidth = oTableEl.clientWidth
          onChangOTableWidth()
        }
      }
  
      const config = { attributes: true, childList: true, subtree: true };
      this.observer = new MutationObserver(callback);
      this.observer.observe(oTableEl, config);
    },
    unBind(){
      // console.log("unBind observe")
      this.observer.disconnect()
    }
  }

  updateMap(oTableUId,{
    obOTableWidth 
  })

  obOTableWidth.start(oTableUId)
}


const bindEvent = (oTableUId)=>{
  // 1. 监听滚动条 节流
  const { oContainerMainDom,throttleContainerScroll } = map.get(oTableUId) 
  oContainerMainDom.addEventListener('scroll',throttleContainerScroll)
  console.log("bind")
  // 2. 监听 oTable 元素宽度发生变化 
  mutationObserverOTableWidth(oTableUId)
}

const unEvent = (oTableUId)=>{
  // 移除滚动条事件的监听 
  const { oContainerMainDom,throttleContainerScroll,obOTableWidth } = map.get(oTableUId)
  if(oContainerMainDom) oContainerMainDom.removeEventListener("scroll",throttleContainerScroll)
  // 移除侧边栏的 展开收起的监听
  obOTableWidth && obOTableWidth.unBind && obOTableWidth.unBind() 
}

// 动态表头的处理
const dynamicTableHeaderFn = (oTableUId)=>{
  const { oTableRef } = map.get(oTableUId)
  // 动态表头需要重新计算 距离顶部的位置
  oTableRef.$watch("context.store.isDynamicTableHeader",isDynamicTableHeader=>{
    if(isDynamicTableHeader) {
      oTableRef.$nextTick(()=>{
        updateElTableHeaderClientRect(oTableUId)
      })
    }
  })
}

export const sticky = {

  inserted:function (el,binding,vNode){

    // 如果用户没有设置吸顶
    if(!binding.value) return 

    // 1. 获取当前组件 实例 
    const elTableRef = vNode.componentInstance
    const oTableRef = elTableRef.$parent
    const oConCationRef = oTableRef.oConCationContext
    const oTableUId = oTableRef._uid


    updateMap(oTableUId,{
      oConCationRef,
      elTableRef,
      oTableRef,
    })

    updateElTableTitleEl(oTableUId)
    
    // 选判断是否包含在oContainer 容器内 ，如果不包含在内 不往下执行即可
    if(!oConCationRef) return console.warn("缺少o-container 组件的包裹，无法进行吸顶")

    // 获取 滚动容器的dom元素
    const oContainerMainDom = oConCationRef.$refs["o-container-main"]

    updateMap(oTableUId,{
      oContainerMainDom,
      // 节流监听 oContainer 容器滚动条
      throttleContainerScroll:_.throttle(()=>onContainerScroll(oTableUId),throttleAwaitTime),
      oContainerMainClientRect:oContainerMainDom.getBoundingClientRect(),
      binding
    })
    
    bindEvent(oTableUId)

    // 动态表头需要特殊处理
    dynamicTableHeaderFn(oTableUId)

  },
  unbind:function (el,binding,vNode){

    // 如果用户没有设置吸顶
    if(!binding.value) return 

    const elTableRef = vNode.componentInstance
    const oTableRef = elTableRef.$parent
    const oTableUId = oTableRef._uid

    unEvent(oTableUId)

    // console.log("unbind",{oTableUId})
  }
}