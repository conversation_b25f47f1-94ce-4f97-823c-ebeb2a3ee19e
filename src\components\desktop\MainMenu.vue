<!-- 侧边菜单组件 -->
<template>
  <div
    class="o-menu"
    v-loading="loading"
    :class="['o-menu-' + theme]"
    :style="{ width: isCollapse ? 0 : 208 + 'px' }"
  >
    <div class="o-menu_wrapper">
      <!-- 应用标题 -->
      <div class="o-menu_wrapper_app" v-if="!loading" @click="reload">
        <div v-if="appIcon.length > 0"><img :src="appIcon[0].iconUrl" /></div>
        <div v-else>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 40 40"
            class="design-iconfont"
          >
            <defs>
              <linearGradient
                x1="25.7216777%"
                y1="0%"
                x2="71.1802874%"
                y2="91.4996992%"
                id="01pg3us7b__ks7s5xsqxa"
              >
                <stop stop-color="#7896FF" offset="0%" />
                <stop stop-color="#5D7EF9" offset="100%" />
              </linearGradient>
            </defs>
            <g fill="none" fill-rule="evenodd">
              <rect
                fill="url(#01pg3us7b__ks7s5xsqxa)"
                width="40"
                height="40"
                rx="8"
              />
              <path
                d="M4.4680053,2 L21.5262466,2 C22.8908755,2 23.9971259,3.10625038 23.9971259,4.47087937 L23.9971259,21.5291206 C23.9971259,22.8937496 22.8908755,24 21.5262466,24 L4.4680053,24 C3.1033763,24 1.99712592,22.8937496 1.99712592,21.5291206 L1.99712592,4.47087937 C1.99712592,3.10625038 3.1033763,2 4.4680053,2 Z"
                fill="#B2C5FB"
                transform="rotate(90 12.997126 21)"
              />
              <path
                d="M2.47087937,0 L19.5291206,0 C20.8937496,-2.67474633e-15 22,1.10625038 22,2.47087937 L22,19.5291206 C22,20.8937496 20.8937496,22 19.5291206,22 L2.47087937,22 C1.10625038,22 1.67118853e-16,20.8937496 0,19.5291206 L0,2.47087937 C-1.67118853e-16,1.10625038 1.10625038,2.5067828e-16 2.47087937,0 Z"
                fill="#FFF"
                transform="rotate(90 11 19)"
              />
              <path
                d="M11.9432049,6.91700586 C12.5721429,8.13968035 13.540465,8.87409209 14.8481711,9.12158615 C16.1558773,9.36908022 16.3498111,9.99454076 15.4313192,10.9979678 C14.5128273,12.0013948 14.1438145,13.1890973 14.3229338,14.5651105 C14.5007065,15.9411237 13.9916303,16.3271607 12.7957054,15.7245664 C11.5997806,15.1219722 10.4025089,15.1219722 9.20523725,15.7245664 C8.0079656,16.3271607 7.50023623,15.9397786 7.67800885,14.5651105 C7.85578147,13.1890973 7.48676861,12.0000497 6.56827674,10.9979678 C5.64978488,9.99454076 5.84506541,9.36908022 7.1514248,9.12158615 C8.45913096,8.87409209 9.42745303,8.13968035 10.056391,6.91700586 C10.6839822,5.69433138 11.314267,5.69433138 11.9432049,6.91700586 L11.9432049,6.91700586 Z"
                fill="#6586FB"
                fill-rule="nonzero"
                transform="translate(8 8)"
              />
            </g>
          </svg>
        </div>
        <div>{{ appInfo.name }}</div>
      </div>
      <div class="o-menu_wrapper_menus webkit-scrollbar">
        <el-menu
          :default-active="defaultActive"
          class="o-menu_wrapper_menus_el"
          text-color="#6A6F7F"
          active-text-color="#4F71FF"
          @select="handleSelect"
        >
          <template
            v-for="(item, index) in slideMenu"
            v-if="item.routePath || item.children.length > 0"
          >
            <!-- 含有子菜单数据 -->
            <el-submenu
              v-if="item.children != null && item.children.length > 0"
              :key="index"
              :index="item.id + ''"
            >
              <template slot="title">
                <span slot="title">{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="(item2, index2) in item.children"
                :key="index2"
                :index="item2.id + ''"
              >
                <span slot="title">{{ item2.title }}</span>
              </el-menu-item>
            </el-submenu>
            <!-- 只有一级菜单显示 -->
            <el-menu-item v-else :key="index" :index="item.id + ''">
              <span slot="title">{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>
    </div>
    <div @click="collapse" class="o-menu_collapse">
      <span
        class="iconfont icon-arrow-left"
        :class="{ 'arrow-right': isCollapse }"
      ></span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { Menu } from "../../model";
import {
  expandMenu,
  filterMenu,
  findMenuByPath,
  getMid,
  MenuContext,
} from "../../util/desktop";

@Component({
  name: "o-main-menu",
})
export default class MainMenu extends Vue {
  /**
   * 菜单列表
   */
  @Prop()
  private menus!: Menu[];

  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop()
  private theme?: string;

  /**
   * 权限(用于判断当前菜单是否显示)
   */
  @Prop()
  private roles?: string[];

  /**
   * 加载状态，只用于获取权限等基本信息接口使用，原因：此接口比较耗时
   */
  @Prop({
    default: true,
  })
  private loading?: boolean;

  /**
   * @description appCode
   */
  @Prop()
  private appCode?: string;

  /**
   * appinfo
   */
  @Prop()
  private appInfo?: object;

  /**
   * 已开通应用
   */
  @Prop()
  private openApp?: string[];

  /**
   * 是否管理员
   */
  @Prop({
    default: false,
  })
  private isManager?: boolean;

  /**
   * @description 未开通显示规则
   */
  @Prop()
  private notOpenDisplayRules;

  /**
   * 侧边菜单图标
   */
  @Prop()
  private appIcon?: string[];

  /**
   *  当前路由path
   */
  // @Prop()
  // private routePath?:string;

  //是否折叠  false：展开状态   true：折叠状态
  private isCollapse: boolean = false;

  /**
   * 侧边菜单数据
   */
  private slideMenu: any[] = [];

  /**
   * 转换后的数据
   */
  private expandMenus: object = {};

  /**
   * 当前选中的菜单,需要通过路由解析
   */
  private defaultActive: string = "";

  /**
   * @description 菜单与权限数据
   */
  private get menuAndRoles() {
    const { menus, roles, openApp, isManager } = this;
    return {
      menus,
      roles,
      openApp,
      isManager,
    };
  }

  /**
   * @description 监听计算函数
   */
  @Watch("menuAndRoles")
  private watchMenuAndRoles(data) {
    this.init();
  }

  /**
   * @description 监听路由path的变化
   */
  // @Watch("routePath")
  // private watchRoutePath(path) {
  //     //设置当前选中的菜单
  //     this.setRoutePath();
  // }

  /**
   * @description 监听路由变化
   */
  @Watch("$route")
  private watchRoute() {
    //设置当前选中的菜单
    this.setRoutePath();
  }

  private setRoutePath() {
    //获取当前路由path
    let path = this.$route.meta?.navActivePath || this.$route.path;
    
    let menu = findMenuByPath(this.slideMenu, path);
    if (menu) {
      this.defaultActive = menu.id + "";
    } else {
      this.defaultActive = getMid() || "";
    }
  }

  /**
   * @description 初始化方法
   */
  private init(): void {
    if (
      this.roles &&
      this.roles.length > 0 &&
      this.openApp &&
      this.openApp.length > 0
    ) {
      if (this.menus.length > 0) {
        this.slideMenu = filterMenu(
          this.menus,
          this.roles || [],
          this.openApp || [],
          this.isManager || false
        );
        //平铺数据，便于通过key查找数据
        this.expandMenus = expandMenu(this.slideMenu);
        //设置当前选中的菜单
        this.setRoutePath();
      }
    }
  }

  /**
   * @description 控制菜单展开与收起
   */
  private collapse(): void {
    this.isCollapse = !this.isCollapse;
    (this as any).$customEventBus && (this as any).$customEventBus.$emit("menu-collapse",this.isCollapse)
  }

  /**
   * @description 刷新当前页面
   */
  private reload(): void {
    window.location.reload();
  }

  /**
   * @description 菜单点击事件
   * @param key 菜单id
   */
  private handleSelect(key: string) {
    let menu: Menu = this.expandMenus[key];
    new MenuContext(this).setRouter(this.$router).openPage(menu);
  }

  private async created() {
    this.init();
  }
}
</script>

<style lang="less">
.o-menu {
  position: relative;
  height: calc(100vh - 64px);
  font-family: PingFangSC-Regular;
  transition: all 0.3s;
  background: #ffffff;
  box-shadow: 0 2px 5px 0 rgba(83, 97, 255, 0.05);
  .el-loading-mask {
    z-index: 10;
  }
  &_wrapper {
    width: 100%;
    overflow: hidden;
    //应用标题样式
    &_app {
      height: 60px;
      border-bottom: 1px solid #eaeaea;
      display: flex;
      align-items: center;
      padding-left: 24px;
      box-sizing: border-box;
      cursor: pointer;
      div {
        &:nth-child(1) {
          width: 24px;
          height: 24px;
          border-radius: 6px;
          img {
            width: 24px;
            height: 24px;
            border-radius: 6px;
          }
        }
        &:nth-child(2) {
          font-weight: 400;
          font-size: 16px;
          color: #070f29;
          letter-spacing: 0;
          margin-left: 12px;
        }
      }
    }
    //菜单样式
    &_menus {
      padding: 10px 15px;
      box-sizing: border-box;
      height: calc(100vh - 60px - 64px);
      overflow: scroll;
      overflow-x: hidden;
      //自定义菜单样式
      &_el {
        width: 178px;
        border-right: none !important;
        .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
          transform: rotateZ(-180deg);
        }
        .el-submenu {
          &__title {
            height: 46px;
            line-height: 46px;
            &:focus {
              background: none;
            }
            &:hover {
              background: none;
            }
          }
        }
        .el-menu--inline {
          .el-menu-item {
          }
        }
        .el-menu-item {
          width: 178px !important;
          height: 40px !important;
          line-height: 40px !important;
          min-width: 100px !important;
          margin-bottom: 6px;
          border-radius: 8px;
          &:focus {
            background: none;
          }
          &:hover {
            height: 40px !important;
            line-height: 40px !important;
            /*background: none;*/
            background: #F7FAFD;
          }
        }
        .el-menu-item.is-active {
          width: 178px;
          height: 40px;
          background: #F7FAFD;
          color: #4f71ff;
          font-size: 14px;
          line-height: 40px;
          font-weight: 400;
        }
      }
    }
  }
  &_collapse {
    position: absolute;
    top: 50%;
    border-radius: 0 8px 8px 0;
    right: -10px;
    font-size: 40px;
    z-index: 10;
    width: 10px;
    height: 40px;
    cursor: pointer;
    background: #d9dde6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #858c9f;
    &:hover {
      background: #4f71ff;
      color: #ffffff;
    }
    .arrow-right {
      transform: rotateZ(180deg) scale(0.8);
    }
    span {
      transition: all 0.3s;
      font-size: 10px;
      transform: scale(0.8);
    }
  }
}

/*******************  红色主题start  ***********************/
.o-menu-red {
  //当前loading中svg的圆圈颜色
  .el-loading-spinner .path {
    stroke: #c81930;
  }
  //侧边菜单收起展开颜色
  .o-menu_collapse {
    &:hover {
      background: #c81930 !important;
    }
  }
  //侧边菜单hover态与选中态样式
  .o-menu_wrapper_menus_el {
    //选中样式
    .el-menu-item.is-active {
      color: #c81930 !important;
      background: #fcf2f2 !important;
    }
    //hover样式
    .el-menu-item:hover {
      height: 40px !important;
      line-height: 40px !important;
      background: #fcf2f2 !important;
    }
  }
}
/*******************  红色主题end  ***********************/
.left-menu {
  width: 210px;
  height: 100%;
  background-color: #fff;
  border: 1px solid rgb(238, 238, 238);
  box-shadow: 0px 0px 56px 0px rgba(241, 241, 241, 1);
  color: #6a6f7f;

  /deep/ .el-menu {
    border: 0 !important;
    &:focus {
      background: none;
    }
  }
}
</style>