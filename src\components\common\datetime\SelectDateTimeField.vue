<template>
  <div>
    <div v-if="textModel">
      {{value}}
    </div>
    <div v-else>
      <el-date-picker
        v-if="!mobile"
        :value="value"
        :type="elType"
        :placeholder="placeholder"
        :disabled="isDisabled"
        @input="onInput"
        :format="format"
        :value-format="valueFormat"
        style="width: 100%"
      >
      </el-date-picker>
    </div>
    <placeholder
      v-if="mobile"
      @click="onClick"
      style="width: 100%"
      :value="text"
      :placeholder="placeholder"
    />
    <popup v-model="show" mobileHeight="50%">
      <van-datetime-picker
        v-if="show"
        :type="type"
        :min-date="minDate"
        :max-date="maxDate"
        title="选择日期"
        :value="defaultValue"
        :formatter="formatter"
        @confirm="onFinish"
        @cancel="show = false"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import dayjs from "dayjs";
import Popup from "../Popup.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, FORM_FIELD_PROVIDER, validateRules } from "../form";
import { dateTimePickerFormatter } from "../../../util";
import { defaultContext, CONTEXT_PROVIDER } from "../context";
import formFieldMixins from "../formFieldMixins";

function format(type: string, value: Date) {
  let format = "YYYY-MM-DD";
  if (type == "year-month") {
    format = "YYYY-MM";
  } else if (type == "date") {
    format = "YYYY-MM-DD";
  } else if (type == "datetime") {
    format = "YYYY-MM-DD HH:mm";
  }

  return dayjs(value).format(format);
}

@Component({
  mixins:[formFieldMixins],
  components: {
    Popup,
    Placeholder,
  },
})
export default class  SelectDateTimeField extends Vue {
  @Prop() value!: Date;
  @Prop() placeholder!: string;
  @Prop() readonly!: any;
  @Prop({
    default: "date",
  })
  type!: "year-month" | "date" | "datetime";
  @Prop() minDate!: Date;
  @Prop() maxDate!: Date;
  @Prop() valueFormat!: string;
  @Prop() format!: string;
  

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get defaultValue() {
    return this.value ?? dayjs().hour(0).minute(0).second(0).toDate();
  }

  get text() {
    if (this.value) {
      return format(this.type, this.value);
    }
  }

  get elType() {
    let map = {
      "year-month": "month",
      date: "date",
      datetime: "datetime",
    };
    return map[this.type] ?? "date";
  }

  get mobile() {
    return this.context.isMobile;
  }

  show = false;

  @Inject({
    from: FORM_FIELD_PROVIDER,
    default: null,
  })
  field!: FieldContext;

  beforeMount() {
    if (this.field && this.field.component == null) {
      this.field.component = this;
      this.field.field.$on("trigger", this.onClick);
    }
  }

  formatter: Function = dateTimePickerFormatter;

  beforeDestroy() {
    if (this.field && this.field.component === this) {
      this.field.component = null;
      this.field.field.$off("trigger", this.onClick);
    }
  }

  async validate() {
    if (this.field) {
      let r = await validateRules(this.value, this.field.rules);
      return r?.detail[0]?.message ?? "";
    }
  }

  mounted() {
    this.refresh();
  }

  refresh() {}

  onFinish(value) {
    this.show = false;
    if(this.valueFormat==="timestamp") {
      value = dayjs(value).valueOf()
    }else if(this.valueFormat) {
      value = dayjs(value).format(this.valueFormat)
    }
    this.$emit("input", value);
  }

  onInput(v) {
    this.$emit("input", v);
  }

  onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }
}
</script>