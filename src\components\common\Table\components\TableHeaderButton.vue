<template>
  <el-button v-bind="newProps" class="table-header-button" :class="['table-header-button__'+this.newProps.type]" :icon="icon" @click="handleButtonClick">
    <span>{{ label }}</span>
  </el-button>
</template>

<script lang="ts">
import { Component, Vue, Prop} from "vue-property-decorator";

@Component({
  inheritAttrs:false,
})
export default class TableButton extends Vue {
  @Prop({ default: "" }) readonly label!: String;
  @Prop({ default: "" }) readonly icon!: String;
  @Prop({ default: false }) readonly click!: boolean | Function;
  @Prop({ default: ()=>{} }) readonly props!: Object;

  handleButtonClick(formItem){
    typeof this.click === "function" && this.click({
      props:this.props
    })
  }

  get newProps (){
    return {
      type:"primary",
      ...this.props
    }
  }
}

</script>

<style lang="less" scoped>
  .table-header-button{
    height: 36px;
    display: flex;
    padding: 0 16px;
    border-radius: var(--o-table-action-button-radius);
    align-items: center;

    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: var(--o-table-action-button-text);
    border:0;
    background: var(--o-primary-color);
    cursor: pointer;
    position: relative;
    span,/deep/i{
      position: relative;
      z-index: 1;
    }

    &:hover{
      &::after{
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        background: rgba(0, 0, 0, .15);
        border-radius: 8px;
        width: 100%;
        height: 100%;
        border-color:rgba(255, 255, 255, .15);
      }
    }

    &__plain{
      background: #fff;
      border:1px solid #CBCED8;
      &:hover{
        border-color: var(--o-primary-color);
        color: var(--o-primary-color);
        &::after{
          background: #fff;
        }
      }
    }
  }
</style>