:root {
	.o-top-select {

		background: #F7FAFD;
		border-radius: 8px;
		padding: 24px 16px;
		user-select: none;
		transform: all .3s;
		position: relative;

		.t-select-user-dialog{
			p{
				margin:0;
			}
			.index-left{
				border-color: #eee;
			}
			.el-button--default:not(.el-button--primary, .is-disabled){
				border-color: #DCDFE6;
			}
			.content-left{
				div{
					line-height: initial;
				}
			}
			.search-input input{
				width: 100%;
			}
			.el-input__suffix{
				right: 6px;
			}
			.el-input__suffix-inner{
				display: flex;
			}

		}
		.placeholder{
			color: var(--top-search-field-placeholder-color);
			font-weight: normal;
		}
		.t-select-user{
			.custom-select-container{
				border-radius: var(--top-search-field-border-radius);
				&.radio{
					height: 36px;
				}
				line-height: 36px;
				min-height: 36px;
				.flex-wrap{
					padding-top: 5px;
				}
				.mb-7px{
					margin-bottom: 5px;
				}
			}
		}

		

		&.action-float {
			display: flex;
			box-sizing: border-box;

			.operating-button {
				flex-shrink: 0;
				padding-top: 0;
				padding-left: 0 !important;
			}
		}


		.operating-button {
			padding-top: 8px;

			.search {
				width: 76px;
				height: 36px;
				background: var(--o-primary-color);
				border-color: var(--o-primary-color);
				border-radius: var(--top-search-btn-border-radius);
				font-family: PingFangSC-Regular;
				line-height: 36px;
				padding: 0;
				font-weight: 400;
				font-size: 14px;
				color: #ffffff;
				position: relative;
				box-sizing: border-box;
				border: 0;

				&:hover {
					&::after {
						content: "";
						position: absolute;
						left: 0;
						top: 0;
						background: rgba(0, 0, 0, .15);
						border-radius: 8px;
						width: 100%;
						height: 100%;
						border-color: rgba(255, 255, 255, .15);
					}
				}

				span {
					z-index: 1;
					position: relative;
				}
			}

			.reset {
				width: 76px;
				box-sizing: content-box;
				height: 36px;
				border: 1px solid var(--top-search-btn-reset-border-color);
				line-height: 36px;
				border-radius: var(--top-search-btn-border-radius);
				padding: 0;
				font-family: PingFangSC-Regular;
				font-weight: 400;
				font-size: 14px;
				color: var(--top-search-btn-reset-color);
				margin-left: 8px;

				&:hover,&:focus{
					border-color: var(--o-primary-color);
					color: var(--o-primary-color);
					background: #fff;
				}
			}
		}

		.t-remote-search-select{
			.el-select__caret.el-input__icon.el-icon-circle-close{
				&::before{
					position: relative;
    			top: -2px;
				}
			}
		}

		/* 下拉搜索按钮 popper 样式修改  */
		.custom-el-popper-wrap {
			.el-popper {
				margin-top: 8px;
			}

			.el-popper[x-placement^=bottom] .popper__arrow {
				display: none;
			}

			.el-select-dropdown {
				border: 0;
			}

			.el-select-dropdown__list {
				background: #FFFFFF;
				border: 0;
				box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.20);
				border-radius: 8px;
				border: 0;
				padding: 12px;
			}

			.el-select-dropdown__item.hover,
			.el-select-dropdown__item:hover {
				background: #F7FAFD;
				border-radius: 4px;
			}

			.el-select-dropdown__item.selected {
				color: var(--o-primary-color);
			}

			.el-cascader__dropdown {
				border: 0;
			}

			.el-cascader-node.in-active-path,
			.el-cascader-node.is-active,
			.el-cascader-node.is-selectable.in-checked-path {
				color: var(--o-primary-color);
			}
		}


		.form-render {
			flex: 1;
			
			.el-row:nth-child(2){
				.o-field{
						margin-bottom: 0;
				}
			}


			&.open{
				.el-row:nth-child(2){
					.o-field{
							margin-bottom: 16px;
					}
				}
			}

			.row {
				background: #F7FAFD;
				flex-wrap: wrap;

				.open {
					&.margin-r-0 {
						right: -77px;
					}

					font-family: PingFangSC-Regular;
					font-weight: 400;
					font-size: 14px;
					color: var(--o-primary-color);
					letter-spacing: 0;
					height: 36px;
					cursor: pointer;
					position: absolute;
					align-items: center;
					padding: 0 16px;
					display: flex;
					top: 2px;
					right: -37px;

					.oi-down {
						font-size: 12px;
						display: block;
						transition: all 0.3s;
						transform: rotate(0) scale(0.5) !important;
					}

					.oi-down.is-reverse {
						transform: rotate(180deg) scale(0.5) !important;
					}
				}
			}

			.dynamic-component {
				.el-icon-circle-close {
					display: block;
					font-family: "olading-iconfont" !important;

					&:before {
						color: #CCCED7;
						content: "\e653";
						font-size: 16px;
					}

					&:hover {
						&:before {
							color: #AAACB5;
						}
					}
				}

				&.t-input {
					.el-icon-circle-close {
						margin-top: -2px;
					}
				}
			}

			.t-date-picker {
				height: 36px;

				.t-date-pricker__date,.t-date-pricker__month {
					display: flex;
					width: 280px;

					.el-input__suffix {
						top: -2px;
					}

					.el-input__icon.el-icon-date {
						&::before {
							display: none;
						}
					}

					input {
						padding-left: 15px;
					}
				}
			}

			/* 日历组件  */
			.t-date-picker-popper {
				&.el-popper[x-placement^=bottom] .popper__arrow::after {
					display: none;
				}

				&.el-picker-panel {
					border: 0;
				}

				&.el-popper {
					margin-top: 8px;
				}

				.el-date-table td.today span {
					color: var(--o-primary-color);
				}

				.el-date-table td.end-date span,
				.el-date-table td.start-date span {
					background-color: var(--o-primary-color);
				}

				.el-date-table td.today.start-date span,
				.el-date-table td.today.end-date span {
					color: #fff;
				}
			}

			&.two-line{
				.o-field {
					margin-bottom: 16px;
				}
			}

			.o-field {
				&.margin-r-0 {
					margin-right: 0;
				}

				background: none;
				padding: 0;
				line-height: 36px;
				// height: 36px;
				margin-right: 40px;
				margin-bottom: 0;

				.label {
					font-family: PingFangSC-Regular;
					font-weight: 400;
					color: #24262a;
					color: var(--top-search-field-label-color);
					letter-spacing: 0;
					margin-bottom: 0;
					text-align: right;
					font-size: var(--top-search-field-label-font-size);
					line-height: 36px !important;
					margin-right: 12px;
				}

				.o-field-content {
					// width: 280px;
					// height: 36px;
					// flex-grow: initial;
					border-radius: 8px;
				}
			}


			.t-cascader {
				.el-radio__input.is-checked .el-radio__inner {
					border-color: var(--o-primary-color);
					background: var(--o-primary-color);
				}
				
			}
			.t-city-select{
				.el-cascader__suggestion-item{
					height: initial;
				}
			}

			.el-input__suffix {
				right: 10px;
			}

			.el-icon-arrow-up,
			.el-icon-arrow-down {
				transform: rotateZ(0deg) scale(0.5) !important;

				&.is-reverse {
					transform: rotateZ(180deg) scale(0.5) !important;
				}

				&[class*=" el-icon-"],
				[class^="el-icon-"] {
					font-family: "olading-iconfont" !important;
				}

				&::before {
					color: #777c94;
					content: "\e650";
					font-size: 12px;
				}
			}

			.t-member,
			.t-dept {
				.o-box {
					display: flex;
					align-items: center;
				}

				.o-chip-close {
					padding-right: 4px;
					padding-left: 2px;
				}
			}

			.o-field-content,
			.t-member {
				// height: 36px;
				display: flex;
				align-items: center;
				text-align: left;

				.o-placeholder{
					.flex-1{
						flex-wrap: wrap;
						display: flex;
					}
				}

				.member{
					text-align: left;
				}

				input{
					color: var(--top-search-field-input-color);
				}
				input::placeholder{
					color: var(--top-search-field-placeholder-color);
				}
				.el-input input,
				.o-select-member-field-body,
				.o-select-dept-field-body,
				.el-date-editor--date,
				.el-date-editor--daterange {
					width: 280px;
					height: 36px;
					flex-grow: initial;
					overflow: hidden;
					background: #ffffff;
					border: 1px solid var(--top-search-field-border-color);
					border-radius: var(--top-search-btn-border-radius);

					
					&:focus {
						border-color: var(--o-primary-color);
					}

					&:hover {
						border-color: var(--o-primary-color);
					}
				}
				.custom-select-container{
					border-radius: var(--top-search-field-border-radius);
					border: 1px solid var(--top-search-field-border-color);
				}

				.el-date-editor--date input {
					border: 0;
				}

				.o-select-member-field-body,
				.o-select-dept-field-body {
					min-width: 280px;
					// width: auto;
					min-height: 36px;
					height: auto;
				}
			}
		}
	}



}