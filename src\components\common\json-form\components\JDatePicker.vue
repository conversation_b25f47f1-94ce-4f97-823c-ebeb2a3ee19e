<template>
  <div>
    <div v-if="textModel">
      {{value}}
    </div>
    <div v-else>
      <el-date-picker
        :value="value"
        @blur="onBlur"
        popperClass="t-date-picker-popper"
        :disabled="isDisabled"
        @input="onInput"
        :class="{ 'exist-value':value }"
        v-bind="formItem"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules, ValidateTrigger } from "../../form";
import { DatePicker as DatePicker } from "element-ui";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
  components: {
    DatePicker,
  },
})
export default class TDatePicker extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;

  field!: FieldContext;
  error = false;

  beforeMount() {
    this.field = registerField(this);
  }

  @Watch("value")
  valueChange() {
    if (this.validateTrigger == "realtime") {
      this.validate();
    }
  }

  onBlur(e: any) {
    if (this.validateTrigger == "blur") {
      this.validate();
    }
  }


  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset() {
    this.onInput(this.formItem.defaultValue || "");
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }

    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }

    return r?.detail[0]?.message ?? "";
  }
}
</script>