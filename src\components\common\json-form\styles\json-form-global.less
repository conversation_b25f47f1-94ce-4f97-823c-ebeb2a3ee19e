:root {
	.o-json-form {

		.scrollbar_y{
			&::-webkit-scrollbar-thumb {
				border-radius: 10px;
				background: rgba(36,38,42,0.16);
			}
			&::-webkit-scrollbar {
				background: #fff;
				width: 6px;
			}
		}

		pre{
			white-space: pre-wrap !important;
			word-wrap: break-word !important;
			word-break: break-all;
		}

		.json-form-field-textarea{
			margin-bottom: 30px !important;
		}

		// PC端样式
		&.o-json-form-pc {
			background: #fff;

			.j-select-user-dialog{
				p{
					margin:0;
				}
				.index-left{
					border-color: #eee;
				}
				.el-button--default:not(.el-button--primary, .is-disabled){
					border-color: #DCDFE6;
				}

			}

			.el-input.is-disabled .el-input__inner{
				padding-right: 16px !important;
			}
			.j-new-select{
				.el-input.is-disabled .el-input__inner{
					padding-right: 32px !important;
				}
			}

			.j-radio .el-radio__label,.j-checkbox .el-checkbox__label{
				color: #24262A;
			}

			.el-input__suffix-inner {
				display: flex;
			}

			.o-form-block{
				margin: 0 auto;
			}

			.text-model{
				&>div{
					margin-bottom: 68px;
				}
				.o-form-section-title{
					margin-bottom: 10px;
				}
				.o-field {
					margin-bottom: 0;
					display: flex;
					align-items: flex-start;
					padding: 12px 0;
					.hint-primary{
						padding-right: 0;
					}
					.o-field-content-wrapper{
						min-height: inherit;
					}
					.field-left{
						align-items: flex-start;
						position: relative;
					}
					.o-field-content{
						padding-left: 24px;
						margin-top: 1px;
					}
					pre{
						font-weight: normal;
					}
					.label{
						color: #777C94;
						line-height: 14px;
						text-align: right;
						margin-bottom: 0;
						position: relative;
						top: 4px;
						.required{
							display: none;
						}
					}
				}
			}

			.o-select-member-field-body>div{
				display: flex;
				flex-wrap: wrap;
			}

			.el-input-number {
				width: 100%;

				.el-input-number__decrease {
					border-radius: 9px 0 0 10px;
				}

				.el-input-number__increase {
					border-radius: 0 8px 8px 0;
				}
			}

			.form-render {
				margin: 0 auto !important;
			}

			.dynamic-component {
				width: 100%;

				.el-icon-circle-close {
					display: block;
					font-family: "olading-iconfont" !important;

					&:before {
						color: #CCCED7;
						content: "\e653";
						font-size: 16px;
					}

					&:hover {
						&:before {
							color: #AAACB5;
						}
					}
				}

				&.t-input {
					.el-icon-circle-close {
						margin-top: -2px;
					}
				}
			}

			/* 下拉搜索按钮 popper 样式修改  */
			.custom-el-popper-wrap {
				.el-popper {
					margin-top: 8px;
				}

				.el-popper[x-placement^=bottom] .popper__arrow {
					display: none;
				}

				.el-select-dropdown {
					border: 0;
				}

				.el-select-dropdown__list {
					background: #FFFFFF;
					border: 0;
					box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.20);
					border-radius: 8px;
					border: 0;
					padding: 12px;
				}

				.el-select-dropdown__item.hover,
				.el-select-dropdown__item:hover {
					border-radius: 4px;
				}

				.el-select-dropdown__item.selected {
					color: var(--o-primary-color);
				}

				.el-cascader__dropdown {
					border: 0;
				}

				.el-cascader-node.in-active-path,
				.el-cascader-node.is-active,
				.el-cascader-node.is-selectable.in-checked-path {
					color: var(--o-primary-color);
				}
			}

			.t-cascader {
				.el-radio__input.is-checked .el-radio__inner {
					border-color: var(--o-primary-color);
					background: var(--o-primary-color);
				}
			}

			.row {
				flex-wrap: wrap;
			}

			.el-input__suffix {
				right: 10px;
			}

			.el-icon-arrow-up,
			.el-icon-arrow-down {
				transform: rotateZ(0deg) scale(0.5) !important;

				&.is-reverse {
					transform: rotateZ(180deg) scale(0.5) !important;
				}

				&[class*=" el-icon-"],
				[class^="el-icon-"] {
					font-family: "olading-iconfont" !important;
				}

				&::before {
					color: #777c94;
					content: "\e650";
					font-size: 12px;
				}
			}

			.o-field-content{

				.j-input{
					.passwordClass{
						input[type="password"]  + span{
							.el-icon-view::before{
								font-family: "olading-iconfont" !important;
								content: "\e658"
							}
						}
					}
				}


				.el-date-editor.el-input,
				.el-date-editor.el-input__inner,
				.el-cascader {
					width: 100%;
				}

				.el-input input,
				.el-date-editor--daterange,
				.o-placeholder {
					width: 100%;
					flex-grow: initial;
					overflow: hidden;
					background: #ffffff;
					border: 1px solid #eef0f4;
					border-radius: 8px;

					&:focus {
						border-color: var(--o-primary-color);
					}

					&:hover {
						border-color: var(--o-primary-color);
					}
				}

				.el-input__count {
					right: 2px;
					bottom: -24px;
				}

				.el-textarea__inner {
					display: block;
					resize: vertical;
					padding: 5px 15px;
					line-height: 1.5;
					box-sizing: border-box;
					width: 100%;
					font-size: inherit;
					color: #606266;
					background-color: #fff;
					background-image: none;
					border: 1px solid #EEF0F4;
					border-radius: 4px;
    			cursor:unset;
					transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
					.scrollbar_y()
				}

				.passwordClass {
					.el-input__inner {
						padding-right: 60px;
					}
				}
			}
		}

		&.o-json-form-mobile{
			.el-input.is-disabled .el-input__inner{
				background: #F5F7FA;
				color: #C0C4CC;
				border-color: #dcdfe6;
				opacity: 1;
			}
			.o-form-scene{
				margin:0;
			}
			.o-form-section-title{
				margin-bottom: 12px;
				position: relative;
			}
			.o-field{
				padding: 0;
				.o-placeholder,.o-select-field{
					border: 1px solid #DCDFE6;
					border-radius:4px;
					overflow: hidden;
					padding-left: 10px;
				}
				.j-input-range{
					.mb-10{
						margin-bottom: 0;
					}
				}
			
				.error-message{
					position: inherit;
				}
			}

			.json-form-field-datetime{
				.o-placeholder{
					margin-bottom: 10px;
				}
			}

			.o-form-block{
				border-radius: 4px;
				overflow: hidden;
				margin-bottom: 35px;
				background: #fff;
				padding-top: 14px;
			}
		}
	}

	.j-date-picker{
		font-family: "Avenir", Helvetica, Arial, sans-serif;
		.el-date-editor--daterange {
			padding-left: 16px;
			padding-right: 16px;
			&.exist-value {
				.el-range-separator {
					color: #606266;
					margin-right: 6px;
				}
				input {
					width: 72px;
				}
				&:hover {
					.el-icon-date {
						display: none;
					}
				}
			}
			input {
				text-align: left;
				width: 58px;
			}
			&.is-active {
				border-color: var(--o-primary-color);
			}
			.el-icon-date {
				right: 9px;
				position: absolute;
				&:before {
					font-family: "olading-iconfont" !important;
					content: "\e652";
				}
				/*display: none;*/
			}
			.el-range-separator {
				color: #cbced8;
				position: relative;
				top: -3px;
				margin-left: 3px;
				margin-right: 4px;
			}
			.el-range__close-icon {
				right: 8px;
				// background: red;
				position: absolute;
			}
		}
	}

	/* 日历组件  */
	.t-date-picker-popper {
		&.el-popper[x-placement^=bottom] .popper__arrow::after {
			display: none;
		}

		&.el-picker-panel {
			border: 0;
		}

		&.el-popper {
			margin-top: 8px;
		}

		// .el-date-table td.today span {
		// 	color: var(--o-primary-color);
		// }

		.el-date-table td.end-date span,
		.el-date-table td.start-date span {
			background-color: var(--o-primary-color);
		}

		.el-date-table td.today.start-date span,
		.el-date-table td.today.end-date span {
			color: #fff;
		}
	}
}