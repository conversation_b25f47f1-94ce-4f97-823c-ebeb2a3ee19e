import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';

import Comp from './SelectCityField.vue';

<Meta title="业务组件/o-select-city-field" component={Comp} argTypes={{
  readonly: {
    type: 'boolean'
  },
  placeholder: {
    type: 'string'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  data: () => ({ city: "" }),
  template: '<o-select-city-field v-bind="$props" v-model="city"></o-select-city-field>',
});


# 选择城市组件

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

最多显示到第一级

<Canvas>
  <Story 
    name="max-deep"
    args={{
      maxDeep: 0
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

设置可选择的深度

<Canvas>
  <Story 
    name="selectable-deep"
    args={{
      selectableDeep: [0, 2]
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

设置只能选择末级节点

<Canvas>
  <Story 
    name="only-select-leaf"
    args={{
      onlySelectLeaf: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>
