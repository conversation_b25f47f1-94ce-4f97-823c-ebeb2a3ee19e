<template>
  <div v-loading="loading">
    <div v-if="textModel">
      {{valueText}}
    </div>
    <el-cascader
      v-show="!textModel"
      :value="value"
      :appendToBody="true"
      :placeholder="placeholder"
      ref="cascader"
      collapse-tags
      filterable
      class="custom-el-popper-wrap"
      :disabled="isDisabled"
      :options="options"
      @input="onInput"
      clearable
      :props="{
        multiple:$attrs.multiple,
        checkStrictly:$attrs.checkStrictly
      }"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules } from "../../form";
import { Cascader as ElCascader } from "element-ui";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";
import { listCity } from "../../../../util";
import _ from "lodash";

let cityListJson:any = [],
  cityMapIds:any = {}

@Component({
  inheritAttrs: false,
  mixins:[formFieldMixins],
  components: {
    ElCascader,
  },
})
export default class JCitySelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() rules!: any[];
  @Prop() placeholder!: String;

  @Ref("cascader") 
  cascader:any

  field!: FieldContext;

  valueText = ""
  error = false;
  options = cityListJson
  loading = false
  isWaitSetValue = false

  // @Watch("value",{
  //   immediate:true
  // })
  // onWatchValue(){
  //   this.setValueText()
  //   this.setValue()
  // }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  async setValueText (){
   
  }

  setValue(value){
    if(Array.isArray(value) && !this.$attrs.multiple) return  
    if(typeof value === "string" || Array.isArray(value[0])){
      if(cityListJson.length === 0 && this.loading) {
        this.isWaitSetValue = true
      }else if (cityListJson.length) {
        this.getStrValueId(value)
      }
    }
  }

  getStrValueId(value?:String | any[]){
    const newValue = value || this.value
    const getIds = (id)=>{
      const ids:string[] = []
      let cityItem = cityMapIds[id]
      while (cityItem) {
        ids.unshift(cityItem.id as string)
        cityItem = cityItem.parentId?cityMapIds[cityItem.parentId]:null
      }
      return ids
    }

    if(!this.$attrs.multiple) {
      return this.onInput(getIds(newValue))
    }else{
      const ids = (newValue as any[]).map(item=>getIds(item[0]))
      return this.onInput(ids)
    }
  }

  get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v.length==0?null:v, this.actualRules);

    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }

    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }

    return r?.detail[0]?.message ?? "";
  }

  onFieldReset(){
    if(Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue)
    }
  }

  created(){
    this.loadCityList()
  }

  async loadCityList(){

    const formatterCityList = (data)=>{
      const map = cityMapIds = {}
      const newList:any = []
      data.forEach((item:any)=>{
        item.children = item.childrenNum ? []:null
        item.value = item.id
        item.label = item.name
        map[item.id] = item
        if(map[item.parentId]) {
          map[item.parentId].children.push(item)
        }
        if(!item.parentId) newList.push(item)
      })
      return newList
    }

    if(cityListJson.length) {
      this.options = _.cloneDeep(cityListJson)
      return 
    }
    
    this.loading = true

    const { list } = await listCity({
      filters: {
        country: ["CHN"],
      },
      start:0,
      limit:99999,
    })

    this.loading = false

    cityListJson = this.options = _.cloneDeep(formatterCityList(list))

    if(this.isWaitSetValue){
      this.getStrValueId()
    }

  }
}
</script>