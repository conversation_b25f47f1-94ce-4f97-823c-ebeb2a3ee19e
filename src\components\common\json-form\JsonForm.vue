<template>
  <div class="o-json-form" :class="jsonFormClass">
    <!-- 表单渲染组件 -->
    <json-form-render
      :formJson="cFormJson"
      class="form-render"
      :class="[textModel?'text-model':'']"
      :readonly="readonly"
      :textModel="textModel"
      :disabled="disabled"
      :labelPosition="labelPosition"
      :validateTrigger="validateTrigger"
      :save="onSave"
      :formItemMap="formItemMap"
      :showResetButton="showResetButton"
      :hideFootButton="hideFootButton"
      :saveButtonText="saveButtonText"
      :labelWidth="parse2px(labelWidth)"
      v-bind="$attrs"
      @valueTextChange="valueTextChange"
      @change="onChange"
      :columns="columns"
      v-on="$listeners"
      ref="json-form-render"
    />
  </div>
</template>

<script lang="ts">
import {
  Component,
  Vue,
  Prop,
  Ref,
  Watch,
  Emit,
  Provide,
} from "vue-property-decorator";
import { field2Options } from "../TopSelect/utils";
import JsonFormRender from "./JsonFormRender";
import { FormItem, TFormJson } from "./type";
import _ from "lodash";
import { parse2px } from "../util";
import { ValidateTrigger } from "../form";
import isMobile from "is-mobile";

@Component({
  components: {
    JsonFormRender,
  },
})
export default class JsonForm extends Vue {
  // 筛选配置JSON
  @Prop({ default: () => {} }) readonly formJson!: TFormJson;
  // 设置表单宽度
  @Prop({ default: "" }) readonly width!: string | number;
  // 只读状态
  @Prop() readonly save!: Function | string;
  // 只读状态
  @Prop() readonly readonly!: boolean;
  // 是否文本查看模式
  @Prop() readonly textModel!: boolean;
  // 禁用状态
  @Prop() readonly disabled!: boolean;
  // 是否显示重置按钮
  @Prop({ default: false }) readonly showResetButton!: boolean
  // 左右模式或者上下模式。top 为上下，left/right 为左右（指左右时，label 是左对齐或右对齐）
  @Prop() readonly labelPosition!: "top" | "left" | "right";
  // 左右模式时，label 的宽度
  @Prop() readonly labelWidth!: string
  // 是否隐藏底部操作按钮
  @Prop() readonly hideFootButton!: boolean;
  // 列
  @Prop() readonly columns!: number;
  // 保存按钮
  @Prop() readonly saveButtonText!: string;
  // 校验方式
  @Prop({ default:"submit" }) readonly validateTrigger!: ValidateTrigger;
  
  @Ref("json-form-render") jsonFormRender!: any;

  @Provide("JsonForm")
  JsonForm = this;

  cFormJson: TFormJson = [];
  

  formItemMap:Record<string,FormItem> = {}
  formGroupMap = {}

  parse2px = parse2px

  get jsonFormClass(){
    return `o-json-form-${isMobile()?'mobile':'pc'}`
  }

  @Watch("formJson", {
    deep: true,
    immediate: true,
  })
  onWatchFormJson(formJson) {
    this.initCFormJson(formJson)
    // console.log("jsonForm onWatchFormJson ");
  }

  @Watch("width",{ immediate:true })
  async onWidthWatch (){
    await this.$nextTick()
    this.$el.querySelectorAll(".o-form-block").forEach(el=>{
      (el as HTMLElement).style.width = parse2px(this.width)
    })
  }

  initCFormJson(formJson){
    this.cFormJson = _.cloneDeep(formJson);

    // 递归 获取选项集合
    const getFormJsonItems = (item, callback) => {
      const result = [];
      const pushItem = (item, result) => {
        if (Array.isArray(item.children)) {
          item.children.forEach((item) => {
            pushItem(item, result);
          });
          if(!("ifShow" in item)) this.$set(item,"ifShow",true)
          this.formGroupMap[item.groupId] = item
        } else {
          callback && callback(item);
          result.push(item);
        }
      };
      if (Array.isArray(item)) {
        item.forEach((item) => pushItem(item, result));
      }
      return result as any;
    };

    getFormJsonItems(this.cFormJson, (item) => {

      const formItem = item.item

      if(!("ifShow" in formItem)) this.$set(formItem,"ifShow",true)

      formItem._parentItem = item

      this.formItemMap[formItem.prop] = formItem

      // 用户传入filed字段 组件内自动帮他 做映射关系
      if (formItem.field && Array.isArray(formItem.options)) {
        field2Options(formItem.options, formItem.field);
      }

      if(Array.isArray(formItem.options)) field2Options(formItem.options, "label as text");
    
    });

  }
  
  getJsonFormItem (prop){
    const formItem = this.formItemMap[prop]
    if(!formItem) return null
    return (formItem as any)._parentItem
  }

  setFieldRequired(prop,required){
    if(!prop) return console.error("props is undefined","setRequire")

    const setFieldRequire = (prop,required)=>{
      try {
        const jsonFormItem = this.getJsonFormItem(prop)
        jsonFormItem.field.rules[0].required = required
      }catch(error){
        console.error(error)
      }
    }
    
    if(Array.isArray(prop)){
      prop.forEach(key=>setFieldRequire(key,required))
      return
    }

    setFieldRequire(prop,required)

  }

  valueTextChange(prop,value){
    if(!prop || !this.formItemMap[prop]) return 
    this.$set(this.formItemMap[prop],"valueText",value)
  }

  // 监听数据发生变化
  @Emit("change")
  onChange (formData){
    return formData
  }

  setFormJson(formJson){
    this.initCFormJson(formJson)
    this.$nextTick(()=>{
      this.jsonFormRender.initFormData()
      this.$nextTick(this.clearFieldsErrorMessage)
    })
    return this
  }

  clearFieldsErrorMessage(){
    this.jsonFormRender.clearFieldsErrorMessage()
  }

  // 获取日期中 用户设置的 startField 和 endField 字段集合的映射
  get formJsonDateFieldMap() {
    const map = {};
    for(let key in this.formItemMap) {
      const formItem = this.formItemMap[key]
      if(!(formItem.startField && formItem.endField)) continue
      map[formItem.prop] =  {
        startField: formItem.startField,
        endField: formItem.endField,
      }
    }
    return map;
  }

  async onSave(formData){
    const setFormDataField = (fieldMap,value)=> {
      formData[fieldMap.startField] = value[0] ?? ""
      formData[fieldMap.endField] = value[1] ?? ""
    }
    for(let key in formData) {
      const fieldMap = this.formJsonDateFieldMap[key]
      const value = formData[key]
      // 如果用户没有设置字段映射 或者 值不是 数组 
      if(!fieldMap || !_.isArray(value)) continue;
      setFormDataField(fieldMap,value)
    }
    _.isFunction(this.save) && await this.save(formData)
  }

  // 动态执行校验
  validator(prop){
    return this.jsonFormRender.validator(prop)
  }

  // 获取指定选项的Ref
  getFormItemRef(prop){
    return this.jsonFormRender.getFormItemRef(prop)
  }

  // 动态设置下拉选项 数据源
  setOptions(data) {
    const formItemMap = this.formItemMap
    Object.keys(data).forEach(key=>{
      if(formItemMap[key]) {
        if(data[key].length) {
          const defaultField = "label as text"
          let field = formItemMap[key].field
          field = field? `${field},${defaultField}`:`${defaultField}`
          field2Options(data[key], field);
          this.$set(formItemMap[key],"options",data[key])
        }else{
          this.$set(formItemMap[key],"options",[])
        }
      }
    })
    return this
  }

  // 获取选项的数据源
  getOptions(prop?:string){
    const getOptions = (prop)=> this.formItemMap[prop]?.options || []

    // 如果传入指定的 Prop
    if(prop){
      return {
        [prop]: getOptions(prop)
      }
    }
    // 否则获取全部prop的options 
    return Object.keys(this.formItemMap).reduce((acc,cur)=>{
      const options = getOptions(cur)
      if(options.length) acc[cur] = options
      return acc
    },{})
  }
  
  getOptionsItem (field:string,value){
    const options = this.getOptions(field)[field] as any[]
    const result = options.find(item=>item.value === value)

    return result
  }

  // 获取异步选项，为了避免影响 原来的数据 ， copy 一个 getOptionsItem 接口
  async getAsyncOptionsItem (field:string,value){
    let options = this.getOptions(field)[field] as any[]
    if(_.isFunction(options)) options = await options()
    const result = options.find(item=>item.value === value)
    return result
  }

  // 设置是否显示隐藏字段
  setIsShowField(map:Record<string,boolean>){
    for(let key in this.formItemMap) {
      if(key in map) this.$set(this.formItemMap[key],"ifShow", map[key])
    }
    return this
  }

  setLabel(data){
    const formItemMap = this.formItemMap
    Object.keys(data).forEach(key=>{
      if(formItemMap[key]) {
        if(data[key].length) {
          this.$set(formItemMap[key],"label",data[key])
        }
      }
    })
    return this
  }

  // 设置分组显示隐藏
  setIsShowGroup(map:Record<string|number,boolean>){
    for(let key in this.formGroupMap) {
      if(key in map) this.$set(this.formGroupMap[key],"ifShow", map[key])
    }
    return this
  }

  // 设置字段禁用状态
  setDisabled(map){
    for(let key in this.formItemMap) {
      if(key in map) this.$set(this.formItemMap[key],"disabled", map[key])
    }
    return this
  }

  // 重置
  resetForm(){
    this.jsonFormRender.resetForm()
  }

  // 设置分组或者字段 显示隐藏
  setIfShow(map:Record<string,any>){
    if(map.group) this.setIsShowGroup(map.group)
    if(map.field) this.setIsShowField(map.field)
    return this
  }

  // 获取表单数据
  getFormData(noValidate?:boolean) {
    return this.jsonFormRender.getFormData(noValidate);
  }

  // 填充表单数据
  setFormValue(formData) {
    this.jsonFormRender.setFormData(formData);
    return this
  }
}
</script>
<style lang="less">
@import "./styles/json-form-global.less";
</style>