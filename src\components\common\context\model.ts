import isMobile from "is-mobile";

export const CONTEXT_PROVIDER = "__o_context__"


export interface ContextProps {
  device?: "mobile" | "desktop";
}

export class Context {

  _props: ContextProps | null = null

  get props(): ContextProps | null {
    return this._props
  }

  set props(v: ContextProps | null) {
    this._props = v
  }

  get isMobile() {
    if (this.props?.device) {
      return this.props?.device == "mobile"
    } else {
      return isMobile()
    }
  }
}

export const defaultContext = new Context()
defaultContext.props = {
  device: isMobile() ? "mobile" : "desktop"
}

