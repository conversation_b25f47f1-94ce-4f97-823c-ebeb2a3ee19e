

<template>
  <o-form
    ref="form"
    class="form-render"
    :class="[isTwoLine || isOpen?'two-line':'']"
    :label-width="labelWidth"
    :model="formData"
  >
    <el-row
      type="flex"
      class="row"
      ref="el-row"
    >
      <div
        class="el-row"
        ref="el-row-item"
        :style="rowMarginRight(index)"
        v-for="(item,index) in _formJson"
        :key="item.formItem.prop"
        v-show="showFormItem(index,item)"
      >
        <o-field
          :label="item.formItem.label"
          :label-width="item.formItem.labelWidth"
          :class=[isRowLast(index)]
          v-bind="getFieldProp(item.formItem)"
        >

          <component
            v-bind="item.formItem"
            v-if="item.type==='custom'"
            class="dynamic-component"
            :class="[getCommentName(item)]"
            :is="item.formItem.component"
            :formItem="item.formItem"
            :ref="item.formItem.prop"
            @keyup.enter.native="oTopSelect.handleSearchClick()"
            @input="ctmInput"
            v-model="formData[item.formItem.prop]"
          />

          <component
            :is="getCommentName(item)"
            class="dynamic-component"
            :class="[getCommentName(item)]"
            :formItem="item.formItem"
            v-bind="item.formItem"
            @keyup.enter.native="oTopSelect.handleSearchClick()"
            v-else
            clearable
            @input="componentInput(item.formItem,formData[item.formItem.prop])"
            v-model="formData[item.formItem.prop]"
          />

        </o-field>
        
        <!-- 展开收起按钮 -->
        <div
          v-if="showOpenBtn(index)"
          type="text"
          class="open open-button flex"
          :class="[isRowLast(index)]"
          @click="open"
        >
          {{ buttonText }}
          <i
            class="icon olading-iconfont oi-down "
            :class="{'is-reverse':isOpen}"
          ></i>
        </div>
      </div>
    </el-row>
  </o-form>
</template>

<script lang="ts">

/**
 * 校验的大致流程
 * 1. field 组件 初始化，会实例化 FieldContext 这个类 ， 然后再 beforeMount 这个生命周期 ， 获取插槽下的所有组件 ， 放入到 fieldContext.component 属性下
 * 2. fieldContext 实例化成功以后 会拿去到 当前field 组件用户传入的 prop rules, 设置为 当前实例的 rules 属性。
 * 3. 用户点击 校验 调用 formContext.validate 这个方法,
 * 4. FormContext.validate 会找到 所有 FieldContext 实例，并且依次循环调用 下面 component 的 validate 方法。
 * 5. component 对应的就是 field 下面插槽对应的组件，例如：o-input , 
 * 6. 然后会调用 o-input 的 validate 方法， validate 会调用 validateCore 方法
 * 7. validateCore 方法传入 一个参数 (value：当前组件选中或者填写的值) 
 * 8. validateCore 会拿到当前 field 上 用户传入的 rules , 在个 调用全局 validateRules 方法进行 校验 
 * 9. 最后 validateCore 会返回错误信息 errorMsg 
 * */ 

import { addResizeListener, removeResizeListener } from "../util";
import { TFormJson } from "./type";
import lodash from "lodash";
import {
  Component,
  Vue,
  Emit,
  Prop,
  Ref,
  Inject,
  Watch,
} from "vue-property-decorator";
import { field2Options } from "./utils";

const files = import.meta.globEager("./components/*.vue")
const modules = {};

for(var key in files){
  const file = files[key]
  const tmp = key.split('/')
  modules[tmp[2].replace(/(\.\/|\.vue)/g, "")] = file.default
}

const oFieldMarginRight = 40


// 组件名称不统一，需要做一层映射
enum DynamicComponentName {
  // input = "t-input",
  input = "t-input",
  dept = "t-dept",
  datePicker = "t-date-picker",
  // select = "o-select-field",
  select = "t-select",
  selectUser = "t-select-user",
  cascader = "t-cascader",
  citySelect = "t-city-select",
  member = "t-member",
  remoteSearchSelect = "t-remote-search-select",
}

@Component({
  inheritAttrs:false,
  components: {
    ...modules,
  },
})
export default class FormRender extends Vue {
  @Prop({ default: () => {} }) readonly formJson!: TFormJson;
  @Prop({ default: "56px" }) readonly labelWidth!: string;
  @Prop({ default: false }) readonly isOpen!: Boolean;
  // 外部显示多少个筛选项 ， 没有展开的情况下
  @Prop({ default: 2 }) readonly outSelectNumber!: Number;

  @Ref("form") form: any;
  @Ref("el-row") elRow: any;
  @Ref("el-row-item") elRowItem: any;

  @Inject({ from: "oConCationContext", default: null })
  readonly oConCationContext!: any;

  @Inject({ from: "oTopSelect", default: null })
  readonly oTopSelect!: any;

  formData: Record<string, any> = {};

  react = {
    oTopSelectWidth:0,
    oTopSelectHight:0,
    isIntersectButton:false
  }

  get _formJson() {
    if (!this.formJson.length) return [];
    

    const cloneFormJson = lodash.cloneDeep(this.formJson).filter(item=>{
      if(!("ifShow" in item.formItem)) return true
      return item.formItem.ifShow
    });

    for (let { formItem } of cloneFormJson) {
      // 用户传入filed字段 组件内自动帮他 做映射关系
      if (formItem.field && Array.isArray(formItem.options))
        field2Options(formItem.options, formItem.field);
    }

    return cloneFormJson;
  }

  get isTwoLine(){
    return this.react.oTopSelectHight>52 ;
  }

  // 获取日期中 用户设置的 startField 和 endField 字段集合的映射
  get formJsonDateFieldMap (){
    const map = {} 
    for(let { formItem } of this.formJson) {
      if(formItem.startField && formItem.endField) {
        map[formItem.prop] = {
          startField:formItem.startField,
          endField:formItem.endField,
        }
      }
    }
    return map
  }

  get buttonText(){
    return this.isOpen ? "收起" : "展开"
  }

  getCommentName(item) {
    const componentName = item.type;
    if(item.type === "custom") return componentName
    return DynamicComponentName[componentName];
  }

  created() {
    this.initFormData();
  }

  @Emit()
  open() {
    this.oConCationContext?.backToTop();
    return this.isOpen;
  }

  // 获取 收起状态 用户设置的在最外层 展示的筛选项
  get isOutOpt() {
    let idx: number[] = [];

    const isOuts = this.formJson.filter((item, index) => {
      const result = item.isOut;
      if (result) idx.push(index);
      return result;
    });

    const len = isOuts.length;

    return {
      len: isOuts.length,
      // 这里需要获取 最后展示元素的索引 ， 因为用户可能 在FormJson 第五项 设置展示，
      // 那下边 展示更多按钮的时候 循环index 必须 === 这个lastIndex 才进行展示
      lastIndex: len ? idx[len - 1] : 0,
    };
  }

  showOpenBtn(index) {
    // 如果存在交集 隐藏选项旁边的展开收起按钮
    // if(this.react.isIntersectButton) return

    // 如果就只有2个选项 ，无需显示展开收起按钮
    if (this._formJson.length < 3) return false;

    // 如果当前是收起状态，并且用户设置了 isOut , 则获取 isOut长度 是最后展示的筛选项，并显示收起/展开 按钮
    if (!this.isOpen) {
      if (this.isOutOpt.len) return index === this.isOutOpt.lastIndex;
      // 否则 只展示 outSelectNumber 筛选项即可
      return (index+1) === this.outSelectNumber;
    }

    // 展开状态 , 只有在最后一个 选项后边 出现展开按钮
    return index === this._formJson.length - 1;
  }

  showFormItem(index, item) {
    // 如果用户设置了 formJson isOut , 在没有展开的情况下优先使用用户设置 isOut 选项显示
    if (item.isOut && !this.isOpen) return item.isOut;
    if (this.isOpen) return true;
    return index < this.outSelectNumber;
  }
  
  // 设置是否存在交集
  setIsIntersectButton(){
    if(!this.$el) return false
    // 获取展开收起按钮元素
    const openBtnEl = this.$el.querySelector(".open-button") as HTMLDivElement
    // 如果没有展开收起按钮 ， 直接终止下面程序执行
    if(!openBtnEl) return this.react.isIntersectButton = false
    // 展开收起按钮父元素宽度 = el-row 宽度
    const openBtnParentElWidth = (openBtnEl.offsetParent as HTMLElement)?.offsetWidth || 0
    // topSelect 左侧边距
    const topSelectPaddingLeft = 16
    // 展开收起按钮 距离容器左侧的距离
    const openBtnLeft = openBtnEl.offsetLeft + openBtnEl.offsetWidth + openBtnParentElWidth + topSelectPaddingLeft
    // 查询重置组合按钮距离 左侧容器的距离
    const actionButtonOffsetLeft = (this.$el.parentNode?.querySelector(".operating-button") as HTMLDivElement)?.offsetLeft
    // 展开收起按钮 距离左侧的距离 > 查询重置组合按钮距离左侧的距 && 不是展开状态 ，代表他们之间有交集
    this.react.isIntersectButton = openBtnLeft>actionButtonOffsetLeft && !this.isOpen
  }

  async getFormData() {
    // let ok = await this.form.validate();
    // // 如果不写rule 也会校验失败，暂时先不用校验了
    // if (!ok) {
    //   return
    // } 

    const data = { ...this.formData }
    for(let prop of Object.keys(this.formJsonDateFieldMap) ) {
      const dateValue = data[prop]
      const map = this.formJsonDateFieldMap[prop]
      
      if(Array.isArray(dateValue)) {
        data[map.startField] = dateValue[0] || ""
        data[map.endField] = dateValue[1] || ""
      }else{
        data[map.startField] = ""
        data[map.endField] = ""
      }
    }

    return data
  }

  // 获取每个筛选项+label 占用宽度 , 因为渲染之前就得知道 宽度，所以没办法动态获取元素拿到宽度
  get fieldItemWidth (){
    const fieldContentWidth = 280, 
      oFieldLabelMarginRight = 12 + 40
    return Number((this.labelWidth as string).replace("px","")) + fieldContentWidth + oFieldLabelMarginRight 
  }

  setFormData(formData) {
    this.formData = { ...this.formData, ...formData };
  }

  setFormValue(formData){
    this.setFormData(formData)
  }

  ctmInput(prop, value) {
    this.formData[prop] = value;
  }

  componentInput({ onInput },value){
    onInput && onInput(value,this.$parent)
  }

  initFormData() {
    for (let item of this.formJson) {
      const value = item.formItem.defaultValue ?? item.formItem.value ?? "";
      this.$set(this.formData, item.formItem.prop, value);
    }
    this.$watch("formData",(newValue)=>{
      this.$emit("change",newValue)
    },{
      deep:true
    })
  }

  getFieldProp(item){
    const props:Record<string,any> = {}
    if(item.rules && item.rules.length) {
      props.rules = item.rules
    }
    return props
  }

  // 是否为横向最后一个元素
  isRowLast(index){

    let rowNumber = Math.floor(this.react.oTopSelectWidth / this.fieldItemWidth);
    rowNumber = Math.floor((this.react.oTopSelectWidth - (rowNumber-1)*oFieldMarginRight)/0)

    if(!rowNumber) return ""

    return ( index + 1 ) % rowNumber === 0 ? "margin-r-0" : ""
  }

  resetForm() {
    this.form.context.resetFields();
    this._formJson.forEach((item) => {
      if (item.type === "custom") {
        const customComponent = (this.$refs[item.formItem.prop][0] as any)
        if(!customComponent) return
        if(customComponent.clearField)customComponent.clearField()
        if(customComponent.onFieldReset)customComponent.onFieldReset()
      }
    });
  }

  resizeListener(){

    this.react.oTopSelectWidth = this.elRow.$el.clientWidth; 
    this.react.oTopSelectHight = this.elRow.$el.clientHeight; 
    this.setIsIntersectButton()

    // const ua = navigator.userAgent
    // if(ua.toLocaleLowerCase().includes("firefox")) {
    //   this.oTopSelectWidth = that.$el.clientWidth; 
    // }else{
    //   this.oTopSelectWidth = that.$el.parentNode.clientWidth; 
    // }
  }
  
  // 添加marginRight 
  rowMarginRight(index){
    // 如果存在交集，并且 有第二个元素 ， 设置marginRight , 避免交集 带来的错位，直接换到下一行即可
    if(this.react.isIntersectButton && index===1) {
      return {
        marginRight:"20px"
      }
    }
    return null
  }

  mounted(){
    this.resizeListener()
    addResizeListener(this.$el, this.resizeListener);
  }
  

  beforeDestroy() {
    removeResizeListener(this.$el,this.resizeListener)
  }

}
</script>