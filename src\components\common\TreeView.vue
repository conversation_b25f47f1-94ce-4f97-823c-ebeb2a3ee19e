<template>
  <div class="tree-view">
    <div v-if="enableSearch" class="search-input">
      <OInput v-bind="inputProps" v-model="form.search"></OInput>
    </div>
    <div
      v-if="!hideNavBar && !keywords"
      class="tree-view-nav-wrap"
      ref="tree-view-nav-wrap"
    >
      <div class="tree-view-nav">
        <span v-for="(item, i) in levels" :key="item.id">
          <span v-if="i != 0">
            <i class="el-icon-arrow-right"></i>
          </span>
          <span @click="onClickNavItem(item)" class="tree-view-nav-item">
            {{ item.name }}</span
          >
        </span>
      </div>
    </div>
    <div class="tree-view-body">
      <van-list :loading="loading" :finished="finished" @load="loadNext()">
        <template v-if="!records.length && !loading">
          <van-empty description="暂无数据" />
        </template>
        <template v-for="(item, index) in records">
          <div class="tree-view-record" :key="getKey(item)">
            <checkbox
              class="tree-view-checkbox"
              v-if="item.selectable"
              :value="item.checked"
              :disabled="item.disabled"
              @input="onClickContent(item)"
            ></checkbox>
            <div
              class="tree-view-content"
              @click.stop="onClickContent(item)"
            >
              <slot :item="item" :index="index"></slot>
            </div>
            <div
              v-if="item.isDirectory"
              :key="index"
              @click.stop="onClickDown(item)"
            >
              <slot name="directory"></slot>
              <div v-if="!$slots['directory']" class="directory-buttons">
                <i class="el-icon-arrow-down" ></i>
                <span>下一级</span>
              </div>
            </div>
          </div>
        </template>
      </van-list>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from "vue-property-decorator";
import _ from "lodash";
import Checkbox from "./Checkbox.vue";
import OInput from "./input";

/** 分页数据加载器 */
type PagedLoader = (context: {
  keywords?: string;
  directory?: Record;
  start?: number;
  limit?: number;
}) => Promise<Record[]>;

interface Record {
  id: string;
  name?: string;
  checked?: boolean;
  isDirectory?: boolean;
  selectable?: boolean;
  disabled?: boolean;
}

interface ISearchInputProps {
  placeholder?: string;
  prefixIcon?: string;
}

@Component({
  components: {
    Checkbox,
    OInput,
  },
})
export default class ListView extends Vue {
  @Prop() loader!: PagedLoader;
  @Prop() value!: string[];
  @Prop() selected!: string[];
  @Prop() disabled!: string[];
  /**是否隐藏导航条 */
  @Prop() hideNavBar!: boolean;

  @Prop() searchInputProps!: ISearchInputProps;

  @Prop({
    default: false,
  })
  enableSearch!: boolean;
  @Prop({
    default: 30,
  })
  limit!: number;
  @Prop({
    default: "公司",
  })
  rootTitle!: string;

  @Ref("tree-view-nav-wrap") treeViewNavWrap!: HTMLElement;

  form = {
    search: "",
  };

  directory?: Record = null as any;
  directoryLevels: any[] = [];
  currentSelected: string[] = [];
  records: Record[] = [];
  loading: boolean = false;
  finished = false;
  keywords: string = "";
  inSearch = false;

  get levels() {
    return [
      {
        name: this.rootTitle,
      },
      ...this.directoryLevels,
    ];
  }

  get inputProps() {
    const defConfig: ISearchInputProps = {
      placeholder: "请输入内容",
      prefixIcon: "el-icon-search",
    };
    return Object.assign(defConfig, this.searchInputProps);
  }

  getKey(item: Record) {
    let prefix = item.isDirectory ? "dir_" : "item_";
    return `${item.id}`;
  }

  @Watch("loader")
  onLoaderChanged() {
    this.refresh();
  }

  @Watch("directory")
  onDirectoryChanged() {
    this.refresh();
  }

  @Watch("selected")
  onSelectedChanged() {
    this.measure();
  }

  @Watch("disabled")
  onDisabledChanged() {
    this.measure();
  }

  @Watch("value")
  onValueChanged() {
    this.measure();
  }

  @Watch("levels")
  async onLevelWatch() {
    const treeViewNavWrap = this.treeViewNavWrap;
    if (!treeViewNavWrap) return;
    await this.$nextTick();
    treeViewNavWrap.scrollLeft = treeViewNavWrap.offsetWidth;
  }

  doSearch = _.debounce(
    (s) => {
      this.search(s);
    },
    1000,
    {
      leading: false,
      trailing: true,
    }
  );

  @Watch("form.search")
  onSearchChanged() {
    this.doSearch(this.form.search);
  }

  mounted() {
    this.refresh();
  }

  measure() {
    this.records.forEach((o) => {
      if (this.value != null && this.value.find((id) => o.id == id) != null) {
        Vue.set(o, "checked", true);
      } else {
        Vue.set(o, "checked", false);
      }
      if (
        this.disabled != null &&
        this.disabled.find((id) => o.id == id) != null
      ) {
        Vue.set(o, "disabled", true);
      } else {
        Vue.set(o, "disabled", false);
      }
    });
  }

  async refresh() {
    await this.reload();
  }

  async reload() {
    this.records.splice(0, this.records.length);
    this.currentSelected.splice(0, this.currentSelected.length);
    this.finished = false;
    await this.load(this.keywords);
  }

  async load(keywords: string) {
    if (!this.loader) {
      return;
    }
    if (this.loading) {
      return;
    }

    this.loading = true;
    try {
      let records = await this.loader({
        keywords: keywords ? keywords : undefined,
        directory: this.directory,
        start: this.records.length,
        limit: this.limit,
      });

      if (records.length == 0 || records.length < this.limit) {
        this.finished = true;
      }

      this.records.push(...records);
      this.measure();
    } catch (e) {
      this.finished = true;
      throw e;
    } finally {
      this.loading = false;
    }
  }

  async loadNext() {
    await this.load(this.keywords);
  }

  async search(keywords: string) {
    if (!this.keywords && !keywords.trim()) {
      return;
    }
    this.keywords = keywords.trim();
    await this.reload();
  }

  onClickDown(item: Record) {
    this.directory = item;
    this.directoryLevels.push(item);
  }

  onClickContent(item: Record) {
    if (item.disabled) {
      return;
    }

    if (!item.selectable) {
      if (item.isDirectory) {
        this.directory = item;
        this.directoryLevels.push(item);
      }
      return;
    }
    if (item.isDirectory && !item.selectable) {
    }

    this.$emit("change", {
      ...item,
      checked: !item.checked,
    });
    let array = [...this.value];
    if (!item.checked) {
      array.push(item.id);
    } else {
      let i = array.indexOf(item.id);
      if (i >= 0) {
        array.splice(i, 1);
      }
    }
    this.$emit("input", array);
  }

  onClickNavItem(item) {
    this.directory = item.id == null ? null : item;
    let index = this.directoryLevels.indexOf(item);
    this.directoryLevels.splice(index + 1);
  }

  getContext() {
    return {
      records: [...this.records],
    };
  }
}
</script>
<style scoped lang="less">
.tree-view {
  display: flex;
  flex-direction: column;

  .el-icon-arrow-down{
    margin-right: 4px;
  }

  /deep/.el-input {
    .el-input__inner {
      background: #f7f8fa;
      border: 0;
      height: 32px;
      line-height: 32px;
      font-family: PingFangSC-Regular;
    }
    .el-icon-search {
      color: #888888;
      font-weight: bold;
      position: relative;
      top: -2px;
    }
  }

  .directory-buttons {
    position: absolute;
    right: 0;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: var(--o-primary-color);
    top: 0;
    padding: 0 16px;
    padding-left: 30px;
    height: 100%;
    line-height: 72px;
    cursor: pointer;
    .el-icon-scissors {
      position: absolute;
      left: 12px;
      font-size: 14px;
      top: 50%;
      transform: translateY(-50%);
    }
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 12px;
      bottom: 12px;
      width: 0.5px;
      background: #eaeaea;
    }
  }

  .search-input {
    padding: 12px 16px 0;
  }

  .tree-view-nav-wrap {
    overflow-y: hidden;
    overflow-x: auto;
    overflow-x: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tree-view-nav-wrap::-webkit-scrollbar {
    display: none;
  }

  .tree-view-nav {
    height: 54px;
    line-height: 54px;
    display: flex;
    white-space: nowrap;
    border-bottom: 0.5px solid #eaeaea;

    color: #4f71ff;
    text-align: left;
    padding: 0 16px;

    .tree-view-nav-item {
      cursor: pointer;
    }

    & > span:last-child .tree-view-nav-item {
      color: #888;
      margin-right: 30px;
    }

    .el-icon-arrow-right {
      color: #ccc;
      font-size: 14px;
      font-weight: bold;
      position: relative;
      top: 1px;
      padding: 0 2px;
    }
  }

  .tree-view-body {
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .tree-view-record {
    display: flex;
    border-bottom: 0.5px solid #eaeaea;
    align-items: center;
    height: 72px;
    padding: 0 16px;
    position: relative;
  }

  .tree-view-content {
    flex: 1;
    flex-grow: 1;
  }

  .tree-view-checkbox {
    // padding: 20px 20px 20px 20px;
    margin-right: 15px;
  }
}
</style>