import { <PERSON><PERSON>, <PERSON>a, Story, Source, ArgsTable } from '@storybook/addon-docs';

import SelectDeptField from './SelectDeptField.vue';

<Meta title="业务组件/o-select-dept-field" component={SelectDeptField} argTypes={{
  value: {
    type: 'object',
    description: '部门的ID,例如"133"; 如果maxSelectNum > 1,则是数组例如: ["133", "577"]'
  },
  maxSelectNum: {
    description: '最多可以选择的部门数量'
  },
  readonly: {
    type: 'boolean',
    description: '是否只读不可修改'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SelectDeptField },
  data: () => ({ dept: "" }),
  template: '<o-select-dept-field v-bind="$props" v-model="dept"></o-select-dept-field>',
});


# 选择部门

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

<ArgsTable story="基本" />
