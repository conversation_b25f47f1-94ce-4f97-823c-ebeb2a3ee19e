<template>
  <div>
    <placeholder
      :border="!mobile"
      style="min-height: 36px"
      @click="onClick"
      :placeholder="placeholder"
    >
      <legal v-if="value" :value="value" @update:change="onChange" />
    </placeholder>
    <popup v-model="show" title="选择法人实体" mobileHeight="80%">
      <select-legal
        style="height: 100%"
        v-if="show"
        @input="onInput"
        :value="value"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue } from "vue-property-decorator";

import SelectLegal from "./SelectLegal.vue";
import Legal from "./Legal.vue";
import Placeholder from "../Placeholder.vue";
import Popup from "../Popup.vue";
import { FieldContext, registerField } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";

@Component({
  components: {
    SelectLegal,
    Popup,
    Legal,
    Placeholder
  },
})
export default class  SelectLegalField extends Vue {
  @Prop() value!: string;
  @Prop() readonly!: any;
  @Prop() placeholder!: string;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get mobile() {
    return this.context.isMobile;
  }

  show = false;

  field!: FieldContext;
  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && !this.value) {
      return rule.message ?? "请选择法人";
    }
    return null;
  }

  onInput(v) {
    this.show = false;
    this.$emit("input", v[0]);
  }

  onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }

  onChange(v) {
    this.$emit("update:change", v);
  }
}
</script>