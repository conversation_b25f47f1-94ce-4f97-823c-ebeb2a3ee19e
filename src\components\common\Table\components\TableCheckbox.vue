<template>
    <div>
      <o-checkbox :value="value" @input="onInput" shape="square"></o-checkbox>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, } from "vue-property-decorator";
import _ from "lodash"

@Component
export default class TableCheckBox extends Vue {
  @Prop() readonly value!: any;
  @Prop({ default: ()=>{} }) readonly scope!: any;

  onInput (check){
    if(_.isFunction(this.$attrs.onInput)) this.$attrs.onInput(this.scope.row,check)
  }
}
</script>