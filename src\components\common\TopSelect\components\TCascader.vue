<template>
  <el-cascader
    :value="value"
    :appendToBody="false"
    class="custom-el-popper-wrap"
    filterable
    @input="onInput"
    v-bind="$attrs"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { Cascader as ElCascader } from "element-ui";
import { FormItem } from "../type";

@Component({
  inheritAttrs: false,
  components: {
    ElCascader,
  },
})
export default class TCascader extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset(){
    if(Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue)
    }
  }
}
</script>