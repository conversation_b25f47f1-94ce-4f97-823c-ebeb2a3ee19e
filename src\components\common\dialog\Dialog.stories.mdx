import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Source } from '@storybook/addon-docs';

import Dialog from './Dialog.vue'

<Meta title="基础组件/o-dialog" component={Dialog} argTypes={{
  title: {
    type: 'string'
  },
  confirmButton: {
    type: 'string'
  },
  canceButton: {
    type: 'string'
  }
}} />

export const template = `
<div>
  <o-button @click="show = true">打开</o-button>
  <o-dialog v-model="show" v-bind="$props" @confirm="show = false">对话框内容</o-dialog>
</div>
`
export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Dialog },
  data: () => ({ show: false }),
  template: template,
});


# 对话框

基本对话框

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

标题

<Canvas>
  <Story 
    name="标题"
    args={{
      title: '这是标题',
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

基本按钮

<Canvas>
  <Story 
    name="基本按钮"
    args={{
      title: '这是标题',
      confirmButton: '确定',
      canceButton: '取消'
    }}>
    {Template.bind({})}
  </Story>
</Canvas>