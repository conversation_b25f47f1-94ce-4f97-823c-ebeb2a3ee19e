<template>
  <div class="comp-select-user">
    <div>
      <template>
        <PersonCard
          v-for="(item, i) in value"
          :key="item.type + item.id"
          :name="item.name"
          :size="30"
          :fontSize="12"
          :closable="true"
          @close="onCloseTag(i)"
        />
      </template>
      <button class="comp-select-user-addbtn" @click="show = true">+</button>
    </div>
    <van-popup
      v-model="show"
      round
      position="bottom"
      :style="{ height: '70%' }"
    >
      <select-user
        style="height: 100%"
        v-if="show"
        :selectTarget="selectTarget"
        :maxSelectNum="multiselect ? 10000 : 1"
        @input="onInput"
        :value="value"
      />
    </van-popup>
  </div>
</template>
<script lang="ts">
import { listDept, listMerchantMember } from "../../util";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import PersonCard from "./PersonCard.vue";
import SelectUser from "../common/SelectUser.vue";

interface Record {
  type: "dept" | "user";
  id: string;
  name: string;
  data: any;
}

async function loadDeptAndMember(records: Record[]) {
  let promise = [] as Promise<Record[]>[];

  let deptId = records.filter((o) => o.type == "dept").map((o) => o.id);
  if (deptId.length > 0) {
    let p = listDept({
      filters: {
        id: deptId,
      },
    }).then((r) =>
      r.list.map((z) => {
        return {
          type: "dept",
          id: z.id,
          name: z.name,
          data: z,
        } as Record;
      })
    );
    promise.push(p);
  }

  let userId = records.filter((o) => o.type == "user").map((o) => o.id);
  if (userId.length > 0) {
    let p = listMerchantMember({
      filters: {
        userId,
      },
    }).then((r) =>
      r.list.map((z) => {
        return {
          type: "user",
          id: z.userId,
          name: z.name,
          data: z,
        } as Record;
      })
    );
    promise.push(p);
  }

  let a = await Promise.all(promise);
  return a.flatMap((o) => o);
}

@Component({
  components: {
    SelectUser,
    PersonCard,
  },
})
export default class MobileSelectUserField extends Vue {
  @Prop() value!: Record[];
  @Prop() getContainer!: "";
  @Prop({
    default: false,
  })
  readonly!: boolean;
  @Prop({
    default: true,
  })
  multiselect!: boolean;
  @Prop({
    default: "user",
  })
  selectTarget!: string;

  show = false;

  async setValue(userId: Record[]) {
    // 从接口拉取缺失的数据
    let missed = userId.filter((o) => o.data == null);
    if (missed.length > 0) {
      let records = await loadDeptAndMember(missed);
      userId = userId.map((o) => {
        let find = records.find((o2) => o.type == o2.type && o.id == o2.id);
        return find ? find : o;
      });
    }

    if (missed.length > 0) {
      this.$emit("input", [...userId]);
    }
  }
  @Watch("value")
  onValueChange() {
    this.setValue(this.value);
  }

  onInput(v) {
    this.show = false;
    this.$emit("input", v);
  }

  onCloseTag(index) {
    let array = [...this.value];
    array.splice(index, 1);
    this.$emit("input", array);
  }
}
</script>

<style lang="less" scoped>
.comp-select-user {
  &-addbtn {
    height: 25px;
    width: 50px;
    border: 1px solid #4f75ff;
    background-color: #fff;
    color: #4f75ff;
    border-color: #4f75ff;
    border-radius: 20px;
  }
}
</style>