<template>
  <div class="select-user">
    <tree-view
      class="select-user-tree"
      :loader="loader"
      :value="selected"
      :enableSearch="enableSearch"
      :searchInputProps="searchInputProps"
      ref="tree-view"
      :limit="10000"
      @input="onInput"
    >
      <template v-slot:default="{ item, index }">
        <div class="select-user-record">
          <avatar
            v-if="!isDeptType && !item.isDirectory"
            :name="shortUserName(item)"
            :size="39"
            :bgColor="avatarColor[index % 3]"
            :fontSize="12"
          />
          <p class="user-name">{{ item.name || "-" }}</p>
          <span v-if="!isDeptType && item.data.descendantMemberNum != null"
            >（{{ item.data.descendantMemberNum }}）</span
          >
        </div>
      </template>
      <!-- <template v-slot:directory>
        下级按钮插槽
      </template> -->
    </tree-view>
    <div class="select-user-bottom">
      <div class="select-count">
        <div v-if="maxSelectNum !== 1">
          <span> 已选：{{ selected.length }} {{ selectCountUnit }} </span>
          <i class="el-icon-arrow-down" v-if="false"></i>
        </div>
      </div>
      <OButton type="primary" class="select-user-button" @click="onOk">确定</OButton>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch, Ref } from "vue-property-decorator";
import {
  Dept,
  DeptMember,
  listDept,
  listMerchantMember,
  MerchantMember,
  showError,
} from "../../util";
import OButton from "./button";

import TreeView from "./TreeView.vue";
import Avatar from "./Avatar.vue";

// 搜索输入框属性
const searchInputProps = {
  placeholder: "搜索名称",
};

// 名称卡片颜色
const avatarColor = ["#5B84F6", "#6FD6B4", "#F4AE40"];

const SELECT_TARGET_TYPE = {
  USER: "user",
  DEPT: "dept",
};

@Component({
  components: {
    TreeView,
    OButton,
    Avatar,
  },
})
export default class UserListView extends Vue {
  @Prop() value!: any[];
  // 选择目标
  @Prop({
    default: "user",
  })
  selectTarget!: string;
  @Prop({
    default: 1,
  })
  maxSelectNum!: number;

  @Prop() readonly enableSearch!: boolean;

  @Ref("tree-view") readonly treeView!: TreeView;

  selected: string[] = [];

  userCache: any = {};
  deptCache: any = {};

  loader: any = null;

  searchInputProps = searchInputProps;
  avatarColor = avatarColor;

  mounted() {
    this.refresh();
    this.measure();
  }

  @Watch("value")
  watchValue() {
    this.measure();
  }

  shortUserName({ name }) {
    if (!name || typeof name !== "string") return "-";
    return name.substr(0, 2);
  }

  handleCloseBtnClick() {
    this.$emit("close");
  }

  showNextLevelButton({ isDirectory }) {
    return isDirectory && this.isDeptType;
  }

  handleNextLevelClick(item) {
    this.treeView.onClickDown(item);
  }

  get isDeptType() {
    return this.selectTarget === SELECT_TARGET_TYPE.DEPT;
  }

  get selectCountUnit() {
    return !this.isDeptType ? "人" : "项";
  }

  measure() {
    this.selected.splice(0, this.selected.length);
    if (this.value) {
      this.selected = this.value.map((o) => {
        if (this.selectTarget == "user") {
          return "user:" + o.id;
        } else if (o.type == "dept") {
          return "dept:" + o.id;
        } else {
          throw new Error();
        }
      });
    }
  }

  async refresh() {
    this.loader = async ({ directory, keywords, start, limit }) => {
      if (keywords) {
        if (this.selectTarget == "user") {
          // 搜索用户
          let members = await listMerchantMember({
            start,
            limit,
            filters: {
              keywords,
            },
          });
          return this.memberToItem(members.list);
        } else if (this.selectTarget == "dept") {
          let depts = await listDept({
            start,
            limit,
            filters: {
              keywords: keywords,
              withMember: false,
              withChildren: false,
            },
          });
          return this.deptToItem(depts.list).map((o) => {
            o.isDirectory = false;
            return o;
          });
        }
      }

      let data = await listDept({
        filters: {
          parentId: directory ? undefined : [null],
          id: directory ? [directory.data.id] : undefined,
          withMember: this.selectTarget == "user",
          withChildren: true,
        },
      });

      if (data.list.length > 0) {
        let dept = data.list[0];
        let a = this.deptToItem(dept.children);
        let b = this.memberToItem(dept.members);

        let result = [...a, ...b];
        return result;
      }
      return [];
    };
  }

  memberToItem(members?: DeptMember[]) {
    return (
      members?.map((m) => {
        this.userCache[m.userId] = m;
        return {
          id: `user:${m.userId}`,
          name: m.name,
          selectable: this.selectTarget == "user",
          data: m,
        };
      }) ?? []
    );
  }

  deptToItem(depts?: Dept[]) {
    return (
      depts?.map((d) => {
        this.deptCache[d.id] = d;

        // 如果是选择部门模式，没有下级部门的话就不显示下级按钮
        let isDirectory = true;
        if (this.selectTarget == "dept" && d.childrenNum == 0) {
          isDirectory = false;
        }

        return {
          id: `dept:${d.id}`,
          name: d.name,
          isDirectory,
          selectable: this.selectTarget == "dept",
          data: d,
        };
      }) ?? []
    );
  }

  onInput(v) {
    if (this.maxSelectNum >= 1) {
      if (this.maxSelectNum == 1) {
        // 如果只能选一个，自动删除之前选择的项
        if (v.length > 1) {
          v = v.splice(1);
        }
        // 如果不加这个 "v.length>= this.maxSelectNum" , 用户选到最大限制的人员数以后 无法取消选中状态
      } else if (
        this.selected.length >= this.maxSelectNum &&
        v.length >= this.maxSelectNum
      ) {
        showError(`最多支持选择${this.maxSelectNum}人`);
        return;
      }
    }
    this.selected = v;
  }

  async onOk() {
    let value = this.value ?? [];
    value = [...value];
    value = this.selected.map((o) => {
      let type = o.startsWith("user:") ? "user" : "dept";
      let id =
        type == "user"
          ? o.substring("user:".length)
          : o.substring("dept:".length);
      return {
        id,
        type,
      };
    });

    value = value.map((o) => {
      let data;
      if (o.type == "user") {
        data = this.userCache[o.id];
      } else if (o.type == "dept") {
        data = this.deptCache[o.id];
      }
      return {
        ...o,
        name: data?.name,
        data,
      };
    });

    this.$emit("input", value);
  }
}
</script>

<style scoped lang="less">
.select-user {
  display: flex;
  flex-direction: column;

  .select-user-tree {
    flex: 1;
    min-height: 0;
  }

  .select-user-record {
    display: flex;
    align-items: center;
    .user-name {
      margin: 0;
      margin-left: 12px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
    }
  }

  .select-dept-record {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    letter-spacing: 0;
    line-height: 16px;
    position: relative;
    padding-right: 80px;

    .el-icon-arrow-right {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      color: #d2d4db;
      font-size: 16px;
      font-weight: bold;
    }
  }

  .select-user-bottom {
    display: flex;
    height: 74px;
    display: flex;
    background: #ffffff;
    box-shadow: 0 -2px 8px 0 rgba(224, 224, 224, 0.6);
    align-items: center;

    .select-count {
      flex: 1;
      color: var(--o-primary-color);
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #4f71ff;
      padding-left: 16px;
      text-align: left;
      i {
        font-weight: bold;
        margin-left: 4px;
      }
    }

    .select-user-button {
      width: 165px;
      height: 46px;
      background: var(--o-primary-color);
      border: 0;
      // color: #ffffff;
      border-radius: 4px;
      margin-right: 16px;
    }
  }
}
</style>