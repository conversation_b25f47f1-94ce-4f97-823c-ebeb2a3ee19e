
export const isRegExp = (v) => {
  return Object.prototype.toString.call(v) === '[object RegExp]';
}

export function formatNumber(
  value: string,
  scale = 0,
  min = -Infinity,
  max = Infinity
) {
  let allowDot = !scale ? false : true
  let allowMinus = min < 0
  console.log('允许小数点:' + allowDot)
  console.log('允许负数:' + allowMinus)
  value = number(value, allowDot, allowMinus)
  value = numberAttr(value,scale,allowDot,allowMinus,min,max)
  return value
}

export 

//纯数字格式 - 该校验允许 数字 负数 小数点
function number(
  value: string,
  allowDot: boolean,
  allowMinus: boolean
) {
  if (allowDot) {
    value = trimExtraChar(value, '.', /\./g);
  } else {
    value = value.split('.')[0];
  }

  if (allowMinus) {
    value = trimExtraChar(value, '-', /-/g);
  } else {
    value = value.replace(/-/, '');
  }

  const regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;

  return value.replace(regExp, '');
}


// 校验 小数点 正负数 最值
function numberAttr(
  value: string,
  scale:number|string,
  allowDot:boolean = true,
  allowMinus:boolean = true,
  min:number,
  max:number
) {
  if(!allowMinus){
    value = value.replace(/[^\d\.]/g, '');
  }

  if(allowDot){
    let str = ``
    for(let i = 0;i<scale;i++){
      str+='\\d'
    }
    //只能输入特定小数
    let reg = new RegExp(`^(\\-)*(\\d+)\\.(${str}).*$`)
    value = value.replace(reg,'$1$2.$3');
  }

  let v = parseFloat(value);
  if(v > max){
    value = max.toString()
  }
  if(v < min){
    value = min.toString()
  }

  return value
}


function trimExtraChar(value: string, char: string, regExp: RegExp) {
  const index = value.indexOf(char);

  if (index === -1) {
    return value;
  }

  if (char === '-' && index !== 0) {
    return value.slice(0, index);
  }

  return value.slice(0, index + 1) + value.slice(index).replace(regExp, '');
}

