<template>
  <el-select 
    @input="onInput"
    :value="value"
    v-bind="formItem" 
    :disabled="isDisabled"
    @change="onChange"
    :clearable="clearable"
    :filterable="filterable"
    @remove-tag="removeTag"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :disabled="item.disabled"
      :label="item.label"
      :value="item.value">
      <el-tooltip class="item" effect="dark" :disabled="getTipDisabled(item.label)" :content="item.label" placement="top">
        <span>{{getLabel(item.label)}}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules, ValidateTrigger } from "../../form";
import formFieldMixins from "../../formFieldMixins";
import { FormItem } from "../type";
import _ from "lodash"
import { field2Options } from "../../TopSelect/utils";

@Component({
  inheritAttrs: false,
  mixins:[formFieldMixins],
})
export default class JCascader extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop({ default:true }) clearable!: String;
  @Prop({ default:true }) filterable!: String;
  @Prop({ default:99999 }) readonly labelMaxLength!: number;
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;

  field!: FieldContext;
  error = false;
  options:any[] = [];

  onChange() {
    if (this.validateTrigger == "realtime" || this.validateTrigger == "blur") {
      setTimeout(() => {
        this.validate();
      }, 100);
    }
  }

  async setOptions (){
    if(!this.formItem.options) return this.options = [];

    if(_.isFunction(this.formItem.options)) {
      this.options = await this.formItem.options()
      if(this.formItem.field) {
        field2Options(this.options, this.formItem.field);
      }
    }else{
      this.options = this.formItem.options as any[]
    }
  }

  @Watch("formItem.options")
  onOptions (){
    this.setOptions();
  }

  created(){
    this.setOptions()
  }


  getLabel(label){
    return label.length<this.labelMaxLength?label:(label.substring(0, this.labelMaxLength) + "...")
  }

  getTipDisabled(label){
    const showLabel = this.getLabel(label)
    return !showLabel.endsWith("...") 
  }


  removeTag(){
    if (this.validateTrigger == "blur" || this.validateTrigger == "realtime") {
      setTimeout(() => {
        this.validate();
      }, 100);
    }
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset(){
   
  }

  async validate() {
    return this.validateCore(this.value as any);
  }

  async validateCore(v: string) {
    v = Array.isArray(v) && v.length === 0 ? "" : v
    
    let r = await validateRules(v, this.actualRules);

    if (!r.ok ) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }

    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>