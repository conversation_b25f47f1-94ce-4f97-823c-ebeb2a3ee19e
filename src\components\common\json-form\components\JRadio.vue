<template>
  <div>
    <div v-if="textModel">
      {{valueText}}
    </div>
    <el-radio-group
      v-else
      @input="onInput"
      :value="value"
      radio
      horizontal
      :disabled="isDisabled"
    >
      <el-radio
        v-for="item in formItem.options"
        :key="item.value"
        :label="item.value"
      >{{item.label}}</el-radio>
    </el-radio-group>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules } from "../../form";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
})
export default class JRadio extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() rules!: any[];

  field!: FieldContext;
  error = false;

  beforeMount() {
    this.field = registerField(this);
  }

  get valueText (){
    try {
      return (this.formItem.options as any[]).find(item=>item.value === this.value).label
    }catch{
      return ""
    }
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset() {
    this.onInput([this.formItem.defaultValue]);
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }


  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>