import { <PERSON><PERSON>, <PERSON>a, Story, Source, ArgsTable } from '@storybook/addon-docs';

import UserUpload from './UserUpload.vue';

<Meta title="业务组件/o-user-upload" component={UserUpload} argTypes={{
  value: {
    type: 'array',
    description: '文件的ID数组, 例如: ["133", "577"]'
  },
  readonly: {
    type: 'boolean'
  },
  maxFileSize: {
    description: '最大文件大小，单位字节'
  },
  allowSuffix: {
    description: '允许上传的文件后缀，例如["jpg", "pdf"]'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { UserUpload },
  data: () => ({ file:"" }),
  template: '<o-user-upload v-bind="$props" v-model="file"></o-user-upload>',
});


# 文件上传

<Canvas>
  <Story
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>

<ArgsTable story={"基本"} />
