<!-- 切换企业提示 -->
<template>
  <div>
    <el-dialog
      title="提示"
      :visible.sync="visible"
      width="420px"
      top="37vh"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :custom-class="customClass"
      :show-close="false"
      :before-close="confirm"
    >
      <div class="el-dialog__body_content">
        <span class="change-enterprse_title" v-if="type == 0"
          >当前企业发生了变化，点击确定进入工作台</span
        >
        <span class="change-enterprse_title" v-if="type == 1"
          >当前登录用户发生了变化，点击确定进入工作台</span
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";

@Component({
  name: "o-change-enterprise-dialog",
})
export default class ChangeEnterpriseDialog extends Vue {
  /**
   * 是否显示
   */
  @Prop({
    default: false,
  })
  private visible!: boolean;

  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  @Prop()
  private type?: number;

  /**
   * @description 获取客户定义class
   */
  private get customClass() {
    const { theme } = this;
    return `o-nav-logout-dialog o-nav-logout-dialog-${theme}`;
  }

  private confirm() {
    this.$emit("on-confirm");
  }
}
</script>

<style scoped>
.change-enterprse_title {
  color: #46485a !important;
}
</style>