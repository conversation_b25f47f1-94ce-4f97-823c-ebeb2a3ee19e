<script lang="ts">
import TableHeaderButton from './TableHeaderButton.vue'
import TableHeaderDropdown from './TableHeaderDropdown.vue'

import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
  components:{
    TableHeaderButton,
    TableHeaderDropdown
  },
  inheritAttrs:false
})
export default class TableHeaderButtonRender extends Vue {
  @Prop({ default: "button" }) readonly type!: string
  @Prop() readonly component!: string

  render(h){
    const customRender = this.$attrs.render as any
    const map = {
      button:TableHeaderButton,
      dropdown:TableHeaderDropdown,
      custom:this.component,
      render:customRender && customRender(h,{
        props:this.$attrs
      })
    }
    const component = map[this.type]

    if(this.type === "render") return map["render"]

    return h(component,{
      props:this.$attrs
    })
  }
}

</script>