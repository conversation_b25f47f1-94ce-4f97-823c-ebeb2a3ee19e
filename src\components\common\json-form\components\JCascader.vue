<template>
  <div>
    <div v-if="textModel">
      {{valueText}}
    </div>
    <el-cascader
      v-show="!textModel"
      :value="value"
      :appendToBody="false"
      ref="cascader"
      class="custom-el-popper-wrap"
      :disabled="isDisabled"
      filterable
      @input="onInput"
      clearable
      v-bind="$attrs"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { Cascader as ElCascader } from "element-ui";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";

@Component({
  inheritAttrs: false,
  mixins:[formFieldMixins],
  components: {
    ElCascader,
  },
})
export default class JCascader extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;

  @Ref("cascader") 
  cascader:any

  field!: FieldContext;

  valueText = ""

  @Watch("value",{
    immediate:true
  })
  onWatchValue(){
    this.setValueText()
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  async setValueText (){
    await this.$nextTick()
    if(this.cascader.presentText) {
      this.valueText = this.cascader.presentText
    }else if(this.cascader.presentTags && this.cascader.presentTags.length) {
      this.valueText = this.cascader.presentTags.join(",")
    }else{
      this.valueText = ""
    }
  }

  onFieldReset(){
    if(Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue)
    }
  }
}
</script>