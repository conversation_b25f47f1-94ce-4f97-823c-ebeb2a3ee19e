<template>
  <span @click="$emit('click', file)">{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../util";

@Component
export default class  File extends Vue {
  @Prop() value!: string;

  text = "";
  file: any = null;

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let file = await store.loadFile(this.value);
      if (file) {
        this.text = file.name;
        this.file = file;
        this.$emit("update:change", file);
      }
    }
  }

  onClick() {
    if (this.file) {
      this.$emit("click", this.file);
    }
  }
}
</script>