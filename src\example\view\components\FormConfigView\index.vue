<template>
  <main class="form-config">
    <index-left class="form-config_left" />
    <index-center class="form-config_center" />
    <index-right class="form-config_right" />
  </main>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import indexLeft from "./components/index-left.vue"
import indexRight from "./components/index-right.vue"
import indexCenter from "./components/index-center.vue"

@Component({
  components:{
    indexLeft,
    indexRight,
    indexCenter
  }
})
export default class  Index extends Vue {

}

</script>
 
  
<style lang="less" >
@import './index.less';
</style>