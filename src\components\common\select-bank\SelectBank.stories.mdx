import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import Comp from './SelectBankField.vue';

<Meta title="业务组件/o-select-bank-field" component={Comp} argTypes={{
  readonly: {
    type: 'boolean'
  },
  placeholder: {
    type: 'string'
  },
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Comp },
  data: () => ({ m: "" }),
  template: '<o-select-bank-field v-bind="$props" v-model="m"></o-select-bank-field>',
});


# 选择银行组件

<Canvas>
  <Story 
    name="基本"
    args={{
    }}>
    {Template.bind({})}
  </Story>
</Canvas>
