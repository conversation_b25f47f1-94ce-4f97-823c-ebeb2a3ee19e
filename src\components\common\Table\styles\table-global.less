:root {
  .o-table {
    ::-webkit-scrollbar {
      opacity: 1;
    }

    .table-header-left{
      display: flex;
      align-items: center;
    }


    .table-loading{
      position: fixed;
      left: 0;
      top: 0;
      right: 20px;
      bottom: 0;
      z-index: 2001;
    }

    &.pb60{
      padding-bottom: 60px;
    }

    .el-table__fixed{
      &::before{
        display: none;
      }
    }

    .action-button-empty{
      padding-left: 6px;
    }
    
    .o-success-primary {
      color: var(--o-primary-color);
    }
    .o-danger-color {
      color: var(--o-danger-color);
    }
    .o-success-color {
      color: var(--o-success-color);
    }
    .table-column-title{
      font-size: 12px;
    }
    .el-table--scrollable-x .el-table__body-wrapper{
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
      &::-webkit-scrollbar-thumb {
        display: none;
      }
      &::-webkit-scrollbar-track-piece {
        display: none;
      }
    }
    &-action-dropdown-menu {
      padding: 12px 0;
      &.el-popper {
        margin-top: 4px;
        min-width: 84px;
        background: #ffffff;
        box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.2);
        border: 0;
        border-radius: 8px;
      }
      .popper__arrow {
        display: none;
      }
      .el-dropdown-menu__item {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #46485a;
        letter-spacing: 0;
        text-align: center;
        line-height: 40px;
        word-break: keep-all;
        &.is-disabled {
          color: #bbb;
        }
        &:hover {
          background: none;
          color: var(--o-primary-color);
        }
      }
    }
    &-pagination-select-popup {
      border: 0;
      transform: translateY(8px);
      margin-left: -20px;
      .el-select-dropdown__list {
        font-family: PingFangSC-Regular;
        padding: 12px;
        letter-spacing: 0;
        width: 120px;
        background: #ffffff;
        box-shadow: 0 10px 30px 4px rgba(182, 185, 196, 0.2);
        border-radius: 8px;
        .el-select-dropdown__item {
          font-weight: 400;
          text-align: center;
          overflow: inherit;
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          color: #46485a;
          margin-bottom: 4px;
          &.hover {
            background: #f7fafd;
            border-radius: 4px;
          }
        }
      }
      .popper__arrow {
        display: none;
      }
    }
    .el-loading-mask{
      background: #fff !important;
      // min-height:100vh;
    }
    .el-table.empty{
      &::before{
        content: none;
      }
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active{
      background: #fff !important;
      color: var(--o-primary-color) !important;
    }
    .el-pagination {
      padding: 0;
      .is-focus{
        .el-icon-arrow-up{
          transform: rotate(180deg) scale(0.5) !important;
        }
      }
      .el-icon-arrow-up{
        font-family: "olading-iconfont" !important;
        transform: rotate(0) scale(0.5) !important;
          &::before{
          color: #777C94;
          content: "\e650";
          font-size: 16px;
        }
      }

      .el-select .el-input {
        width: 88px;
      }

      .el-pagination__jump {
        margin-left: 16px;
      }

      .el-input.is-active .el-input__inner,
      .el-input__inner:focus {
        border-color: var(--o-primary-color);
      }

      .el-select-dropdown__list {
        width: 120px;
      }

      .btn-prev,
      .el-pager li,
      .btn-next {
        margin: 0;
        margin-left: 4px;
        border-radius: 4px;
        border: 1px solid #cbced8;
        box-sizing: border-box;
        background: #fff !important;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #24262a !important;
        letter-spacing: 0;
        text-align: center;

        &[disabled="disabled"] {
          color: #cbced8 !important;
          border-color:#EEF0F4 !important;
        }

        &.active {
          color: var(--o-primary-color);
          border-color: var(--o-primary-color);
        }
      }
    }
    .el-loading-spinner{
      .circular{
        margin:0 auto;
      }
    }
    .table-pagination-scroll-wrap {
      width:100%;
      overflow-x: scroll;
      margin: 0;
      margin-bottom: 12px;
      height: 6px;
      &::-webkit-scrollbar {
        width: 12px;
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(36,38,42,0.16) !important;
      }
      &::-webkit-scrollbar-track-piece {
        background-color: none;
        border-radius: 10px;
      }
      .scroll-x {
        height: 1px;
        display: block;
      }
    }
  }
  .o-table-pagination-select-popup{
    &::-webkit-scrollbar{
      height: inherit;
      width: inherit;
    }
  }
}