<template>
  <div class="comp-select-user">
    <div>
      <template>
        <OPersonCard
          v-for="(item, i) in value"
          :key="item.type + item.id"
          :name="item.name"
          :size="30"
          :closable="!readonly && readonly !== ''"
          @close="onCloseTag(i)"
        />
      </template>
      <button
        v-if="showAddBtn"
        type="button"
        class="comp-select-user-addbtn"
        @click="onClick()"
      >
        <span>+</span>
      </button>
    </div>
    <popup
      v-model="show"
      :title="selectTarget == 'dept' ? '选择部门' : '选择员工'"
      mobileHeight="80%"
    >
      <select-user
        style="height: 100%"
        v-if="show"
        @close="closePopup"
        :enableSearch="enableSearch"
        :selectTarget="selectTarget"
        :maxSelectNum="multiselect ? maxSelectNum : 1"
        @input="onInput"
        :value="value"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { listDept, listMerchantMember } from "../../util";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import OButton from "./button";
import OPersonCard from "./PersonCard.vue";
import Popup from "./Popup.vue";
import SelectUser from "./SelectUser.vue";
import Tag from "./Tag.vue";
import { Member } from "./member-chip";
import { FieldContext, registerField } from "./form";

interface Record {
  type: "dept" | "user";
  id: string;
  name: string;
  data: any;
}

async function loadDeptAndMember(records: Record[]) {
  let promise = [] as Promise<Record[]>[];

  let deptId = records.filter((o) => o.type == "dept").map((o) => o.id);
  if (deptId.length > 0) {
    let p = listDept({
      filters: {
        id: deptId,
      },
    }).then((r) =>
      r.list.map((z) => {
        return {
          type: "dept",
          id: z.id,
          name: z.name,
          data: z,
        } as Record;
      })
    );
    promise.push(p);
  }

  let userId = records.filter((o) => o.type == "user").map((o) => o.id);
  if (userId.length > 0) {
    let p = listMerchantMember({
      filters: {
        userId,
      },
    }).then((r) =>
      r.list.map((z) => {
        return {
          type: "user",
          id: z.userId,
          name: z.name,
          data: z,
        } as Record;
      })
    );
    promise.push(p);
  }

  let a = await Promise.all(promise);
  return a.flatMap((o) => o);
}

@Component({
  components: {
    Tag,
    SelectUser,
    OPersonCard,
    OButton,
    Popup,
    Member,
  },
})
export default class SelectUserField extends Vue {
  @Prop() value!: Record[];
  @Prop() readonly!: any;
  @Prop({ default: 10000 }) maxSelectNum!: number;

  @Prop({
    default: true,
  })
  multiselect!: boolean;
  @Prop({
    default: "user",
  })
  selectTarget!: string;

  @Prop({
    default: true,
  })
  readonly enableSearch!: boolean;

  show = false;

  field!: FieldContext;
  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  mounted() {
    this.setValue(this.value);
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && rule && (this.value == null || this.value.length == 0)) {
      return rule.message ?? "请选择";
    }
    return null;
  }

  async setValue(userId: Record[]) {
    // 从接口拉取缺失的数据
    let missed = userId.filter((o) => o.data == null);
    if (missed.length > 0) {
      let records = await loadDeptAndMember(missed);
      userId = userId.map((o) => {
        if (o.data) {
          return o;
        }
        let find = records.find((o2) => o.type == o2.type && o.id == o2.id);
        const defaultValue = {
          ...o,
          name: "[已删除]" + o.id,
          data: {},
        };
        return find ? find : defaultValue;
      });

      this.$emit("input", [...userId]);
    }
  }

  @Watch("value")
  onValueChange() {
    this.setValue(this.value);
  }

  get showAddBtn() {
    //可编辑 && 单选 && 已选择 情况下隐藏加号
    let readonly = this.readonly || this.readonly === "";
    let multiselect = this.multiselect;
    let valueLength = this.value.length;
    if (multiselect) {
      return !readonly;
    } else {
      return !readonly && valueLength == 0;
    }
  }

  onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }

  closePopup() {
    this.show = false;
  }

  onInput(v) {
    this.closePopup();
    this.$emit("input", v);
  }

  onCloseTag(index) {
    if (this.readonly || this.readonly === "") {
      return;
    }
    let array = [...this.value];
    array.splice(index, 1);
    this.$emit("input", array);
  }
}
</script>

<style lang="less" scoped>
.comp-select-user {
  &-addbtn {
    height: 25px;
    width: 50px;
    border: 1px solid var(--o-primary-color);
    background-color: #fff;
    color: var(--o-primary-color);
    border-color: var(--o-primary-color);
    border-radius: 20px;
    cursor: pointer;
  }
}
.comp-select-user-addbtn{
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>