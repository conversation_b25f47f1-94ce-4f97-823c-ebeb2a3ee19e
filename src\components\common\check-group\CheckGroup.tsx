
import { Component, Vue, Prop } from "vue-property-decorator";
import { FieldContext, registerField } from "../form";

@Component
export class CheckItem extends Vue {
  @Prop() disabled!: any;
  @Prop() value!: any;

  render() {
    return this.$slots.default
  }
}

@Component
export class CheckGroup extends Vue {
  @Prop() value!: string[];
  @Prop() horizontal: any;
  // 是否是多选
  @Prop() multiple!: any;
  // 是否必须至少选中一个
  @Prop() mandatory!: any;
  @Prop() disabled!: boolean;

  get objectClass() {
    const horizontal = this.horizontal === "" || this.horizontal == true
    return {
      "o-check-group": true,
      "horizontal": horizontal,
      "vertical": !horizontal
    };
  }

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  render() {
    let children = this.$slots.default ?? []
    children = children.filter(o => o.componentOptions)
    return <o-toggle-item-group multiple={this.multiple} mandatory={this.mandatory} class={this.objectClass} value={this.value} onInput={(v) => this.$emit("input", v)}>
      {children.map(o => {
        const props = o.componentOptions?.propsData as any
        return <o-toggle-item value={props.value}>{({ active, toggle }) => {
          return <van-checkbox class="o-check" disabled={this.disabled}  onInput={toggle} value={active}>{o}</van-checkbox>
        }}</o-toggle-item>
      })}
    </o-toggle-item-group>
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && (this.value ?? []).length == 0) {
      return rule.message ?? "请勾选一个项目";
    }
    return null;
  }
}
