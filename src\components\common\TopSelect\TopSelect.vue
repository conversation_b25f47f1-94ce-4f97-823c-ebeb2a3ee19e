<template>
  <div
    class="o-top-select"
    :class="actionButtonFloatClass"
  >
    <!-- 表单渲染组件 -->
    <form-render
      :formJson="cFormJson"
      class="form-render"
      :class="{open:isOpen}"
      ref="form-render"
      :labelWidth="labelWidth"
      :outSelectNumber="outSelectNumber"
      :is-open="isOpen"
      @change="formData=>$emit('change',formData)"
      @open="onOpen"
    />

    <!-- 操作按钮 -->
    <div
      class="operating-button"
      :style="operatingButtonStyle"
      :class="{ open:isOpen }"
    >
      <el-button
        type="primary"
        class="search"
        @click="handleSearchClick"
      ><span>查询</span></el-button>
      <el-button
        class="reset"
        @click="handleResetBtnClick"
      ><span>重置</span></el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Provide } from "vue-property-decorator";
import formRender from "./TopSelectFormRender.vue";
import { TFormJson } from "./type";
import _ from "lodash";
import { parse2px } from "../util";

@Component({
  inheritAttrs: false,
  components: {
    formRender,
  },
})
export default class TopSelect extends Vue {
  // 筛选配置JSON
  @Prop({ default: () => {} }) readonly formJson!: TFormJson;
  // 是否自动触发搜索
  @Prop({ default: false }) readonly immediate!: boolean;
  // 设置表单全局label宽度
  @Prop({ default: "58px" }) readonly labelWidth!: string | number;
  @Prop({ default: 2 }) readonly outSelectNumber!: Number;
  

  @Ref("form-render") formRender!: formRender;

  searchLoading = false;
  resetLoading = false;

  cFormJson: TFormJson = [];

  @Provide() oTopSelect = this;

  isOpen: boolean = false;

  get actionButtonFloatClass() {
    return !this.isOpen ? "action-float" : "";
  }

  protected async handleSearchClick() {
    this.searchLoading = true;
    const formData = await this.getFormData();
    try {
      await (this.$listeners?.search as Function)(formData)
    }finally{
      this.searchLoading = false
    }
  }

  protected async handleResetBtnClick() {
    this.formRender.resetForm();
    this.resetLoading = true;
    const formData = await this.getFormData();
    this.$emit("reset")
    try {
      await (this.$listeners?.search as Function)(formData)
    }finally{
      this.resetLoading = false
    }
  }

  protected onOpen(show) {
    this.isOpen = !show;
  }

  // 动态设置下拉选项 数据源
  setOptions(data) {
    this.cFormJson.forEach((item) => {
      const options = data[item.formItem.prop];
      if (Array.isArray(options)){
        this.$set(item.formItem, "options", options);
      }
    });
    return this
  }

  setFormValue(formData) {
    this.formRender.setFormValue(formData);
  }

  async getFormData() {
    return await this.formRender.getFormData();
  }

  // 设置表单数据项
  setFormJson(formJson) {
    this.formRender?.resetForm();
    this.cFormJson = _.cloneDeep(formJson).map(item=>{
      // 适配 配置项传item 和 jsonForm 保持一直
      if(!item.formItem && item.item) {
        item.formItem = item.item
      }
      return item
    });
  }

  // 获取表单数据源
  getOptions(prop) {
    for (let item of this.cFormJson) {
      if (item.formItem.prop === prop) return item.formItem.options;
    }
    return [];
  }

  get operatingButtonStyle(){
    return {
      paddingLeft:`calc(${parse2px(this.labelWidth)} + 32px)`
    }
  }

  created() {
    this.setFormJson(this.formJson);
  }

  mounted() {
    // 自动触发搜索
    if (this.immediate) this.$nextTick(() => this.handleSearchClick());
  }
}
</script>

<style lang="less">
@import "./styles/topSelect-global.less";
</style>