<template>
  <div class="o-select-dept-field">
    <div v-if="textModel">
      <span v-if="!innerValue.length">-</span>

      <template v-for="item in innerValue">
        <o-dept :key="item.id" :value="item.id" :showDeleteText="true"  />
        <span :key="item.id" v-if="innerValue.length>1 && index!==(innerValue.length-1)">,</span>
      </template>
    </div>
    <placeholder  @click="onClick" :placeholder="placeholder" :border="!mobile" class="o-select-dept-field-body" hoverable v-else >
      <o-dept-chip
        v-for="(item, i) in innerValue"
        :key="item.id"
        :value="item.id"
        :closable="isCloseable"
        @close="onCloseTag(i)"
        @change="onDeptChange"
        style="max-width: 140px; margin: 2px 0"
      />
      <!-- 新增判断：只有移动端才展示此按钮 -->
      <o-chip
        v-if="showAddBtn && mobile"
        type="primary"
        outlined
        class="o-select-dept-field-addbtn"
        @click="onClick()"
      >
        +
      </o-chip>
    </placeholder>
    <popup v-model="show" title="选择部门" mobileHeight="80%">
      <select-user
        style="height: 100%"
        v-if="show"
        @close="closePopup"
        :enableSearch="true"
        selectTarget="dept"
        :maxSelectNum="maxSelectNum"
        @input="onInput"
        :value="innerValue"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import Popup from "../Popup.vue";
import SelectUser from "../SelectUser.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, registerField } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";
import { asArray } from "../util";
import formFieldMixins from "../formFieldMixins";

@Component({
  mixins:[formFieldMixins],
  components: {
    SelectUser,
    Popup,
    Placeholder,
  },
})
export default class  SelectDeptField extends formFieldMixins {
  @Prop() value!: string[] | string;
  @Prop({ default: 10000 }) maxSelectNum!: number;
  @Prop() placeholder!: string;

  get innerValue() {
    let v = asArray(this.value);
    return v.map((o) => ({
      type: "dept",
      id: o,
    }));
  }

  show = false;
  valueTextMap:any= {}

  field!: FieldContext;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get mobile() {
    return this.context.isMobile;
  }

  get valueText(){
    return Object.keys(this.valueTextMap).reduce((cur,id)=>{
      if(this.valueTextMap[id]) cur.push(this.valueTextMap[id])
      return cur
    },[] as any).join(",")
  }

  @Watch("innerValue")
  onWatchInnerValue(newValue){
    const valueTextMap = {}
    newValue.forEach(item=>{
      valueTextMap[item.id] = this.valueTextMap[item.id] || ""
    })
    this.valueTextMap = { ...valueTextMap }
  }

  onDeptChange(newValue){
    this.valueTextMap[newValue.id] = newValue.name
  }

  get isCloseable (){
    if(this.isDisabled) return false
    return !this.isReadonly
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && rule && (this.value == null || this.value.length == 0)) {
      return rule.message ?? "请选择";
    }
    return null;
  }

  get showAddBtn() {
    // 禁用状态
    if(this.isDisabled) return false

    //可编辑 && 单选 && 已选择 情况下隐藏加号
    let readonly = this.readonly || this.readonly === "";
    if (readonly) {
      return false;
    }

    let maxSelectNum = this.maxSelectNum ?? 1;
    let valueLength = this.value?.length ?? 0;
    return valueLength < maxSelectNum;
  }

  onClick() {
    // 复用原来显示添加按钮逻辑
    // if(!this.showAddBtn) return
    this.show = true;
  }

  closePopup() {
    this.show = false;
  }

  onInput(v) {
    this.closePopup();
    if(Array.isArray(v)) {
      this.emitInput(v.map((o) => o.id));
    }else{
      this.emitInput([]);
    }
  }

  onCloseTag(index) {
    if (this.readonly || this.readonly === "") {
      return;
    }
    let array = [...this.innerValue];
    array.splice(index, 1);
    this.emitInput(array.map((o) => o.id));
  }

  private emitInput(v: string[]) {
    if (this.maxSelectNum == 1) {
      this.$emit("input", v.length == 0 ? null : v[0]);
    } else {
      this.$emit("input", v);
    }
  }
}
</script>

<style lang="less" scoped>
.o-select-dept-field {
  &-addbtn {
    height: 25px;
    width: 50px;
    cursor: pointer;
  }

  &-body {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>