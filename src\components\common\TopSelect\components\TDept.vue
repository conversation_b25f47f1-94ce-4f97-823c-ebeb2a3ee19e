<template>
  <div class="dept">
    <o-select-dept-field v-bind="$attrs" :value="value" @input="onInput" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { FormItem } from "../type";


@Component({
  inheritAttrs: false,
})
export default class TMember extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset(){
    this.onInput(this.formItem.defaultValue || "")
  }
}
</script>
<style scoped>
</style>