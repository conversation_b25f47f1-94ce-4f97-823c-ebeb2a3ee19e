export default [
  {
    id: 14616,
    title: "组织管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "dept",
    notOpenRule: false,
    routePath: "",
    appCode: "HR",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 14679,
        title: "组织架构",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "dept",
        notOpenRule: true,
        routePath: "/address-book",
        appCode: "HR",
        belongProject: "sso",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14616,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14680,
        title: "岗位管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "post",
        notOpenRule: true,
        routePath: "/post-manage",
        appCode: "HR",
        belongProject: "sso",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14616,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 14617,
    title: "员工管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "hrEmployee",
    notOpenRule: true,
    routePath: "",
    appCode: "HR",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 14673,
        title: "花名册",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.roster",
        notOpenRule: true,
        routePath: "/staff-manage/roster",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14674,
        title: "入职管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.entry",
        notOpenRule: true,
        routePath: "/staff-manage/entry-manage",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14675,
        title: "转正管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.turnRegular",
        notOpenRule: true,
        routePath: "/staff-manage/regular-manage",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14676,
        title: "离职管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.leave",
        notOpenRule: true,
        routePath: "/staff-manage/leave-manage",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14677,
        title: "异动管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.changeOpt",
        notOpenRule: true,
        routePath: "/staff-manage/operate-records",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14678,
        title: "员工关怀",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.empcare",
        notOpenRule: true,
        routePath: "/employeeCare",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14617,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 14618,
    title: "人事报表",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "",
    notOpenRule: true,
    routePath: "",
    appCode: "HR",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 14672,
        title: "常用报表",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.report",
        notOpenRule: true,
        routePath: "/staff-manage/common-reports",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14618,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 14619,
    title: "合同管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "hrContract",
    notOpenRule: true,
    routePath: "",
    appCode: "HR",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 14669,
        title: "劳动合同管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrContract.conManage.laborContract",
        notOpenRule: true,
        routePath: "/contract-manage/labor-contract",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: "CONTRACT_MANAGEMENT",
        incrementLevel: "BASIC",
        children: [],
        pid: 14619,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14670,
        title: "电子合同",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrContract.conManage.eContract",
        notOpenRule: true,
        routePath: "/contract-manage/electronic-contract",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: "CONTRACT_MANAGEMENT",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 14619,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14671,
        title: "合同模版",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrContract.conManage.conTemplate",
        notOpenRule: true,
        routePath: "/contract-manage/contract-template",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: "CONTRACT_MANAGEMENT",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 14619,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 14620,
    title: "员工管理设置",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "",
    notOpenRule: true,
    routePath: "",
    appCode: "HR",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 14681,
        title: "员工信息设置",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.template",
        notOpenRule: true,
        routePath: "/staff-manage/template-set",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14620,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 14682,
        title: "常用字段设置",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.employee.option",
        notOpenRule: true,
        routePath: "/staff-manage/option-dictionary",
        appCode: "HR",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 14620,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9667,
    title: "考勤管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "hrAttend.attendManage",
    notOpenRule: true,
    routePath: "",
    appCode: "HRATTEND",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9734,
        title: "考勤组管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.group",
        notOpenRule: true,
        routePath: "/attendance/workAttendance",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9667,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9735,
        title: "班次管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.work",
        notOpenRule: true,
        routePath: "/attendance/shift",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9667,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9736,
        title: "补卡规则",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.supplement",
        notOpenRule: true,
        routePath: "/attendance/cardRule",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9667,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9737,
        title: "加班规则",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.overTime",
        notOpenRule: true,
        routePath: "/attendance/workOvertime",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9667,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9738,
        title: "人脸管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.faceManager",
        notOpenRule: true,
        routePath: "/attendance/face",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9667,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9668,
    title: "假期管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "hrAttend.attendManage",
    notOpenRule: true,
    routePath: "",
    appCode: "HRATTEND",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9739,
        title: "假期类型",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.leave",
        notOpenRule: true,
        routePath: "/attendance/holidayType",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9668,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9740,
        title: "假期余额",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.leave",
        notOpenRule: true,
        routePath: "/attendance/holidayBalance",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9668,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9669,
    title: "考勤报表",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "hrAttend.attendManage",
    notOpenRule: true,
    routePath: "",
    appCode: "HRATTEND",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9741,
        title: "每日统计",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.dailyCount",
        notOpenRule: true,
        routePath: "/attendance/dayStatistical",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9669,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9742,
        title: "月度统计",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.monthlyCount",
        notOpenRule: true,
        routePath: "/attendance/monthStatistical",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9669,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9743,
        title: "打卡时间",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.signTime",
        notOpenRule: true,
        routePath: "/attendance/clockTime",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9669,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9744,
        title: "原始记录",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.count",
        notOpenRule: true,
        routePath: "/attendance/originalRecord",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9669,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9745,
        title: "加班明细",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrAttend.attendManage.overTimeDetail",
        notOpenRule: true,
        routePath: "/attendance/overtimeDetail",
        appCode: "HRATTEND",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9669,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9672,
    title: "社保公积金",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "salary.social",
    notOpenRule: true,
    routePath: "",
    appCode: "SALARY_MANAGER",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9754,
        title: "增减员",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.social.floatEmployee",
        notOpenRule: true,
        routePath: "/attrition",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9672,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9755,
        title: "参保月度台账",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.social.ledger",
        notOpenRule: true,
        routePath: "/insuredAccount",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9672,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9756,
        title: "参保方案",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.social.insuredProject",
        notOpenRule: true,
        routePath: "/insuredPlan",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9672,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9670,
    title: "薪资核算",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "salary.compute",
    notOpenRule: true,
    routePath: "",
    appCode: "SALARY_MANAGER",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9746,
        title: "薪资核算",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.compute.salaryCheck",
        notOpenRule: true,
        routePath: "/salary-cal",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9670,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9747,
        title: "薪资档案",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.compute.salaryArchive",
        notOpenRule: true,
        routePath: "/adjust-salary",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9670,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9748,
        title: "计薪规则",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.compute.attendance",
        notOpenRule: true,
        routePath: "/salary-rule",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9670,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9671,
    title: "个税申报",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "salary.report",
    notOpenRule: true,
    routePath: "",
    appCode: "SALARY_MANAGER",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9749,
        title: "人员信息采集",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.report.personReport",
        notOpenRule: true,
        routePath: "/tax/collect",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 9671,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9750,
        title: "专项附加扣除",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.report.additionl",
        notOpenRule: true,
        routePath: "/tax/attach",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 9671,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9751,
        title: "综合所得申报",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.report.taxReport",
        notOpenRule: true,
        routePath: "/tax/report",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 9671,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9752,
        title: "分类所得申报",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.report.classify",
        notOpenRule: true,
        routePath: "/tax/class-report",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 9671,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9753,
        title: "三方协议缴税",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.taxpay.paytax",
        notOpenRule: true,
        routePath: "/taxPaid/paid",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "PREMIUM",
        children: [],
        pid: 9671,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9678,
    title: "薪酬设置",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "salary.auth",
    notOpenRule: true,
    routePath: "",
    appCode: "SALARY_MANAGER",
    belongProject: "salary",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9771,
        title: "数据权限管理",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "salary.auth.dataAuth",
        notOpenRule: true,
        routePath: "/power-set",
        appCode: "SALARY_MANAGER",
        belongProject: "salary",
        incrementBusinessCode: "SALARY_MANAGER",
        incrementLevel: "BASIC",
        children: [],
        pid: 9678,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 96781,
    title: "审批管理",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "",
    notOpenRule: true,
    routePath: "",
    appCode: "AUTOAPPROVAL",
    belongProject: "approval",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9699,
        title: "单据管理",
        level: 1,
        routeCode: null,
        icon: null,
        privilegeCode: "autoApproval.formManagement",
        notOpenRule: false,
        routePath: "/receipt",
        appCode: "AUTOAPPROVAL",
        belongProject: "approval",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: null,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9700,
        title: "审批数据",
        level: 1,
        routeCode: null,
        icon: null,
        privilegeCode: "autoApproval.approvalData",
        notOpenRule: false,
        routePath: "/approvalDataList",
        appCode: "AUTOAPPROVAL",
        belongProject: "approval",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: null,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9701,
        title: "自定义档案",
        level: 1,
        routeCode: null,
        icon: null,
        privilegeCode: "autoApproval.customArchives",
        notOpenRule: false,
        routePath: "/archives",
        appCode: "AUTOAPPROVAL",
        belongProject: "approval",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: null,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
  {
    id: 9689,
    title: "基础设置",
    level: 1,
    routeCode: null,
    icon: null,
    privilegeCode: "merchant",
    notOpenRule: false,
    routePath: "",
    appCode: "BASIC_FUNCTION",
    belongProject: "sso",
    incrementBusinessCode: null,
    incrementLevel: null,
    children: [
      {
        id: 9784,
        title: "法人实体",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "hrEmployee.init.taxSubject",
        notOpenRule: false,
        routePath: "/initialize/paid",
        appCode: "BASIC_FUNCTION",
        belongProject: "salary",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9689,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9787,
        title: "数据接入",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "tripartitePlatForm",
        notOpenRule: false,
        routePath: "/open-platform",
        appCode: "BASIC_FUNCTION",
        belongProject: "sso",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9689,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9788,
        title: "显示设置",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "merchantConfig",
        notOpenRule: false,
        routePath: "/customize-set",
        appCode: "BASIC_FUNCTION",
        belongProject: "sso",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9689,
        isOpen: true,
        isAuth: true,
      },
      {
        id: 9789,
        title: "操作日志",
        level: 2,
        routeCode: null,
        icon: null,
        privilegeCode: "merchant.log",
        notOpenRule: false,
        routePath: "/operation-log",
        appCode: "BASIC_FUNCTION",
        belongProject: "sso",
        incrementBusinessCode: null,
        incrementLevel: null,
        children: [],
        pid: 9690,
        isOpen: true,
        isAuth: true,
      },
    ],
    pid: null,
    isOpen: true,
    isAuth: true,
  },
//   {
//     id: 14621,
//     title: "UI2导航",
//     level: 1,
//     routeCode: null,
//     icon: null,
//     privilegeCode: "",
//     notOpenRule: true,
//     routePath: "",
//     appCode: "HR",
//     belongProject: "salary",
//     incrementBusinessCode: null,
//     incrementLevel: null,
//     children: [
//       {
//         id: 14681,
//         title: "card",
//         level: 2,
//         routeCode: null,
//         icon: null,
//         privilegeCode: "hrEmployee.employee.template",
//         notOpenRule: true,
//         routePath: "/components/card",
//         appCode: "HR",
//         belongProject: "salary",
//         incrementBusinessCode: null,
//         incrementLevel: null,
//         children: [],
//         pid: 14620,
//         isOpen: true,
//         isAuth: true,
//       },
//       {
//         id: 14682,
//         title: "invoice组件",
//         level: 2,
//         routeCode: null,
//         icon: null,
//         privilegeCode: "hrEmployee.employee.option",
//         notOpenRule: true,
//         routePath: "/components/invoice",
//         appCode: "HR",
//         belongProject: "salary",
//         incrementBusinessCode: null,
//         incrementLevel: null,
//         children: [],
//         pid: 14620,
//         isOpen: true,
//         isAuth: true,
//       },
//       {
//         id: 14683,
//         title: "topSelect组件",
//         level: 2,
//         routeCode: null,
//         icon: null,
//         privilegeCode: "hrEmployee.employee.option",
//         notOpenRule: true,
//         routePath: "/components/topSelect",
//         appCode: "HR",
//         belongProject: "salary",
//         incrementBusinessCode: null,
//         incrementLevel: null,
//         children: [],
//         pid: 14683,
//         isOpen: true,
//         isAuth: true,
//       },
//     ],
//     pid: null,
//     isOpen: true,
//     isAuth: true,
//   },
];
