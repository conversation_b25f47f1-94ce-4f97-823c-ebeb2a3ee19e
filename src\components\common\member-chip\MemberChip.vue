<template>
  <o-chip
    :closable="closable"
    @close="$emit('close')"
    style="background-color: #f1f1f1"
  >
    <div style="display: flex; align-items: center">
      <o-avatar :size="25" :name="simpleName" style="margin: 0 3px 0 -7px" />
      <o-member class="member" :value="value" :change.sync="member" />
    </div>
  </o-chip>
</template>

<script lang='ts'>
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component
export default class  MemberChip extends Vue {
  @Prop() value!: string;
  @Prop() closable!: any;

  get simpleName() {
    let v = (this.member?.name ?? "").substring(0, 1);
    return v;
  }

  member: any = null;

  @Watch("member")
  onMemberWatch(value){
    this.$emit("change",value)
  }
}
</script>
<style lang='less' scoped>
.member {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  font-size: 14px;
}
</style>