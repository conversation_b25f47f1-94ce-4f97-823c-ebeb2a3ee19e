<template>
  <o-dialog :title="title" :value="show" @input="onClose" style="width: 700px">
    <o-form-scene
      ref="form"
      :save="save"
      hideButton
      :loading.sync="loading"
      @submit="$emit('submit', $event)"
    >
      <slot />
    </o-form-scene>
    <div slot="footer">
      <o-button :disabled="loading" @click="onClose" style="width: 100px"
        >取消</o-button
      >
      <o-button
        :loading="loading"
        @click="onSubmit"
        type="primary"
        style="width: 100px; margin: 0 0 0 10px"
      >
        确定
      </o-button>
    </div>
  </o-dialog>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue } from "vue-property-decorator";

@Component
export default class FormSceneDialog extends Vue {
  @Prop() show!: string;
  @Prop() title!: string;
  @Prop() save!: string;

  @Ref("form")
  form: any;

  loading = false;

  async getValues() {
    return this.form.getValues();
  }

  async submit() {
    return await this.form.submit();
  }

  protected onClose() {
    if (this.loading) {
      return;
    }-+
    this.$emit("update:show", false);
  }

  protected async onSubmit() {
    await this.submit();
  }
}
</script>
<style lang='less' scoped>
</style>