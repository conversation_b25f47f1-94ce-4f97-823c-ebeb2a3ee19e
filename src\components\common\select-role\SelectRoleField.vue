<template>
  <div class="comp-select-post">
    <div>
      <template>
        <OPersonCard
          v-for="(item, i) in value"
          :key="item.id"
          :name="item.name"
          :size="30"
          :closable="!readonly && readonly !== ''"
          @close="onCloseTag(i)"
        />
      </template>
      <button
        v-if="showAddBtn"
        type="button"
        class="comp-select-post-addbtn"
        @click="onClick()"
      >
        <span>+</span>
      </button>
    </div>
    <popup
      v-model="show"
      title="选择角色"
      mobileHeight="80%"
    >
      <select-role
        style="height: 100%"
        v-if="show"
        @close="closePopup"
        :enableSearch="enableSearch"
        :maxSelectNum="multiselect ? maxSelectNum : 1"
        @input="onInput"
        :value="value"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { listRole } from "../../../util";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import OButton from "../button";
import OPersonCard from "../PersonCard.vue";
import Popup from "../Popup.vue";
import SelectRole from "../SelectRole.vue";
import Tag from "../Tag.vue";
import { Member } from "../member-chip";
import { FieldContext, registerField } from "../form";

interface Record {
  id: string;
  name: string;
  data: any;
}




async function loadRole(records: Record[]) {
    let promise = [] as Promise<Record[]>[];
    let postId = records.map((o) => o.id);
    if (postId.length > 0) {
      let p = listRole({
        filters: {
          id: postId,
        },
      }).then((r) =>
        r.list.map((z) => {
          return {
            id: z.id,
            name: z.name,
            data: z,
          } as Record;
        })
      );
      promise.push(p);
      let a = await Promise.all(promise);
      return a.flatMap((o) => o);
    }
}

@Component({
  components: {
    Tag,
    SelectRole,
    OPersonCard,
    OButton,
    Popup,
    Member,
  },
})
export default class  SelectRoleField extends Vue {
  @Prop() value!: Record[];
  @Prop() readonly!: any;
  @Prop({ default: 10000 }) maxSelectNum!: number;
  @Prop({ default: true }) multiselect!: boolean;

  @Prop({
    default: true,
  })
  readonly enableSearch!: boolean;

  show = false;

  field!: FieldContext;
  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  mounted() {
    this.setValue(this.value);
  }

  @Watch("value")
  onValueChange() {
    this.setValue(this.value);
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && rule && (this.value == null || this.value.length == 0)) {
      return rule.message ?? "请选择";
    }
    return null;
  }


  get showAddBtn() {
       //可编辑 && 单选 && 已选择 情况下隐藏加号
      let readonly = this.readonly || this.readonly === "";
      if (readonly) {
      return false;
      }
      let multiselect = this.multiselect;
      let valueLength = this.value.length;
      if (multiselect) {
      return !readonly;
      } else {
      return !readonly && valueLength == 0;
      }
  }


  async setValue(postId: Record[]) {
    // 从接口拉取缺失的数据
    let missed = postId.filter((o) => o.name == null);
    if (missed.length > 0) {

      let records = await loadRole(missed);
      postId = postId.map((o) => {
        if (o.data) {
          return o;
        }
        let find = records!.find((o2) => o.id == o2.id);
        const defaultValue = {
          ...o,
          name: "[已删除]" + o.id,
          data: {},
        };
        return find ? find : defaultValue;
      });
      this.$emit("input", [...postId]);
    }
  }

  onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }

  closePopup() {
    this.show = false;
  }

  onInput(v) {
    this.closePopup();
    this.$emit("input", v);
  }

  onCloseTag(index) {
    if (this.readonly || this.readonly === "") {
      return;
    }
    let array = [...this.value];
    array.splice(index, 1);
    this.$emit("input", array);
  }
}
</script>

<style lang="less" scoped>
.comp-select-post {
  &-addbtn {
    height: 25px;
    width: 50px;
    border: 1px solid var(--o-primary-color);
    background-color: #fff;
    color: var(--o-primary-color);
    border-color: var(--o-primary-color);
    border-radius: 20px;
    cursor: pointer;
  }
}
.comp-select-post-addbtn{
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>