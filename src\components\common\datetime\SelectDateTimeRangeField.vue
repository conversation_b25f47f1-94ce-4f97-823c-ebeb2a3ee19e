<template>
  <div style="line-height: 36px">
    <div v-if="textModel">
      {{valueText}}
    </div>
    <div v-else>
      <el-date-picker
        v-if="!mobile"
        :value="value"
        :type="elType"
        :disabled="isDisabled"
        placeholder="选择日期时间"
        @input="onInput"
        :start-placeholder="startPlaceholder"
        :end-placeholder="endPlaceholder"
        :range-separator="rangeSeparator"
        :value-format="valueFormat"
        style="width: 100%"
      >
      </el-date-picker>
    </div>
   
    <placeholder
      v-if="mobile"
      @click="onClick(0)"
      :value="text(0)"
      :placeholder="startPlaceholder"
    />
    <placeholder
      v-if="mobile"
      @click="onClick(1)"
      :value="text(1)"
      :placeholder="endPlaceholder"
    />

    <popup v-model="show" mobileHeight="50%">
      <van-datetime-picker
        v-if="show"
        :type="type"
        :min-date="popupMinDate"
        :max-date="popupMaxDate"
        title="选择日期"
        :value="popupDate"
        :formatter="formatter"
        @confirm="onFinish"
        @cancel="show = false"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue, Watch } from "vue-property-decorator";
import dayjs, { Dayjs } from "dayjs";
import Popup from "../Popup.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, registerField } from "../form";
import { dateTimePickerFormatter } from "../../../util";
import { CONTEXT_PROVIDER, defaultContext } from "../context";
import formFieldMixins from "../formFieldMixins";
import _ from "lodash"

function min(...days: (Dayjs | null)[]): Dayjs {
  let l = days.filter((o) => dayjs.isDayjs(o)) as Dayjs[];
  if (l.length > 0) {
    return l.reduce((a, b) => (a!.isBefore(b) ? a : b));
  } else {
    throw new Error("");
  }
}

function max(...days: (Dayjs | null)[]): Dayjs {
  let l = days.filter((o) => dayjs.isDayjs(o)) as Dayjs[];
  if (l.length > 0) {
    return l.reduce((a, b) => (a!.isAfter(b) ? a : b));
  } else {
    throw new Error("");
  }
}

@Component({
  mixins:[formFieldMixins],
  components: {
    Popup,
    Placeholder,
  },
})
export default class SelectDateTimeRangeField extends Vue {
  @Prop() value!: [Date | null, Date | null];
  @Prop() readonly!: any;
  @Prop({ default:"-" }) rangeSeparator!: string;
  @Prop() placeholder!: string[] | string;
  @Prop() valueFormat!: string;
  @Prop({
    default: "date",
  })
  type!: "year-month" | "date" | "datetime";
  @Prop() minDate!: Date;
  @Prop() maxDate!: Date;
  // 是否允许选择同一天,默认为true
  @Prop({ default: true }) allowSameDay!: boolean;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  text(i: number) {
    let format = "YYYY-MM-DD";
    if (this.type == "year-month") {
      format = "YYYY-MM";
    } else if (this.type == "date") {
      format = "YYYY-MM-DD";
    } else if (this.type == "datetime") {
      format = "YYYY-MM-DD HH:mm";
    }

    let value = this.value ?? [];
    let v = value[i];
    return v == null ? "" : dayjs(v).format(format);
  }

  get valueText (){
    if(!_.isArray(this.value)) return ""
    return this.value.join(" 至 ")
  }

  formatter: Function = dateTimePickerFormatter;

  show = false;

  popupIndex: number = 0;
  popupDate: Date | null = null;
  popupMinDate: Date | null = null;
  popupMaxDate: Date | null = null;

  field!: FieldContext;

  get elType() {
    let map = {
      "year-month": "daterange",
      date: "daterange",
      datetime: "datetimerange",
    };
    return map[this.type] ?? "date";
  }

  get mobile() {
    return this.context.isMobile;
  }

  get startPlaceholder() {
    if (typeof this.placeholder == "string") {
      return this.placeholder;
    } else if (this.placeholder) {
      return this.placeholder[0] ?? "";
    }
  }

  get endPlaceholder() {
    if (typeof this.placeholder == "string") {
      return this.placeholder;
    } else if (this.placeholder) {
      return this.placeholder[1] ?? "";
    }
  }

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (
      this.value &&
      this.value.length == 2 &&
      this.value.findIndex((o) => o == null) >= 0
    ) {
      return "请填写完整的日期范围";
    }
    if (rule) {
      if (
        this.value == null ||
        this.value.length != 2 ||
        this.value.findIndex((o) => o == null) >= 0
      ) {
        return rule.message ?? "请选择时间";
      }
    }
    return null;
  }

  mounted() {
    this.refresh();
  }

  refresh() {}

  @Watch("value")
  onModelChanged() {
    this.refresh();
  }

  onInput(v) {
    this.$emit("input", v);
  }

  onFinish(value) {
    this.show = false;

    let list = [null, null] as any[];
    if (this.value) {
      list[0] = this.value[0];
      list[1] = this.value[1];
    }
    list[this.popupIndex] = value;
    this.$emit("input", list);
  }

  onClick(i: number) {
    if (this.readonly || this.readonly === "") {
      return;
    }

    let value = this.value ?? [];
    let start = value[0] == null ? null : dayjs(value[0]);
    let end = value[1] == null ? null : dayjs(value[1]);
    let minDate = this.minDate == null ? null : dayjs(this.minDate);
    let maxDate = this.maxDate == null ? null : dayjs(this.maxDate);
    let year = dayjs().year();

    // 计算最小最大时间
    let popupMinDate = dayjs()
      .year(year - 20)
      .startOf("day");
    let popupMaxDate = dayjs()
      .year(year + 20)
      .endOf("day");

    // 不能和已选择的时间冲突
    if (i == 0 && end) {
      popupMaxDate = end;
      if (!this.allowSameDay) {
        popupMaxDate = popupMaxDate.subtract(1, "day").endOf("day");
      }
    } else if (i == 1 && start) {
      popupMinDate = start;
      if (!this.allowSameDay) {
        popupMinDate = popupMinDate.add(1, "day").startOf("day");
      }
    }

    // 无论如何，不能超过minDate和maxDate
    if (minDate) {
      popupMinDate = max(minDate, popupMinDate);
    }
    if (maxDate) {
      popupMaxDate = min(maxDate, popupMaxDate);
    }

    // 无论如何，popupMinDate也不能超过popupMaxDate
    popupMaxDate = max(popupMinDate, popupMaxDate);

    // 计算当前展示时间
    let popupDate = value[i] == null ? dayjs() : dayjs(value[i]);
    popupDate = max(popupDate, popupMinDate);
    popupDate = min(popupDate, popupMaxDate);

    this.popupMinDate = popupMinDate.toDate();
    this.popupMaxDate = popupMaxDate.toDate();
    this.popupDate = popupDate.toDate();
    this.popupIndex = i;
    this.show = true;
  }
}
</script>