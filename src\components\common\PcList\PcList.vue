<template>
   <o-container
    ref="o-container"
    :isBackLeaveScrollPosition="true"
    :title="$attrs.title"
    :back="$attrs.titleBack"
  >
    <!-- 筛选区域 -->
    <o-top-select
      ref="top-select"
      class="top-select"
      v-bind="$attrs"
      v-show="ifShowTopSelect"
      :immediate="immediate"
      @search="onSearch"
    />

    <!-- 表格区域 -->
    <o-table
      ref="o-table"
      v-bind="$attrs"
      :pagination="{ fixed:true }"
      :sticky="sticky"
      :showPagination="true"
      v-on="$listeners"
    >
      <!-- 插槽透传 -->
      <template  v-for="(fn,name) in $scopedSlots" :slot="name" slot-scope="scope" >
        <slot :name="name" v-bind="scope"  />
      </template>
    </o-table>
   </o-container> 
</template>

<script lang='ts'>
import { Component, Prop, Ref, Vue } from "vue-property-decorator";
import { Table } from "..";
import TopSelect from "../TopSelect/TopSelect.vue";

@Component({
  inheritAttrs:false
})
export default class PcList extends Vue {

  @Prop({ default:()=>{} }) beforeSearch!:Function
  @Prop({ default:true }) ifShowTopSelect!:Boolean
  @Prop({ default:()=>{
    return {
      top: 60, actionButton:true
    }
  } }) sticky!:any
  @Prop({ default:true }) immediate!:Boolean

  @Ref("o-table")
  oTable:Table|undefined

  @Ref("o-container")
  oContainer:any
  

  @Ref("top-select")
  oTopSelect:TopSelect|undefined
  
  setOptions (map){
    this.oTopSelect?.setOptions(map)
  }

  // 刷新
  reload(){
    this.oTable?.reload()
  }

  appendRequestParams(formData){
    return this.oTable?.appendRequestParams(formData);
  }  

  setRequestParams(formData){
    return this.oTable?.setRequestParams(formData,1);
  }  

  async onSearch(formData){
    if(this.beforeSearch) {
      formData = await this.beforeSearch(formData)
    }
    await this.oTable?.appendRequestParams(formData);
  }
}

</script>

<style scoped lang="less">
  .top-select{
    margin-top: 12px;
  }
</style>