<template>
  <div
    class="o-field"
    :class="fieldClass"
    ref="el"
  >
    <div :class="contentClass">
      <label
        class="label"
        :style="labelStyle"
      >
        <span>{{ label }}</span>
        <span
          v-if="actualAsterisk"
          class="required"
        > *</span>
        <slot name="hint">
          <el-popover
            v-if="hint"
            placement="bottom-start"
            width="400"
            :disabled="hintMessage == null"
            trigger="hover"
            :content="hintMessage"
          >
            <i
              slot="reference"
              class="hint"
              :class="hintClass"
              @click.stop="onHintClick"
            />
          </el-popover>
        </slot>
      </label>
      <div class="o-field-content-wrapper">
        <div class="o-field-content">
          <slot></slot>
        </div>
      </div>
    </div>
    <p
      v-if="actualErrorMessage"
      class="error-message"
      :style="errorStyle"
    >
      {{ actualErrorMessage }}
    </p>
  </div>
</template>

<script lang='ts'>
import {
  Component,
  Inject,
  Prop,
  Provide,
  Ref,
  Vue,
} from "vue-property-decorator";
import { CONTEXT_PROVIDER, defaultContext } from "./context";
import {
  FieldContext,
  FormContext,
  FORM_FIELD_PROVIDER,
  FORM_PROVIDER,
} from "./form";

@Component({
  name: "o-field",
})
export default class Field extends Vue {
  @Prop() name!: string;
  @Prop() colspan!: number;
  @Prop() label!: string;
  @Prop() rules: any;
  @Prop() asterisk!: any;
  @Prop() errorMessage!: string;
  @Prop() labelPosition!: string;
  @Prop() labelWidth!: string;
  @Prop() hint!: "warning" | "question" | "info";
  @Prop() hintColor!: "primary" | "danger" | "warning" | "success";
  @Prop() hintMessage!: string;

  @Ref("el")
  el!: HTMLElement;

  @Inject({
    from: FORM_PROVIDER,
    default: null,
  })
  context!: FormContext;

  @Provide(FORM_FIELD_PROVIDER)
  field: FieldContext = new FieldContext(this);

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context2: any;

  get mobile() {
    return this.context2.isMobile;
  }

  get labelStyle(): any {
    let pos = this.val("labelPosition", "left");
    return {
      width: pos == "left" ? this.val("labelWidth", "90px") : "",
    };
  }

  get errorStyle(): any {
    let pos = this.val("labelPosition", "left");
    return {
      marginLeft: pos == "left" ? this.val("labelWidth", "90px") : "",
    };
  }

  get fieldClass() {
    let layout = this.val("layout", "normal");
    let o = {} as any;
    o["field-" + layout] = true;
    o["is-error"] = this.actualErrorMessage ? true : false;
    o["o-mobile"] = this.mobile;
    return o;
  }

  get contentClass() {
    let pos = this.val("labelPosition", "left");
    let o = {
      "field-left": pos == "left",
      "field-top": pos == "top",
    } as any;
    return o;
  }

  get hintClass() {
    let hint = this.hint ?? "";
    let hintColor = this.hintColor ?? "primary";
    let o = {} as any;
    o["el-icon-" + hint] = true;
    o["hint-" + hintColor] = true;
    return o;
  }

  get actualErrorMessage() {
    return this.val("errorMessage", "");
  }

  get actualAsterisk(): boolean {
    if (this.asterisk || this.asterisk === "") {
      return true;
    }
    if (this.field.required) {
      return true;
    }
    return false;
  }

  beforeMount() {
    this.field.field = this;
    if (this.context) {
      this.context.addField(this.field);
    }

    for (let cmt of this.$children) {
      // eslint-disable-next-line no-prototype-builtins
      if (!this.field.component && cmt.hasOwnProperty("elForm")) {
        this.field.component = cmt;
      }
    }
  }

  beforeDestroy() {
    if (this.context && this.field) {
      this.context.removeField(this.field);
    }
  }

  val(key: string, defaultValue: any) {
    // let a = this.$props[key];
    // let b = this.field[key];
    // let c = this.context[key];
    if (this.$props[key] != null) {
      return this.$props[key];
    } else if (this.field[key] != null) {
      return this.field[key];
    } else if (this.context && this.context.props[key] != null) {
      return this.context.props[key];
    } else {
      return defaultValue;
    }
  }

  onHintClick() {
    if (this.hintMessage) {
      return;
    }
    this.$emit("hint");
  }
}
</script>
<style lang='less' scoped>
.o-field {
  min-height: 20px;
  background-color: white;
  position: relative;
  margin: 0 20px;
  margin-bottom: 20px;

  .required{
    color: #F63939;
    font-size: 14px;
  }

  .label {
    line-height: 24px;
    margin-bottom: 6px;
    color: #24262A;
  }

  .error-message {
    color: var(--o-danger-color);
    font-size: 12px;
    margin: 5px 0px 0px 0px;
    word-break: break-all;
  }

  .hint {
    padding: 0 8px;
    cursor: pointer;
    font-size: 16px;
  }

  .hint-danger {
    color: #d6342a;
  }

  .hint-warning {
    color: #ff9500;
  }

  .hint-primary {
    color: #4f71ff;
  }

  .hint-success {
    color: #36a64d;
  }
}


.field-left {
  display: flex;
  // align-items: center;

  .label {
    width: 90px;
    flex-shrink: 0;
  }

  .o-field-content {
    flex: 1;
    min-width: 0;
  }

  .o-field-content-wrapper {
    display: flex;
    flex-grow: 1;
    min-width: 0;
    min-height: 36px;
    align-items: center;
  }
}

.field-top {
  .label {
    display: block;
  }
}

.el-icon-question{
  font-family: "olading-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  &::before{
    content:"\e60e";
  }
}
</style>