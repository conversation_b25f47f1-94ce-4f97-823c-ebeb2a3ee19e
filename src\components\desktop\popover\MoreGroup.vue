<!-- 更多包含组 -->
<template>
  <div class="o-nav_popover_wrapper">
    <ul class="o-nav_popover_wrapper_ul" :class="['o-nav-popover-' + theme]">
      <template v-for="(item, index) in list">
        <el-popover
          :key="index"
          v-if="item.children.length > 0"
          placement="right-start"
          width="auto"
          trigger="hover"
          @show="show(index)"
          @hide="hide(index)"
          :tabindex="index"
          :popper-class="
            item.groupName === 'HAVE'
              ? 'o-nav_popover popover-scroll'
              : 'o-nav_popover o-nav_popover_more popover-scroll'
          "
        >
          <div slot="reference">
            <li class="o-nav_popover_wrapper_ul_item nav_item_hover">
              <span>{{ item.name }}</span>
              <span class="el-icon-arrow-down"></span>
            </li>
          </div>
          <!-- 存在组的情况 -->
          <div
            class="webkit-scrollbar o-nav_popover_wrapper_scrollbar"
            v-if="item.groupName === 'HAVE'"
          >
            <!-- 分组显示在最前面  -->
            <template v-for="(child, i) in getGroupData(item.children)">
              <!-- 分组start   判断是否有组，或者存在组的情况（可能是有分组未命名的数据） -->
              <div class="o-nav_popover_wrapper_group">
                <div class="o-nav_popover_wrapper_group_title">
                  {{ child.groupName }}
                </div>
                <!-- 组列表 分为一行两列 -->
                <div
                  class="o-nav_popover_wrapper_group_list"
                  :class="['o-nav-popover-' + theme]"
                >
                  <div
                    class="o-nav_popover_wrapper_group_list_column"
                    v-for="(seletor, i) in 2"
                    :key="i"
                  >
                    <div
                      class="
                        o-nav_popover_wrapper_group_list_item
                        nav_item_hover
                      "
                      @click="openAppEvent(group)"
                      v-for="(group, j) in child.children"
                      v-if="(j + 2 - i) % 2 === 0"
                      :key="j"
                    >
                      <div class="o-nav_popover_wrapper_group_list_item_icon">
                        <img :src="group.icon" />
                      </div>
                      <div class="o-nav_popover_wrapper_group_list_item_name">
                        {{ group.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <!-- 未分组显示在最后面 -->
            <template v-if="getNotGroupData(item.children).length > 0">
              <div class="o-nav_popover_wrapper_group">
                <div
                  class="o-nav_popover_wrapper_group_list"
                  :class="['o-nav-popover-' + theme]"
                >
                  <div
                    class="o-nav_popover_wrapper_group_list_column"
                    v-for="(seletor, i) in 2"
                    :key="i"
                  >
                    <div
                      class="
                        o-nav_popover_wrapper_group_list_item
                        nav_item_hover
                      "
                      @click="openAppEvent(group)"
                      v-for="(group, j) in getNotGroupData(item.children)"
                      v-if="(j + 2 - i) % 2 === 0"
                      :key="j"
                    >
                      <div class="o-nav_popover_wrapper_group_list_item_icon">
                        <img :src="group.icon" />
                      </div>
                      <div class="o-nav_popover_wrapper_group_list_item_name">
                        {{ group.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <!-- 不存在组的情况 -->
          <ul
            class="o-nav_popover_wrapper_leafnode webkit-scrollbar"
            :class="['o-nav-popover-' + theme]"
            v-else
          >
            <li
              class="o-nav_popover_wrapper_leafnode_item nav_item_hover"
              @click="openAppEvent(child)"
              v-for="(child, i) in item.children"
            >
              <span><img :src="child.icon" /></span>
              <span>{{ child.name }}</span>
            </li>
          </ul>
        </el-popover>
        <li
          v-else
          class="o-nav_popover_wrapper_ul_item nav_item_hover"
          @click="openAppEvent(item)"
        >
          <span>{{ item.name }}</span>
        </li>
      </template>
    </ul>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from "vue-property-decorator";
import { Navigation } from "../../../model/type";
@Component({
  name: "o-more-group",
})
export default class MoreGroup extends Vue {
  /**
   * 主题风格 默认default，可选值default、red
   */
  @Prop({
    default: "default",
  })
  private theme?: string;

  /**
   * 导航数据
   */
  @Prop()
  private list?: Navigation;

  @Prop()
  private refObj?: any;

  /**
   * @description 弹出层状态
   */
  private poopverStatus: boolean = false;

  /**
   * @description 存储状态
   */
  private popoverStatus = {};

  /**
   * @description 点击触发事件
   * @param item
   */
  private openAppEvent(item): void {
    this.$emit("on-select", item);
  }

  private show(index) {
    this.popoverStatus[index + ""] = true;
  }

  private hide(index) {
    this.popoverStatus[index + ""] = false;
  }

  /**
   * @description 获取有分组的数据
   * @param list
   */
  private getGroupData(list) {
    return list.filter((item) => item.groupName != null);
  }

  /**
   * @description 获取未分组的数据
   * @param list
   */
  private getNotGroupData(list) {
    return list.filter((item) => item.groupName == null);
  }
}
</script>

<style lang="less">
.o-nav_popover_wrapper_scrollbar {
  max-height: 400px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: 12px !important;
}
.popover-scroll {
  padding-right: 0px !important;
}
.o-nav_popover_wrapper_leafnode {
  max-height: 400px;
  overflow-x: hidden;
  padding-right: 10px;
  overflow-y: scroll;
}
</style>