<template>
  <div>
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :before-upload="onBeforeUpload"
      :on-success="onSuccess"
      :on-error="onError"
      :on-progress="onProgress"
      :show-file-list="false"
      :file-list="files"
      :headers="headers"
      style="display: none"
    >
      <button ref="trigger" slot="trigger" style="display: none"></button>
    </el-upload>
    <div v-for="(file, i) in value" :key="file" class="file-container">
      <i class="el-icon-document-copy file-icon"></i>
      <OFile class="file-name" :value="file" :change.sync="uploadedFiles[i]" />
      <i
        class="el-icon-download op-icon"
        @click="onClickFile(i)"
      ></i>
      <i
        v-if="!isReadonly"
        class="el-icon-delete op-icon"
        @click="onRemove(file)"
      ></i>
    </div>
    <OButton
      v-if="!isReadonly"
      size="small"
      style="width: 100%; margin: 10px 0px 0px 0px"
      :loading="uploading"
      @click="handleUpload"
      >上传</OButton
    >
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Ref, Vue } from "vue-property-decorator";
import OButton from "../button";
import OFile from "../File.vue";
import {
  downloadFile,
  prettyFileSize,
  uploadFileUrl,
  API_URL,
  showError,
  getToken,
} from "../../../util";
import { FieldContext, FORM_FIELD_PROVIDER } from "../form";

@Component({
  components: {
    OButton,
    OFile,
  },
})
export default class  UserUpload extends Vue {
  @Prop() value!: string[];
  @Prop() readonly!: any;
  @Prop() maxFileSize!: number;
  @Prop() allowSuffix!: string[];

  @Ref("upload")
  upload: any;

  @Ref("trigger")
  trigger: any;

  get isReadonly() {
    return this.readonly == true || this.readonly === "";
  }

  get headers() {
    const token = getToken();
    const header = {} as any;
    if (token) {
      header.Authorization = "Bearer " + token;
    }
    return header;
  }

  uploadUrl: string = "";
  files: any[] = [];
  uploading = false;
  uploadedFiles: any[] = []

  @Inject({
    from: FORM_FIELD_PROVIDER,
    default: null,
  })
  field!: FieldContext;

  beforeMount() {
    if (this.field && this.field.component == null) {
      this.field.component = this;
    }

    this.uploadUrl = uploadFileUrl;
  }

  beforeDestroy() {
    if (this.field && this.field.component === this) {
      this.field.component = null;
    }
  }

  async validate() {
    if (this.field) {
      let rule = this.field.requiredRule;
      if (rule && (this.value == null || this.value.length == 0)) {
        return rule.message ?? "请选择";
      }
      let max = this.field.rules.find((o) => o.max != null);
      if (max && this.value && this.value.length > max.max!) {
        return max?.message ?? "最多上传" + max.max! + "个文件";
      }
    }

    return null;
  }

  onSuccess(response, file: any, files) {
    if (file.response.success) {
      this.files.push(file);
    } else {
      showError("上传失败");
    }
    this.uploading = false;

    let list = (this.value ?? []) as string[];
    this.$emit("input", [...list, file.response.data.id]);
  }

  onError(err, file, files) {
    this.uploading = false;
    showError("上传失败");
  }

  onProgress(event, file, files) {
    this.uploading = true;
  }

  onRemove(file) {
    let list = (this.value ?? []) as string[];
    list = [...list];
    let i = list.indexOf(file);
    if (i >= 0) {
      list.splice(i, 1);
      this.$emit("input", list);
    }
  }

  onBeforeUpload(file) {
    console.log(file);
    if (this.maxFileSize != null && file.size > this.maxFileSize) {
      showError("文件大小不能超过" + prettyFileSize(this.maxFileSize));
      return false;
    }
    if (this.allowSuffix && this.allowSuffix.length > 0) {
      let i = file.name.lastIndexOf(".");
      if (i < 0) {
        // 没有后缀
        showError("不允许上传没有扩展名的文件");
        return false;
      }

      let allowSuffix = this.allowSuffix.map((o) => o.toLowerCase());
      let suffix = file.name.substring(i + 1);
      if (allowSuffix.indexOf(suffix.toLowerCase()) < 0) {
        showError("不允许上传" + suffix + "类型的文件");
        return false;
      }
    }

    this.uploading = true;
    return true;
  }

  onChange(file, fileList) {
    if (file.status == "ready") {
      if (this.maxFileSize != null && file.size > this.maxFileSize) {
        showError("文件大小不能超过" + this.maxFileSize);
        return;
      }
    }
  }

  onClickFile(index) {
    const file = this.uploadedFiles[index]
    console.log(file)
    if ((window as any).__wxjs_environment === "miniprogram") {
      let url = API_URL + "downloadFile/" + file.archiveId + "/" + file.name;
      console.log(url);
      (window as any).wx?.miniProgram?.navigateTo({
        url: `/pages/workFile/main?filePath=${url}&fileName=${file.name}`,
      });
    } else {
      downloadFile(file);
    }
  }

  handleUpload() {
    if (this.field) {
      let max = this.field.rules.find((o) => o.max != null);
      if (max && this.value && this.value.length >= max.max!) {
        showError(`最多上传${max.max}个文件`);
        return;
      }
    }

    this.trigger.click();
  }
}
</script>
<style scoped lang="less">
.file-container {
  display: flex;
  align-items: center;

  .file-icon {
    display: inline-block;
    font-size: 30px;
    margin: 15px 10px 15px 0px;
  }

  .file-name {
    flex-grow: 1;
    width: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .op-icon {
    padding: 10px;
    font-size: 20px;
    flex-shrink: 0;
    cursor: pointer;
  }
}
</style>