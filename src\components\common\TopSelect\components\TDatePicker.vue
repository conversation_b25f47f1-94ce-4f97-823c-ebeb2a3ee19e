<template>
  <div class="t-date-picker">
  <!-- 因为UI设计的 DatePick样式跟 ElDatePick组件样式不一致，所以日期组件有些样式需要进行单独处理 -->
    <el-date-picker
      :value="value"
      popperClass="t-date-picker-popper"
      :class="['t-date-pricker__'+$attrs.type,value?'exist-value':'']"
      @input="onInput"
      v-bind="formItem"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { DatePicker as DatePicker } from "element-ui";
import { FormItem } from "../type";

@Component({
  inheritAttrs:false,
  components: {
    DatePicker,
  },
})
export default class TDatePicker extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset(){
    this.onInput(this.formItem.defaultValue || "")
  }
}
</script>

<style lang="less" scoped>
.t-date-picker {
  /deep/.el-date-editor--daterange,/deep/.el-date-editor--datetimerange {
    padding-left: 16px;
    padding-right: 16px;
    &.exist-value {
      .el-range-separator {
        color: #606266;
        margin-right: 6px;
      }
      input {
        width: 72px;
        font-family: "Avenir", Helvetica, Arial, sans-serif;
      }
      &:hover{
        .el-icon-date{
          display: none;
        }
      }
    }
    input {
      text-align: left;
      width: 58px;
    }
    &.is-active {
      border-color: var(--o-primary-color);
    }
    .el-icon-date {
      right: 9px;
      position: absolute;
      &:before{
        font-family: "olading-iconfont" !important;
        content: "\e652";
      }
      /*display: none;*/
    }
    .el-range-separator {
      color: #cbced8;
      position: relative;
      top: -3px;
      margin-left: 3px;
      margin-right: 4px;
    }
    .el-range__close-icon {
      right: 8px;
      // background: red;
      position: absolute;
    }
  }

  /deep/.t-date-pricker__datetimerange{
    padding-left: 14px;
    width: 280px;
    background: #ffffff;
    border: 1px solid var(--top-search-field-border-color);
    border-radius: var(--top-search-btn-border-radius);
    .el-range-separator{
      margin: 0;
      margin-top: 3px;
    }
    &.exist-value .el-range-input{
      width: 110px !important;
    }
    .el-range__icon{
      display: none !important;
    }
  }

}
</style>