<template>
  <div>
    <component ref="component" :is="formItem.component" :textModel="textModel" v-bind="formItem" :value="value" v-on="$listeners" @input="onInput" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules, ValidateTrigger } from "../../../../components/common/form";
import { FormItem, FormJsonItem } from "../type";
import formFieldMixins from "../../formFieldMixins";

@Component({
  mixins:[formFieldMixins],
})
export default class  JCustom extends Vue {
  @Prop() formJsonItem!:FormJsonItem
  @Prop() formItem!:FormItem
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;


  field!: FieldContext;
  @Prop() value!:any

  error = false;

  @Watch("value")
  valueChange() {
    if (this.validateTrigger == "realtime") {
      this.validate();
    }
  }

  onInput(value){
    this.$emit("input",this.formItem.prop,value)
  }

  onFieldReset(){
    const resetFn = (this.$refs["component"] as any)?.onFieldReset
    resetFn && resetFn()
  }

  beforeMount() {
    this.field = registerField(this);
  }

  setValue(value){
    const setValue = (this.$refs["component"] as any)?.setValue
    setValue && setValue(value)
  }

  beforeDestroy() {
    this.field?.clear();
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    const validate = (this.$refs["component"] as any)?.validate
    if(!validate) return this.validateCore(this.value, false);
    const errorMessage = validate()
    this.error = Boolean(errorMessage);
    this.field.errorMessage = errorMessage || "";
    return errorMessage
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>