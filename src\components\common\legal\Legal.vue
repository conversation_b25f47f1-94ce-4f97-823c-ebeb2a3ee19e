<template>
  <span>{{ text }}</span>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class  Legal extends Vue {
  @Prop() value!: string;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let data = await store.loadLegal(this.value);
      if (data) {
        this.text = data.name ?? data.compName ?? "";
        this.$emit("update:change", data);
      }
    }
  }
}
</script>