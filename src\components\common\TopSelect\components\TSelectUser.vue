<template>
  <!-- 暂时先部处理 textModel -->
  <div class="t-select-user-dialog" :class="{textModel:textModel}">
     <z-select-user-dialog ref="select-user-dialog" 
      :tabField="tabField" 
      :inputWidth="inputWidth" 
      :disabled="isDisabled"
      :modalAppendToBody="modalAppendToBody"
      :title="dialogTitle"
      :limit="limit"
      :modal="modal"
      v-bind="formItem"
      :scene="scene"
      input 
      :placeholder="placeholder"
      @confirm="onInput" 
      :multiple="multiple"
    />
  </div>
</template>

<script lang="ts">

import { Component, Vue, Prop, Watch, Ref } from "vue-property-decorator";
import { FieldContext, registerField, validateRules } from "../../form";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";
import _ from "lodash"

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
})
export default class JRadio extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() rules!: any[];
  @Prop({ default:"280px" }) inputWidth!: String;
  // user dept  role
  @Prop({ default:"user" }) tabField!: String;
  @Prop({ default:false }) modalAppendToBody!: Boolean;
  // =search 无视返回的数据带禁用标识 都可以选择
  @Prop({ default:"search" }) scene!: String;
  @Prop({ default:false }) multiple!: Boolean;
  @Prop() dialogTitle!: Boolean;
  @Prop() placeholder!: String;
  @Prop({ default:9999 }) limit!: Number;
  // 是否需要遮罩层
  @Prop() modal!: Boolean;
  
  // id 
  @Prop({ default:"id" }) format!: String;
  
  
  field!: FieldContext;
  error = false;
  show = false

  beforeMount() {
    this.field = registerField(this);
  }

  get valueText (){
    try {
      return (this.formItem.options as any[]).find(item=>item.value === this.value).label
    }catch{
      return ""
    }
  }

  setValue(data){

    const setData = {
      roleId:[],
      userId:[],
      deptId:[],
    } as any

    const isNumberOrString = value => typeof value === "string" || typeof value === "number"

    // 混合 例如弹框带 部门，角色，人员
    // data = { userId:[xxx,xx],deptId:[11,22] }
    if(_.isObject(data) && !Array.isArray(data)) Object.assign(setData,data)

    if(this.tabField==="user") {
      if(Array.isArray(data)) {
        setData.userId = data.map(item=>Number(item))
      }else if (data && isNumberOrString(data)) {
        setData.userId = [Number(data)]
      }
    }

    if(this.tabField==="dept") {
      if(Array.isArray(data)) {
        setData.deptId = data.map(item=>Number(item))
      }else if (data && isNumberOrString(data)) {
        setData.deptId = [Number(data)]
      }
    }

    if(this.tabField==="role") {
      if(Array.isArray(data)) {
        setData.roleId = data.map(item=>Number(item))
      }else if (data && isNumberOrString(data)) {
        setData.roleId = [Number(data)]
      }
    }

    const ref = this.$refs["select-user-dialog"]  as any
    ref.setValue(setData)
  }


  beforeDestroy() {
    this.field.clear();
  }

  onInput(data) {
    let value = data

  
    if(!data) {
      data = {
        user:[],
        dept:[],
        role:[]
      }
    }

    if(data.user.length===0 && data.dept.length === 0 && data.role.length===0) {
      value = ""
    }

    if(value && this.tabField.split(",").length===1 && this.format == "id") {
      if(this.multiple) {
        if(this.tabField === "user" ) {
          value = data.user.map(item=>item.userId)
        }
        if(this.tabField === "dept" ) {
          value = data.dept.map(item=>item.id)
        }
        if(this.tabField === "role" ) {
          value = data.role.map(item=>item.id)
        }
      }else{
        // 单选
        if(this.tabField === "user" ) {
          value = data.user[0].userId
        }
        if(this.tabField === "dept" ) {
          value = data.dept[0].id
        }
        if(this.tabField === "role" ) {
          value = data.role[0].id
        }
      }
    }

    this.$emit("input", value);
  }

  onFieldReset() {
    const ref = this.$refs["select-user-dialog"]  as any
    ref.setValue({
      userId:[],
      roleId:[],
      deptId:[],
    })
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>