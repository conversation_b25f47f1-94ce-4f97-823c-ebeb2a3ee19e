import { toAmount } from "./utils"
import dayjs from "dayjs"

export const TABLE_CONFIG = {
  loadingCloseDelay: 300,
  getListDebounceDelay:100,
  emptyLoadingText: "正在加载数据",
  tableNotValue:"-",
  pageSizeCachePrefix:"oTablePageCache_",
  actionButtonMaxLen:3,
  headerCellStyle: {
    fontFamily: "PingFangSC-Regular",
    background: "#F7FAFD",
    borderColor: "#fff",
    fontSize: "12px",
    fontWeight: 400,
    color: "#777C94",
  },
  pagination: {
    // 当前每页条数
    limit: 20,
    // 当前页码
    currentPage: 1,
    // 总条数
    total: 0,
    pageSize: [10, 20, 50, 100,200],
    // pagerCount:5,
    layout: "total, sizes, prev, pager, next, jumper"
  }
}

export const TABLE_HEADER_TYPE_CONFIG = {
  // 序号列
  INDEX:{
    labelWidth:56,
    align:"center",
    headerAlign:"center",
    value(_row,_headerItem,scope){
      return scope.$index + 1
    }
  },
  // 手机号
  MOBILE:{
    labelWidth:120,
    value(val){
      return val.replace(/\s/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3')
    }
  },
  // 日期-年月日
  DATE:{
    labelWidth:100,
    value(val,tableHederItem){
      const dateFormat =  tableHederItem.valueFormat || "YYYY-MM-DD"
      return dayjs(val).format(dateFormat)
    }
  },
  // 日期-月份
  DATE_MONTH:{
    labelWidth:90,
    value(val,tableHederItem){
      const dateFormat =  tableHederItem.valueFormat || "YYYY-MM"
      return dayjs(val).format(dateFormat)
    }
  },
  // 年月日-时分秒
  DATE_TIME:{
    labelWidth:160,
    value(val,tableHederItem){
      const dateFormat =  tableHederItem.valueFormat || "YYYY-MM-DD HH:mm:ss"
      return dayjs(val).format(dateFormat)
    }
  },
  // 年月日-时分
  DATE_MINUTE:{
    labelWidth:160,
    value(val,tableHederItem){
      const dateFormat =  tableHederItem.valueFormat || "YYYY-MM-DD HH:mm"
      return dayjs(val).format(dateFormat)
    }
  },
  // 时间
  TIME:{
    labelWidth:100,
    value(val,tableHederItem){
      const dateFormat =  tableHederItem.valueFormat || "HH:mm:ss"
      return dayjs(val).format(dateFormat)
    }
  },
  // 数字
  NUMBER:{
    headerAlign:"right",
    align:"right",
  },
  // 金额
  AMOUNT:{
    headerAlign:"right",
    align:"right",
    value(val){
      return `￥${toAmount(val)}`
    }
  },
  // 忽略
  IGNORE:{
    labelWidth:1,
    value(val){
      return ""
    }
  },
  
}