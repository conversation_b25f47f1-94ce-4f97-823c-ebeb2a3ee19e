<template>
  <o-dialog :title="title" :value="show" @input="onInput" :closeOnClickModal="closeOnClickModal">
    <slot />
    <div slot="footer" v-if="showFoot" class="footer">
      <o-button @click="onClose(false)" class="button cancel-button" :loading="cancelLoading" v-if="cancelButton">{{ cancelButton }}</o-button>
      <o-button type="primary" @click="onClose(true)" class="button confirm-button" :loading="confirmLoading" v-if="confirmButton"> {{ confirmButton }} </o-button>
    </div>
  </o-dialog>
</template>

<script lang='ts'>
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({})
export default class MessageBox extends Vue {
  @Prop({ default: "提示" }) title!: string;
  @Prop() message!: string;
  @Prop() close!: any;
  @Prop() closeOnClickModal!: any;
  @Prop({ default:"确定" }) confirmButton!: string;
  @Prop({ default:"取消" }) cancelButton!: string;

  show = true;
  confirmLoading = false
  cancelLoading = false

  get showFoot (){
    return this.confirmButton || this.cancelButton
  }

  protected async onClose(v: boolean) {
    const loadingClose = (ifShow = false)=> this.show = ifShow
    try {
      // v = true , 为确定按钮
      if(v) {
        this.confirmLoading = true
      }else{
        // 点击取消按钮
        this.cancelLoading = true
      }
      // 等待异步结果
      await this.close(v,loadingClose);
      // 关闭弹框 ， 如果用户 返回Promise.reject 弹框不会关闭
      loadingClose(false)
    }finally{
      this.confirmLoading = this.cancelLoading = false
    }
  }

  onInput(val){
    if(!val) this.show = false
  }
}
</script>
<style lang='less' scoped>
.o-avatar {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: #fff;
}
.footer{
  .button{
    border-radius: 8px !important;
    width: 96px;
    height: 36px !important;
    line-height: 36px !important;
  }
  .cancel-button{
    border: 1px solid #cbced8 !important;
    color: #777c94 !important;
    &:hover{
      &::after{
        content: none !important;
      }
    }
  }
  .confirm-button{
    margin-right: 0 !important;
  }
}
</style>