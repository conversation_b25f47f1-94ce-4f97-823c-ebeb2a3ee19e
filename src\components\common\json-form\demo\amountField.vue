<template>
  <div>
    <el-switch
      :value="value"
      @input="onInput"
      active-text="按月付费"
      inactive-text="按年付费">
    </el-switch>
  </div>
</template>

<script>
export default {
  name:"amountField",
  props:["value","formItem"],
  methods:{
    // 当数据发生变化 将值传递给 JsonForm 组件
    onInput($event) {
      this.$emit("input", this.formItem.prop, $event);
    },
    // 当用户点击重置按钮
    onFieldReset(){
      console.log("点击重置按钮")
      this.onInput(this.formItem.defaultValue || false)
    }
  }
}
</script>