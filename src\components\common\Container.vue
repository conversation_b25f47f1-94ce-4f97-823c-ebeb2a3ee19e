<template>
  <div class="o-container">
    <el-row
      type="flex"
      justify="between"
      class="o-container-header"
    >

      <slot name="header" v-if="$slots.header" />

      <div v-else style="display:flex;">
        <div
          v-if="back "
          class="o-container-back"
          @click="handleBackBtnClick"
        >
          <i class="icon olading-iconfont oi-icon_arrow1"/>
          <span> 返回 </span>
          <i class="line"/>
        </div>

        <!-- 标题 -->
        <h1>
          {{ title }}
        </h1>
      </div>
    </el-row>
    <div
      class="o-container-main scrollbar"
      ref="o-container-main"
    
      :class="{ firefox : isFirefox , disabledWhee:isDisabledWhee , 'overflow-hidden':mainIsOverflowHidden}"
      @scroll="onScroll"
    >
      <slot />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Provide, Ref} from "vue-property-decorator";

@Component({
  inheritAttrs: false,
})
export default class Container extends Vue {
  @Prop({ default: "标题" }) title!: string;
  // 如果启用 keepAlive 是否回到当前页面 回到上次记录的滚动条位置
  @Prop({ default: false }) isBackLeaveScrollPosition!: Boolean;

  // 是否展示返回按钮
  @Prop({ default: false }) back!: Boolean;

  @Ref("o-container-main") main!:HTMLElement

  @Provide() oConCationContext = this;

  leaveScrollTop = 0
  isDisabledWhee =  false
  mainIsOverflowHidden = false

  // 获取oTable 组件实例
  get oTable (){
    let oTable:any = null 

    for(let item of this.$children) {
      if((item as any).componentName === "oTable") {
        oTable = item
        break
      }
    }

    return oTable
  }


  get isFirefox (){
    return window.navigator.userAgent.toLocaleLowerCase().includes('firefox')
  }

  activated(){
    if(this.isBackLeaveScrollPosition){
      this.backLeaveScrollPosition(false)
    }
  }

  // 容器是否可以滚动
  setContainerScrollable(show = true){
    this.mainIsOverflowHidden = !show
  }

  disabledScroll (e:any){
    const t = e.target.scrollTop
    const deltaY = e.deltaY;
    e.target.scrollTop = e.deltaY + e.target.scrollTop;
    e.preventDefault()
  }

  disabledMainScroll (){
    if(this.main){
      this.main.addEventListener("wheel",this.disabledScroll)  
    }
    this.isDisabledWhee = true
  }

  enableMainScroll(){
    if(this.main) {
      this.main.removeEventListener("wheel",this.disabledScroll)
    }
    this.isDisabledWhee = false
  }

  // 回到顶部
  backToTop() {
    this.scrollTo(0,0)
  }

  // 滚动到某个位置
  scrollTo (x,y){
    (this.$refs["o-container-main"] as HTMLElement).scrollTo(x,y);
  }

  // 获取滚动条位置
  getScrollTop(){
    return this.main.scrollTop
  }

  // 设置离开时候的滚动条位置
  setLeaveScrollPosition(y?:number){
    this.leaveScrollTop = y ?? this.getScrollTop()
  }

  // 回到离开页面时的滚动条位置
  backLeaveScrollPosition(y){
    const top = y || this.leaveScrollTop
    this.scrollTo(0,top)
    setTimeout(() => this.oTable?.$emit("updateTableHeaderRightFixed"));
  }

  handleBackBtnClick(){
    const listeners = this.$listeners as any
    if(listeners.back) {
      listeners.back()
    }else{
      this.$router.back()
    }
  }

  // // 回到表格表头吸顶位置
  // backToTableHeaderStickPosition(type){
  //   console.log("backToTableHeaderStickPosition");

  //   if(!this.oTable) {
  //     console.warn("o-container 没有找到o-table")
  //     return
  //   } 
    
  //   const top = this.oTable?.oTableOffsetTop
  //   const isTableHeaderSticky = this.oTable.isTableHeaderSticky
  //   const leaveScrollTop = this.leaveScrollTop
  //   // 如果上次操作的距离顶部的距离 大于 吸顶的距离，leaveScrollTop 重新赋值为 上次的距离，oTable getList 会根据 leaveScrollTop 定位到上次操作的位置
  //   if(leaveScrollTop > top) setTimeout(()=>this.setLeaveScrollPosition(leaveScrollTop))

  //   // 取消对滚动条的监听
  //   this.oTable.$emit("removeEventContainerScroll")

  //   // 重新绑定监听滚动条事件
  //   setTimeout(()=> this.oTable.$emit("addEventContainerScroll"),200)

  //   // 如果没有获取到距离顶部的距离 不进行处理
  //   if(!top) return false

  //   // 当前表头不是吸顶状态 调用此方法 拒绝滚动条回到吸顶位置  , 如果用户强行 滚动到表头位置
  //   if(!isTableHeaderSticky && type!=="force") return false

  //   // 把滚动条滚动到 表头吸顶的位置
  //   this.scrollTo(0,top)
    
  //   // 标识 当前表头为吸顶状态
  //   this.oTable.$emit("setIsTableHeaderSticky",true)

  //   return true
  // }

  // 回到表格表头吸顶位置
  backToTableHeaderStickPosition(){
    
    if(!this.oTable) {
      console.warn("o-container 没有找到o-table")
      return
    } 

    const stickyMap = this.oTable._stickyMap;

    if(!stickyMap) return false
    
    const { oTableOffsetTop,isTitleFixed } = stickyMap

    // 当前表头不是吸顶状态 调用此方法 拒绝滚动条回到吸顶位置  
    if(!isTitleFixed) return false

    // 把滚动条滚动到 表头吸顶的位置
    this.scrollTo(0,oTableOffsetTop)

    return true
  }

  mounted(){
    // 展开收起 ， 因为列表页面 表头吸顶 需要重新计算表头 位置，所以直接回到顶部 避免出现BUG
    if(!(this as any).$customEventBus) return 
    (this as any).$customEventBus.$on("menu-collapse", ()=> this.leaveScrollTop = 0 );
  }

  protected onScroll (){
    [...document.querySelectorAll(".o-table-action-dropdown-menu")].forEach(el=>el.remove())
    this.setLeaveScrollPosition()
  }

}
</script>

<style lang="less">
.o-container {
  height: calc(100vh - 80px);
  width: 100%;
  padding-left: 24px;
  border-radius: 8px 0 0 0;
  background: #fff;
  box-sizing: border-box;

  &-back{
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color:var(--o-primary-color);
    i{
      font-size: 14px;
      font-weight: normal;
      padding-right: 2px;
    }
    span{
      font-size: 16px;
    }
    .line{
      display: block;
      height:16px;
      background:#eee;
      margin: 0 12px;
      width: 1px;
    }
  }

  &-header {
    height: 48px;
    line-height: 48px;
    background: #ffffff;
    border-bottom: 1px solid #eef0f4;
    margin-right: 24px;
    h1 {
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 16px;
      color: #24262a;
      letter-spacing: 0;
      text-align: right;
      margin:0;
    }
  }

  &-main {
    height: calc(100vh - 64px - 16px - 48px);
    overflow-y: scroll;
    padding-right: 18px;
    position: relative;

    &.overflow-hidden{
      overflow: hidden;
    }
    
    &.pagination-fixed{
      height: calc(100vh - 64px - 16px - 48px - 56px);
    }

    &.firefox{
      height: calc(100vh - 64px - 16px - 48px - 56px + 16px);
    }
  }
  .scrollbar {
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(36,38,42,0.16);
      display: none;
    }
    &::-webkit-scrollbar {
      background: #fff;
      width: 6px;
    }
    &:hover {
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        opacity: 1;
      }
      &::-webkit-scrollbar-thumb{
        display: block;
      }
    }
  }
  .disabledWhee {
     &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #fff;
      display: none;
    }
  }
}
</style>