<template>
  <div
    class="contrast-action-dom"
    ref="contrast-action-dom"
  >
    <div
      v-if="tableContext.table.showActionButton"
      class="action-button-wrap"
      :class="{ isMore:more }"
    >
      <div
        v-for="(item) in list"
        :key="item.id"
      >
        <el-button
          class="action-button-wrap__btn"
          type="text"
          size="small"
        >{{ item.label }}
        </el-button>
      </div>
      <el-button
        class="more"
        v-show="more"
        icon="icon olading-iconfont oi-icon_other"
        type="text"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Inject, Component, Vue, Prop } from "vue-property-decorator";

@Component({
  inheritAttrs:false,
})
export default class TableActionPlaceholderHtml extends Vue {
  @Prop({ default:()=>[] }) list:any
  @Prop({ default:false }) more!:boolean
  
  @Inject() readonly tableContext!: any
}
</script>

<style lang="less" scoped>
.contrast-action-dom {
  display: inline-block;
  position: absolute;
  left: -9999999em;
  top: 0;
  .action-button-wrap {
    display: flex;
    padding-right: 0;
    padding-left: 0;
    &.isMore {
      padding-right: 0;
    }
  }
}
.action-button-wrap {
  display: flex;
  padding-right: 12px;
  &.isMore{
    padding-right: 0;
  }
  &__btn{
    padding:0 10px;
    border:0;
  }
  .more{
    width: 44px;
    padding: 0 10px;
  }
}
</style>