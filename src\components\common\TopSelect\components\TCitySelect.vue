<template>
  <el-cascader
    :value="value"
    :appendToBody="false"
    ref="cascader"
    filterable
    class="t-city-select"
    collapse-tags
    :options="options"
    @visible-change="visibleChange"
    :placeholder="placeholder"
    @input="onInput"
    clearable
    :props="{
      multiple:$attrs.multiple,
      checkStrictly:$attrs.checkStrictly
    }"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject, } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { FormItem } from "../type";
import { listCity } from "../../../../util";
import _ from "lodash";

let cityListJson:any = []

@Component({
  inheritAttrs: false,
})
export default class TMember extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() placeholder!: String;

  @Inject("oConCationContext") readonly oConCationContext!: any;


  field!: FieldContext;
  options = cityListJson
  loading = false

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset(){
    this.onInput(this.formItem.defaultValue || "")
  }

  visibleChange(show){
    // 如果在 container 容器内容 ，当下拉显示的 阻止container 容器的滚动行为
    if(this.oConCationContext) {
      this.oConCationContext.setContainerScrollable(!show)
    }
  }

  created(){
    this.loadCityList()
  }

  async loadCityList(){

    const formatterCityList = (data)=>{
      const map = {}
      const newList:any = []
      data.forEach((item:any)=>{
        item.children = item.childrenNum ? []:null
        item.value = item.id
        item.label = item.name
        map[item.id] = item
        if(map[item.parentId]) {
          map[item.parentId].children.push(item)
        }
        if(!item.parentId) newList.push(item)
      })
      return newList
    }

    if(cityListJson.length) {
      this.options = _.cloneDeep(cityListJson)
      return 
    }
    
    this.loading = true

    const { list } = await listCity({
      filters: {
        country: ["CHN"],
      },
      start:0,
      limit:99999,
    })

    this.loading = false

    cityListJson = this.options = _.cloneDeep(formatterCityList(list))

  }
}
</script>
<style scoped>
</style>