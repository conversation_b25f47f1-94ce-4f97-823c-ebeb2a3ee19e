<template>
  <div class="drag">
    <i class="icon olading-iconfont oi-table-sort" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import _ from "lodash";

@Component
export default class TableCheckBox extends Vue {
 
}
</script>
<style scoped>
.drag{
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon{
  font-size: 14px;
}
</style>