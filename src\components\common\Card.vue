<template>
  <div class="o-card" :class="cardClass" :style="cardStyle">
    <div v-if="bar != 'none'" class="bar"></div>
    <div class="body">
      <div class="header">
        <slot name="header"
          ><div class="title" style="">{{ title }}</div></slot
        >
      </div>
      <slot></slot>
      <div v-if="$slots['footer']" class="gap"></div>
      <div v-if="$slots['footer']" class="footer">
        <slot name="footer"></slot>
      </div>
    </div>
    <i
      v-if="closable || closable === ''"
      class="el-icon-delete close"
      style="font-size: 20px; flex-shrink: 0"
      @click="$emit('close')"
    ></i>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component
export default class OCard extends Vue {
  @Prop() type!: string;
  @Prop() title!: string;
  @Prop() closable!: boolean;
  @Prop() border!: "none" | "solid";
  @Prop() bar!: "none" | "left";
  @Prop() backgroundColor!: string;

  get cardClass() {
    let type = this.type ?? "primary";
    let o = {} as any;
    o["o-card-" + type] = true;
    o["borderless"] = this.border === "none";
    return o;
  }

  get cardStyle() {
    let backgroundColor = this.backgroundColor ?? null;
    return {
      background: backgroundColor,
    };
  }

  mounted() {}
}
</script>
<style scoped lang="less">
.o-card {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  outline: solid 1px;
  background-color: #F7F8FA;

  .bar {
    width: 3px;
    position: absolute;
    left: 0px;
    top: 0px;
    bottom: 0px;
  }

  .body {
    flex-grow: 1;
    padding: 10px;
  }

  .header {
    margin-right: 20px;
    margin-bottom: 10px;
    overflow: hidden;
  }

  .title {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 18px;
    font-weight: bold;
  }

  .close {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .gap {
    height: 1px;
    background-color: #eaeaea;
    margin: 10px 0px 10px 0px;
  }
}

.borderless {
  outline: none;
}
</style>