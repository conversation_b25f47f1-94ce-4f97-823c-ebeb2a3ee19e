import { Component, Prop } from 'vue-property-decorator'
import * as tsx from 'vue-tsx-support'

@Component
export default class FormBlock extends tsx.Component<any> {

  // 一行有几列
  @Prop() public columns!: number;

  protected render() {
    let columns = this.columns ?? 2
    let children = this.$slots.default ?? []
    children = children.filter(o => o.componentOptions)
    let perspan = 24 / columns

    let rows: any[] = []
    let row: any[] = []
    let usedCol = 0
    children.forEach((item, i) => {
      const props = item.componentOptions?.propsData as any
      const colspan = Math.max(parseInt(props?.colspan ?? 1), 1)

      if (usedCol + colspan > columns) {
        rows.push(row)
        row = []
        usedCol = 0
      }

      usedCol += colspan
      row.push(<el-col span={perspan * colspan}>{item}</el-col>)
    })
    if (row.length > 0) {
      rows.push(row)
    }
    return <div class="o-form-block">{rows.map(row => <el-row gutter={20}>{row}</el-row>)}</div>
  }
}