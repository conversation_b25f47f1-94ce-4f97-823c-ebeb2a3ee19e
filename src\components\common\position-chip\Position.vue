<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class  Position extends Vue {
  @Prop() value!: string;
  @Prop({ default: "span" }) tag!: string;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let position = await store.loadPost(this.value);
      if (position) {
        this.text = position.name;
        this.$emit("update:change", position);
      }
    }
  }

  render(h) {
    return h(this.tag, this.text);
  }
}
</script>