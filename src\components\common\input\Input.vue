<template>
  <div>
    <div v-if="textModel">
      <pre v-if="type==='textarea'" style="margin:0;" >{{value || '-'}}</pre>
      <div v-else>
        {{value || '-'}}
      </div>
    </div>
    <el-input
      v-else
      ref="input"
      class="o-input"
      size="medium"
      :class="[actualClass, type === 'password' ? 'passwordClass' : '']"
      :value="value"
      :placeholder="placeholder"
      :type="type"
      :maxlength="maxlength"
      :showPassword="showPassword"
      :minlength="minlength"
      :show-word-limit="showWordLimit"
      :disabled="isDisabled"
      @clear="$emit('clear')"
      :rows="rows"
      :autosize="autosize"
      :readonly="isReadonly"
      :clearable="clearable"
      @input="onInput"
      @blur="onBlur"
    >
      <template #append>
        <slot name="append" />
      </template>
    </el-input>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import formFieldMixins from "../../../components/common/formFieldMixins"
import {
  FieldContext,
  registerField,
  ValidateTrigger,
  validateRules,
} from "../form";

interface AutoSize {
  minRows: number;
  maxRows: number;
}
@Component({
  mixins:[formFieldMixins]
})
export default class  Input extends Vue {
  @Prop() type!: string;
  @Prop() placeholder!: string;
  @Prop() value!: string;
  @Prop() maxlength!: number;
  @Prop() minlength!: number;
  @Prop() showWordLimit!: boolean;
  @Prop() showPassword!: boolean;
  @Prop({ default: 3 }) rows!: number;
  @Prop({ default: false }) trim!: boolean;
  @Prop() textAlign!: "left" | "right";
  @Prop() autosize!: boolean | AutoSize;
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;
  @Prop({ default:true }) clearable!: any;
  
  get isError() {
    return this.error;
  }

  protected get actualClass() {
    let o = {} as any;
    let align = this.textAlign ?? "left";
    // align = "right"
    o["o-text-align-" + align] = true;
    o["is-error"] = this.error;
    return o;
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }

  field!: FieldContext;
  @Ref("input")
  input: any;

  error = false;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  focus() {
    this.input.focus();
  }

  async validate() {
    return this.validateCore(this.value, false);
  }

  async onInput(value: any) {
    if (this.validateTrigger == "prevent" && value != "") {
      let a = await this.validateCore(value, true);
      if (a) {
        return;
      }
    }
    this.$emit("input", value);
  }

  onBlur(e: any) {
    if(this.trim) this.$emit("input", String(this.value).trim())
    this.$emit("blur")
    if (this.validateTrigger == "blur") {
      this.validate();
    }
  }

  @Watch("value")
  valueChange() {
    if (this.validateTrigger == "realtime") {
      this.validate();
    }
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>

<style lang="less" scoped>
</style>