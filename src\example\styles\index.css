*, :after, :before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
ul {
	list-style: none;
	padding: 0;
}
.fa-icon {
	width: auto;
	height: 1em;
   /* 或任意其它字体大小相对值 */
   /* 要在 Safari 中正常工作，需要再引入如下两行代码 */
	max-width: 100%;
	max-height: 100%;
	vertical-align: middle;
}
.fm2-container {
	background: #fff;
	height: 100%;
	border: 1px solid #e0e0e0;
}
.fm2-container .el-container {
	height: 100% !important;
}
.fm2-container > .el-container {
	background: #fff;
}
.fm2-container .fm2-main {
	position: relative;
}
.fm2-container .fm2-main > .el-container {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
}
.fm2-container main {
	padding: 0;
}
.fm2-container footer {
	height: 30px;
	line-height: 30px;
	border-top: 1px solid #e0e0e0;
	font-size: 12px;
	text-align: right;
	color: #409eff;
	background: #fafafa;
}
.fm2-container footer a {
	color: #409eff;
}
.center-container {
	border-left: 1px solid #e0e0e0;
	border-right: 1px solid #e0e0e0;
}
.center-container .btn-bar {
	height: 45px;
	line-height: 45px;
	font-size: 18px;
	border-bottom: solid 2px #e4e7ed;
	text-align: right;
}
.center-container .el-main {
	padding: 0;
	position: relative;
	background: #fafafa;
}
.components-list {
	padding: 8px 0;
	width: 100%;
	height: 100%;
}
.components-list .config-tab {
	height: 45px;
	line-height: 45px;
	display: inline-block;
	width: 105px;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	position: relative;
	cursor: pointer;
}
.components-list .config-tab.active {
	border-bottom: solid 2px #409eff;
}
.components-list .widget-cate {
	padding: 8px 12px;
	font-size: 13px;
}
.components-list ul {
	position: relative;
	overflow: hidden;
	padding: 0 10px 10px;
	margin: 0;
}
.components-list .form-edit-widget-label {
	font-size: 12px;
	display: block;
	width: 48%;
	line-height: 26px;
	position: relative;
	float: left;
	left: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin: 1%;
	color: #333;
	border: 1px solid #f4f6fc;
}
.components-list .form-edit-widget-label:hover {
	color: #409eff;
	border: 1px dashed #409eff;
}
.components-list .form-edit-widget-label > a {
	display: block;
	cursor: move;
	background: #f4f6fc;
	border: 1px solid #f4f6fc;
}
.components-list .form-edit-widget-label > a .icon {
	margin-right: 6px;
	margin-left: 8px;
	font-size: 14px;
	display: inline-block;
	vertical-align: middle;
}
.components-list .form-edit-widget-label > a span {
	display: inline-block;
	vertical-align: middle;
}
.widget-form-container {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.widget-form-container .widget-form-list {
	border: 1px dashed #999;
	min-height: 600px;
	margin: 10px;
}
.widget-form-container .widget-form-list .widget-col-list {
	min-height: 50px;
	border: 1px dashed #ccc;
	background: #fff;
}
.widget-form-container .widget-form-list .widget-view {
	padding-bottom: 18px;
	position: relative;
	border: 1px dashed rgba(170, 170, 170, 0.7);
	background-color: rgba(236, 245, 255, .3);
	margin: 2px;
}
.widget-form-container .widget-form-list .widget-view .el-form-item__content {
	position: unset;
}
.widget-form-container .widget-form-list .widget-view.is_req .el-form-item__label::before {
	content: '*';
	color: #f56c6c;
	margin-right: 4px;
}
.widget-form-container .widget-form-list .widget-view .widget-view-description {
	height: 15px;
	line-height: 15px;
	font-size: 13px;
	margin-top: 6px;
	color: #909399;
}
.widget-form-container .widget-form-list .widget-view .widget-view-action {
	position: absolute;
	right: 0;
	bottom: 0;
	height: 28px;
	line-height: 28px;
	background: #409eff;
	z-index: 9;
}
.widget-form-container .widget-form-list .widget-view .widget-view-action i {
	font-size: 14px;
	color: #fff;
	margin: 0 5px;
	cursor: pointer;
}
.widget-form-container .widget-form-list .widget-view .widget-view-drag {
	position: absolute;
	left: -2px;
	top: -2px;
	bottom: -18px;
	height: 28px;
	line-height: 28px;
	background: #409eff;
	z-index: 9;
}
.widget-form-container .widget-form-list .widget-view .widget-view-drag i {
	font-size: 14px;
	color: #fff;
	margin: 0 5px;
	cursor: move;
}
.widget-form-container .widget-form-list .widget-view:after {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	display: block;
}
.widget-form-container .widget-form-list .widget-view:hover {
	background: #ecf5ff;
	outline: 1px solid #409eff;
	outline-offset: 0px;
}
.widget-form-container .widget-form-list .widget-view:hover.active {
	outline: 2px solid #409eff;
	border: 1px solid #409eff;
	outline-offset: 0;
}
.widget-form-container .widget-form-list .widget-view:hover .widget-view-drag {
	display: block;
}
.widget-form-container .widget-form-list .widget-view.active {
	outline: 2px solid #409eff;
	border: 1px solid #409eff;
}
.widget-form-container .widget-form-list .widget-view.ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	outline-width: 0;
	height: 3px;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
}
.widget-form-container .widget-form-list .widget-table {
	padding-bottom: 0;
	padding: 5px;
	background-color: rgba(253, 246, 236, .3);
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper {
	min-height: 50px;
	background: #fff;
	display: flex;
	justify-content: flex-start;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-row td {
	border-bottom: 0;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-left {
	width: 51px;
	border-left: 1px solid #ebeef5;
	border-right: 1px solid #ebeef5;
	border-top: 1px solid #ebeef5;
	flex: none;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view {
	border: 1px solid #ebeef5;
	width: 200px;
	float: left;
	height: 100%;
	position: relative;
	display: block;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .el-table {
	height: 100%;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view.is_req .el-form-item__label::before {
	content: '*';
	color: #f56c6c;
	margin-right: 4px;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .widget-view-description {
	height: 15px;
	line-height: 15px;
	font-size: 13px;
	margin-top: 6px;
	color: #909399;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .widget-view-action {
	position: absolute;
	right: 0;
	bottom: 0;
	height: 28px;
	line-height: 28px;
	background: #409eff;
	z-index: 9;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .widget-view-action i {
	font-size: 14px;
	color: #fff;
	margin: 0 5px;
	cursor: pointer;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .widget-view-drag {
	position: absolute;
	left: -2px;
	top: -2px;
	bottom: -18px;
	height: 28px;
	line-height: 28px;
	background: #409eff;
	z-index: 9;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view .widget-view-drag i {
	font-size: 14px;
	color: #fff;
	margin: 0 5px;
	cursor: move;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view::after {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	display: block;
	content: '';
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view::before {
	display: none;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view:hover {
	background: #ecf5ff;
	outline: 1px solid #409eff;
	outline-offset: -1px;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view:hover.active {
	border: 1px solid #409eff;
	outline: 1px solid #409eff;
	outline-offset: -1px;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view:hover .widget-view-drag {
	display: block;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view.active {
	outline: 1px solid #409eff;
	border: 1px solid #409eff;
	outline-offset: -1px;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view.ghost {
	background: #f56c6c;
	outline-width: 0;
	width: 5px !important;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
	position: relative;
	outline: none !important;
	border: 0 !important;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-view.ghost::after {
	background: #f56c6c;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 9999;
	content: '';
	outline: none;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-content {
	width: 100%;
	outline: 1px dashed #ccc;
	background: #fff;
	flex: 1;
	margin: 0 1px;
	overflow: auto;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-content > div {
	height: 100%;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-content .widget-table-col {
	height: 100%;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-content .widget-table-col .ghost {
	background: #f56c6c;
	position: relative;
	content: '';
	float: left;
	height: 100%;
	width: 5px !important;
	list-style: none;
	font-size: 0;
	overflow: hidden;
	outline: none;
}
.widget-form-container .widget-form-list .widget-table .widget-table-wrapper .widget-table-content .widget-table-col .ghost::after {
	background: #f56c6c;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	z-index: 9999;
	content: '';
	outline: none;
}
.widget-form-container .widget-form-list .widget-table.active {
	outline: 2px solid #e6a23c;
	border: 1px solid #e6a23c;
}
.widget-form-container .widget-form-list .widget-table:hover {
	background: #fdf6ec;
	outline: 1px solid #e6a23c;
	outline-offset: 0px;
}
.widget-form-container .widget-form-list .widget-table:hover.active {
	outline: 2px solid #e6a23c;
	border: 1px solid #e6a23c;
	outline-offset: 0;
}
.widget-form-container .widget-form-list .widget-table .widget-view-action.widget-col-action {
	background: #e6a23c;
}
.widget-form-container .widget-form-list .widget-table .widget-view-drag.widget-col-drag {
	background: #e6a23c;
}
.widget-form-container .widget-form-list .widget-table::after {
	display: none;
}
.widget-form-container .widget-form-list .widget-table.ghost {
	background: #f56c6c;
	outline-width: 0;
	height: 5px;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
	position: relative;
	outline: none;
	border: 0;
}
.widget-form-container .widget-form-list .widget-table.ghost::after {
	background: #f56c6c;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	content: '';
	display: block;
	z-index: 999;
}
.widget-form-container .widget-form-list .widget-col {
	padding-bottom: 0;
	padding: 5px;
	background-color: rgba(253, 246, 236, .3);
}
.widget-form-container .widget-form-list .widget-col.active {
	outline: 2px solid #e6a23c;
	border: 1px solid #e6a23c;
}
.widget-form-container .widget-form-list .widget-col:hover {
	background: #fdf6ec;
	outline: 1px solid #e6a23c;
	outline-offset: 0px;
}
.widget-form-container .widget-form-list .widget-col:hover.active {
	outline: 2px solid #e6a23c;
	border: 1px solid #e6a23c;
	outline-offset: 0;
}
.widget-form-container .widget-form-list .widget-col .el-col {
	min-height: 50px;
}
.widget-form-container .widget-form-list .widget-col.ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	outline-width: 0;
	height: 3px;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
}
.widget-form-container .widget-form-list .widget-col .widget-view-action.widget-col-action {
	background: #e6a23c;
}
.widget-form-container .widget-form-list .widget-col .widget-view-drag.widget-col-drag {
	background: #e6a23c;
}
.widget-form-container .widget-form-list .widget-col::after {
	display: none;
}
.widget-form-container .widget-form-list .ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	outline-width: 0;
	height: 3px;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
}
.widget-form-container .ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	position: relative;
}
.widget-form-container .ghost::after {
	background: #f56c6c;
}
.widget-form-container li.ghost {
	height: 5px;
	list-style: none;
	font-size: 0;
	overflow: hidden;
}
.widget-form-container .widget-grid {
	position: relative;
	border-left: 5px solid transparent;
	padding: 5px;
	margin: 0 !important;
}
.widget-form-container .widget-grid.active {
	border-left: 5px solid #409eff;
	background: #b3d8ff;
}
.widget-form-container .widget-grid-container.ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	outline-width: 0;
	height: 3px;
	box-sizing: border-box;
	font-size: 0;
	content: '';
	overflow: hidden;
	padding: 0;
}
.widget-form-container .ghost {
	background: #f56c6c;
	border: 2px solid #f56c6c;
	position: relative;
}
.widget-form-container .ghost::after {
	background: #f56c6c;
}
.widget-form-container li.ghost {
	height: 5px;
	list-style: none;
	font-size: 0;
	overflow: hidden;
}
.widget-config-container {
	position: relative;
}
.widget-config-container .el-header {
	border-bottom: solid 2px #e4e7ed;
	padding: 0 5px;
}
.widget-config-container .config-tab {
	height: 45px;
	line-height: 45px;
	display: inline-block;
	width: 145px;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	position: relative;
	cursor: pointer;
}
.widget-config-container .config-tab.active {
	border-bottom: solid 2px #409eff;
}
.widget-config-container .config-content {
	padding: 10px;
}
.widget-config-container .config-content .el-form-item__label {
	padding: 0;
	font-weight: 500;
}
.widget-config-container .config-content .el-form-item {
	border-bottom: solid 1px #e1e1e1;
	padding-bottom: 10px;
}
.widget-config-container .ghost {
	background: #fff;
	border: 1px dashed #409eff;
}
.widget-config-container .ghost::after {
	background: #fff;
	display: block;
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.widget-config-container ul {
	margin: 0;
	padding: 0;
}
.widget-config-container li.ghost {
	list-style: none;
	font-size: 0;
	display: block;
	position: relative;
}
.viewer-container {
	z-index: 99999 !important;
}
.form-empty {
	position: absolute;
	text-align: center;
	width: 300px;
	font-size: 20px;
	top: 200px;
	left: 50%;
	margin-left: -150px;
	color: #ccc;
}
.create-field {
	display: flex;
	margin: 8px;
}
.create-field .input-field {
	margin-right: 5px;
}
.fieldMain {
	display: flex;
	min-height: 200px;
}
.fieldMain .field-left {
	width: 200px;
	margin-right: 5px;
	border-right: 1px solid #f5f5f5;
}
.fieldMain .field-left ul > li {
	padding: 5px;
	border-bottom: 1px solid #f5f5f5;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	color: #666;
	transition: all 0.3s;
}
.fieldMain .field-left ul > li:hover {
	background: #f5f5f5;
}
.fieldMain .field-left ul > li.active {
	border-bottom: 1px solid blue;
}
.fieldMain .field-right {
	flex: 1;
}
.fieldMain .field-right .create-name h4 {
	margin: 0;
	font-size: 14px;
	font-weight: 500;
}
.fieldMain .field-right .create-name span {
	margin-left: 5px;
	font-size: 12px;
	color: #8492a6;
}
.fieldMain .field-right .create-name .fields-name {
	margin-bottom: 15px;
}
