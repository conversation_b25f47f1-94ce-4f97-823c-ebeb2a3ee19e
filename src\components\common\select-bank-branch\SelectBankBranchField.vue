<template>
  <div>
    <placeholder
      :border="!mobile"
      style="min-height: 36px"
      @click="onClick"
      :placeholder="placeholder"
    >
      <bank-branch v-if="value" :value="value" @update:change="onChange" />
    </placeholder>
    <popup v-model="show" title="选择支行信息" mobileHeight="80%">
      <select-bank-branch
        style="height: 100%"
        v-if="show"
        @input="onInput"
        :value="value"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue } from "vue-property-decorator";

import SelectBankBranch from "./SelectBankBranch.vue";
import Popup from "../Popup.vue";
import BankBranch from "./BankBranch.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, registerField } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";

@Component({
  components: {
    SelectBankBranch,
    Popup,
    Placeholder,
    BankBranch,
  },
})
export default class SelectBankBranchField extends Vue {
  @Prop() value!: string;
  @Prop() readonly!: any;
  @Prop() placeholder!: string;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get mobile() {
    return this.context.isMobile;
  }

  show = false;

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  validate() {
    let rule = this.field.requiredRule;
    if (rule && !this.value) {
      return Promise.resolve(rule.message ?? "请选择");
    }
    return Promise.resolve();
  }

  onInput(v) {
    this.show = false;
    this.$emit("input", v[0]);
  }

  onClick() {
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }

  onChange(v) {
    this.$emit("update:change", v);
  }
}
</script>