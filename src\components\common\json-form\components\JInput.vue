<template>
  <div>
    <o-input
      ref="input"
      :value="value"
      @input="onInput"
      @clear="onClear"
      @blur="onBlur"
      v-bind="formItem"
      :showWordLimit="showWordLimit"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from "vue-property-decorator";
import { FieldContext, registerField, validateRules, ValidateTrigger } from "../../form";
import { Cascader as ElCascader } from "element-ui";
import formFieldMixins from "../../formFieldMixins";
import { FormItem } from "../type";

@Component({
  inheritAttrs: false,
  mixins:[formFieldMixins],
  components: {
    ElCascader,
  },
})
export default class JCascader extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() rules!: any[];
  @Prop() validateTrigger!: ValidateTrigger;

  @Ref("input") 
  oInput:any

  field!: FieldContext;
  error = false;

  valueText = ""

  get showWordLimit(){
    if(this.formItem.type === "textarea") return true
    return false
  }

  @Watch("value")
  valueChange() {
    if (this.validateTrigger == "realtime") {
      this.validate();
    }
  }

  onBlur(e: any) {
    if (this.validateTrigger == "blur") {
      setTimeout(()=>{
        this.validate();
      },100)
    }
  }

  onClear(){
    if (this.validateTrigger == "blur") {
      this.validate();
    }
  }

  protected get actualRules() {
    return this.field?.rules ? this.field?.rules : this.rules;
  }


  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    const valueType = this.formItem?.valueType

    switch (valueType) {
    case "int":
      $event = String($event).replace(/[^0-9]/ig, "")
      if($event.indexOf("0")===0 && $event.indexOf(".")!==1) $event = Number($event)
      break;
    case "decimals_1":
      try {
        $event = (String($event).match(/\d+(\.\d{0,1})?/) as any)[0]
        if($event.indexOf("0")===0 && $event.indexOf(".")!==1) $event = Number($event)
      }catch(err){
        $event = ""
      }
      break;
    case "decimals_2":
      try {
        $event = (String($event).match(/\d+(\.\d{0,2})?/) as any)[0] 
        if($event.indexOf("0")===0 && $event.indexOf(".")!==1) $event = Number($event)
      }catch(err){
        $event = ""
      }
      break;
    }


    // if(this.formItem.trim) $event = $event.trim()
    
    this.$emit("input", $event);
  }

  onFieldReset(){
    if(Array.isArray(this.formItem.defaultValue)) {
      this.onInput(this.formItem.defaultValue)
    }
  }

  async validate() {
    return this.validateCore(this.value as any, false);
  }

  async validateCore(v: string, skipMarkError: boolean) {
    let r = await validateRules(v, this.actualRules);
    if (!r.ok && !skipMarkError) {
      if (this.field) {
        this.field.errorMessage = r?.detail[0]?.message ?? "";
      }
      this.error = true;
    }
    if (r.ok) {
      this.error = false;
      if (this.field) {
        this.field.errorMessage = "";
      }
    }
    return r?.detail[0]?.message ?? "";
  }
}
</script>