<template>
  <div class="invoice-card" 
    :class="{showSelect:showSelect}"
  >

    <o-card
      :type="colorType"
      :closable="false"
    >
      <div class="checkbox" v-if="showSelect">
        <o-checkbox v-model="value.checked" :disabled="!disabled"></o-checkbox>
      </div>
      <div class="action-buttons">
        <div v-if="edit" >
          <van-icon name="edit" @click="$emit('edit')" />
        </div>
        <div  v-if="showDelete">
          <van-icon name="icon olading-iconfont oi-icon_delete" @click="$emit('del')" />
        </div>
      </div>
      <div class="container">
        <div
          slot="header"
          class="tags"
        >
          <el-tag class="m-r-5" :type="colorType">{{invoiceType()}}</el-tag>
          <!-- 增值税发票才展示验真标签 -->
          <template v-if="value.invoiceType==='VAT_INVOICE'">
            <o-tag
              class="m-r-5"
              type="success"
              v-if="value.verifyResult == 'PASS'"
            >已验真</o-tag>

            <o-tag
              class="m-r-5"
              type="success"
              v-else
            >未验真</o-tag>
          </template>

          <!-- 作废发票 -->
          <template v-if="value.invoiceCancellationMark && value.invoiceCancellationMark==='CANCELLATION'">
            <el-tag
              class="m-r-5"
              type="info"
              v-if="value.verifyResult == 'PASS'"
            >已作废</el-tag>
          </template>

          <!-- 王鑫说：ofdVerifyResult!=="UN_CHECK" 才可以进行下面的判断 -->
          <template v-if="value.ofdVerifyResult !== 'UN_CHECK' ">
            <!-- OPF 文件前端会展示是否为原文件tag，可通过 ofdVerifyResult字段判断是否展 示标签和标签文案内容 -->
            <o-tag
              class="m-r-5"
              type="info"
              v-if="value.ofdVerifyResult == 'PASS'"
            >原文件</o-tag>
            <!-- 如果 ofdVerifyResult 有值，代表是OPD， 如果不为PASS 则代表非源文件-->
            <o-tag
              class="m-r-5"
              type="primary"
              v-if="value.ofdVerifyResult && value.ofdVerifyResult !== 'PASS'"
            >非原文件</o-tag>
          </template>
        </div>
        <div class="info flex flex-1">
          <render-image :originalInvoice="value.originalInvoice" />
          <div class="info-right flex-1">
            <div class="flex top" v-if="title() !== '-' ">
              <div class="title">{{  title()  }}</div>
              <van-icon
                name="warning"
                :color="hintLevelColor"
                class="icon-warn"
                v-if="warnMsg"
              />
            </div>
            <div class="time" >
              {{timeLabel}}
              <span
                v-for="item in desc()"
                :key="item"
                v-html="item"
              />
            </div>
          </div>
        </div>

        <div class="footer flex">
          <div class="flex-1" :style="{color:hintLevelColor}">{{ warnMsg }}</div>
          <span class="amount-text">价税合计：￥<span class="amount">{{total()}}</span></span>
        </div>
      </div>
    </o-card>
  </div>
</template>
<script lang="ts">
import dayjs from "dayjs";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

const TYPE = {
  FLIGHT_ITINERARY: "飞机行程单",
  TAXI_TICKET: "出租车票",
  BUS_TICKET: "客车票",
  FERRY_TICKET: "船票",
  TRAIN_TICKET: "火车票",
  CONSUMPTION_RECEIPT: "消费小票",
  ROAD_BRIDGE_INVOICE: "过路过桥发票",
  FIXED_INVOICE: "定额发票",
  MACHINE_PRINTING_INVOICE: "机打发票",
  OTHER_INVOICE: "其他发票",
};

const LABEL_TYPE = {
  // 增值税
  VAT_INVOICE:"发票日期",
  // 机打发票
  MACHINE_PRINTING_INVOICE: "发票日期",
  // 飞机行程单
  FLIGHT_ITINERARY: "填开日期",
  // 火车票
  TRAIN_TICKET: "乘车日期",
  // 出租车票
  TAXI_TICKET: "发票日期",
  // 其他发票
  OTHER_INVOICE: "",
  // 客车票
  BUS_TICKET: "发票日期",
  // 定额发票
  FIXED_INVOICE: "",
  // 消费小票
  CONSUMPTION_RECEIPT: "日期",
  // 船票 ---- 
  FERRY_TICKET: "发票日期",
  // 过路过桥发票
  ROAD_BRIDGE_INVOICE: "发票日期",
};

const vat = {
  VAT_SPECIAL_INVOICE: "增值税专用发票",
  VAT_GENERAL_INVOICE: "增值税普通发票",
  VAT_GENERAL_INVOICE_ELECTRON: "增值税电子普通发票",
  VAT_ELECTRONIC_INVOICE: "增值税电子发票",
};

type ThemeType = "primary" | "success" | "warning" | "danger";

const renderImageIconEnum = {
  "OTHER": ["oi-qita","#88D38B"],
  "PDF": ["oi-bg-pdf","#EB7E7A"],
  "OPD": ["oi-OFD","#7A93F7"],
}

const dateFormat = (date,format="YYYY-MM-DD")=> {
  if(!date) return ""
  return dayjs(date).format(format)
}

@Component
class RenderImage extends Vue{
  @Prop() originalInvoice!: any;

  render(h){
    if(this.originalInvoice.type === "IMAGE") {
      return h('vanImage',{
        class:"img",
        props:{
          width:"60",
          height:"60",
          src:this.originalInvoice.url
        },
        on:{
          click:()=>{
            window.open(this.originalInvoice.url)
          }
        }
      })
    }

    const icon = renderImageIconEnum[this.originalInvoice.type]
    
    return h('div',{
      class:"img"
    },[
      h('i',{
        class:`olading-iconfont ${icon[0]}`,
        style:{
          color:icon[1]
        },
        on:{
          click:()=>{
            if(this.originalInvoice.url) {
              window.open(this.originalInvoice.url)
            }
          }
        }
      })
    ])
  }
}
import { amountFormat } from "../../util";

@Component({
  components:{
    RenderImage
  }
})
export default class InvoiceCard extends Vue {
  @Prop() value!: any;
  @Prop({ default: "primary" }) type!: ThemeType;
  @Prop({ default:false }) showSelect!: boolean;
  @Prop({ default:false }) edit!: boolean;
  @Prop({ default:false }) showDelete!: boolean;
  @Prop({ default:false }) disabled!: boolean;

  invoiceType() {
    let value = this.value;
    if (!value.invoiceType) {
      return "空";
    }
    if (value.invoiceType == "VAT_INVOICE") {
      return vat[value.vatInvoiceType] ?? "增值税发票";
    } else {
      return TYPE[value.invoiceType];
    }
  }

  get colorType(){

    const TYPE = [
      [["VAT_INVOICE","MACHINE_PRINTING_INVOICE"],"warning"],
      [["FLIGHT_ITINERARY","TRAIN_TICKET","TAXI_TICKET","BUS_TICKET","FERRY_TICKET"],"primary"],
      [["OTHER_INVOICE","FIXED_INVOICE","ROAD_BRIDGE_INVOICE"],"info"]
    ]

    for(let item of TYPE) {
      if(item[0].includes(this.value.invoiceType)) {
        return item[1]
      }
    }

    return "info"
  }

  get warnMsg() {
    let hintList = this.value.hintList;
    if(!hintList) return ""
    return hintList.map(item=>item.content).join("、")
  }

  get hintLevelColor (){
    let hintList = this.value.hintList;
    if(!hintList) return ""
    return this.value.hintList.some(item=>item.level === "FATAL") ? "#DB3231" : "rgb(255 133 2)"
  }

  verifyTrue() {
    let value = this.value;
    if (value.verifyResult) {
    }
  }

  title() {
    let value = this.value;
    if (value.invoiceType == ["VAT_INVOICE"]) {
      return value.sellerName;
    } else if (value.invoiceType == "MACHINE_PRINTING_INVOICE") {
      return value.sellerName;
    } else if (value.invoiceType == "TRAIN_TICKET") {
      return value.departureStation + "-" + value.destinationStation;
    } else if (value.invoiceType == "FLIGHT_ITINERARY") {
      return value.departureStation + "-" + value.destinationStation;
    } else if (value.invoiceType == "TAXI_TICKET") {
      return "出租车发票(" + value.location + ")";
    } else if (value.invoiceType == "BUS_TICKET") {
      return value.departureStation + "-" + value.destinationStation;
    } else if (value.invoiceType == "FERRY_TICKET") {
      return value.departureStation + "-" + value.destinationStation;
    } else if (value.invoiceType == "OTHER_INVOICE") {
      return "其他票据";
    } else if (value.invoiceType == "FIXED_INVOICE") {
      return "定额发票";
    } else if (value.invoiceType == "ROAD_BRIDGE_INVOICE") {
      return "过路过桥发票";
    } else if (value.invoiceType == "CONSUMPTION_RECEIPT") {
      return "消费小票";
    }
  }

  get timeLabel(){
    // 根据不同类型 展示不同 label
    const label = LABEL_TYPE[this.value.invoiceType]
    if(!label) return ""
    return `${LABEL_TYPE[this.value.invoiceType]}:`
  }

  desc() {
    let value = this.value;
    value.invoiceDate = value.invoiceDate?dateFormat(value.invoiceDate):""

    const getDateTimeText = (date,time)=> {
      if(date && time) return `${date}  ${time} <br/>`
      if(date && !time)  return `${date} <br/>`
      if(!date && time)  return `${time} <br/>`
      return "</br>"
    }

    if (value.invoiceType == ["VAT_INVOICE"]) {
      return [dateFormat(value.invoiceDate)];
    } else if (value.invoiceType == "MACHINE_PRINTING_INVOICE") {
      return [dateFormat(value.invoiceDate)];
    } else if (value.invoiceType == "TRAIN_TICKET") {
      const date = dateFormat(value.trainDate);
      const time = value.trainTime ? dateFormat(value.trainTime,'HH:mm'):""

      return [
        getDateTimeText(date,time),
        "车次:" + value.trainNumber+'<br/>',
        "乘车人:" + (value.name || ""),
      ];
    } else if (value.invoiceType == "FLIGHT_ITINERARY") {
      const date =  dateFormat(value.issueDate)
      return [
        getDateTimeText(date,""),
        "航班号:" + (value.flight || "") +'<br/>',
        "乘机人:" + (value.passenger || "")+'<br/>',
      ];
    } else if (value.invoiceType == "TAXI_TICKET") {
      return [
        dateFormat(value.invoiceDate)+'<br/>',
        "上车:" + dateFormat(value.departTime,"HH:mm")+'<br/>',
        "下车:" + dateFormat(value.arriveTime,"HH:mm")+'<br/>',
        "里程:" + value.mileage+"KM",
      ];
    } else if (value.invoiceType == "BUS_TICKET") {
      const date = value.invoiceDate?dateFormat(value.invoiceDate):"",
        time = value.time?dateFormat(value.time," HH:mm:ss"):""
      if(!date && !time) return []
      return [date+ time]
      
    } else if (value.invoiceType == "FERRY_TICKET") {
      if(!value.invoiceDate && !value.time) return []
      return [value.invoiceDate + ": " + value.time];
    } else if (value.invoiceType == "OTHER_INVOICE") {
      return [];
    } else if (value.invoiceType == "FIXED_INVOICE") {
      return [];
    } else if (value.invoiceType == "ROAD_BRIDGE_INVOICE") {
      return [value.invoiceDate];
    } else if (value.invoiceType == "CONSUMPTION_RECEIPT") {
      return [dateFormat(value.date)];
    } else {
      return [];
    }
  }

  total() {
    let value = this.value;
    let total = value.totalAmount ?? value.total
    return total ? amountFormat(total,2,".",",") : 0 ;;
  }
}
</script>
<style scoped lang="less">
.invoice-card {
  .m-r-5 {
    margin-right: 5px;
  }
  .flex {
    display: flex;
  }
  .flex-1 {
    flex: 1;
  }

  &.showSelect {
    /deep/ .o-card {
      .container{
        padding-left: 43px;
      }
    }
  }
  
  /deep/ .o-card {
    display: flex;
    background: #fff;
    position: relative;
    .checkbox {
      width: 53px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      justify-content: center;
    }
    .action-buttons{
      position: absolute;
      right: 0;
      height: 42px;
      display: flex;
      align-items: center;
      top: 0;
      padding-top: 12px;
      div{
        width: 35px;
        font-size: 20px;
        color: #878787;
        cursor: pointer;
      }
    }
    .icon-warn {
      color: var(--o-danger-color);
      font-size: 16px;
    }
    .info-right {
      width: 0;
    }
    .tags {
      padding-bottom: 9px;
    }
    .container {
      width: 100%;
      padding-left: 15px;
      box-sizing: border-box;
    }
    .body {
      padding-top: 12px;
      padding-bottom: 12px;
    }
    .header {
      margin: 0 !important;
    }
    .info {
      margin-bottom: 12px;
      .img {
        margin-right: 10px;
        border-radius: 4px;
        overflow: hidden;
        width: 60px;
        height: 60px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #F7F8FA;
        i{
          font-size: 32px;
        }
      }
      .top {
        margin-bottom: 8px;
        align-items: center;
      }
      .title {
        margin-right: 3px;
        height: 14px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #070f29;
        line-height: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .time {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        line-height: 16px;
        font-size: 13px;
        color: #6a6f7f;
      }
    }
    .footer {
      border-top: 0.5px solid #eaeaea;
      padding-top: 13px;

      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 12px;
      color: #d6342a;
      line-height: 12px;
      .amount-text {
        color: #070f29;
        font-size: 14px;
      }
      .amount {
        font-weight: bold;
        font-size: 15px;
      }
    }
  }
}
</style>