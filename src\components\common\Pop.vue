<template>
  <div :class="actualClass">
    <slot />
  </div>
</template>
<script lang="tsx">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { popup } from "../../util";

const effectTable = {
  bottom: "slideUp",
  top: "slideDown",
  left: "slideRight",
  right: "slideLeft",
};

@Component
export default class  Pop extends Vue {
  @Prop() value!: boolean;
  // 是否可以通过点击遮罩层关闭弹框
  @Prop({ default:true }) closeOnClickModal!: boolean;
  @Prop() position!: "bottom" | "top" | "left" | "right" | "center";

  el!: HTMLElement;
  popup!: any;

  playing = false;
  dirty = false;

  get actualClass() {
    let v = [] as any[];
    v.push("o-app")
    v.push("o-pop")
    v.push("o-pop-" + this.actualPosition);
    if (this.actualPosition == "center") {
      v.push("o-elevation-3")
    }
    return v;
  }

  protected get actualPosition() {
    return this.position ?? "center";
  }

  mounted() {
    this.el = this.$el as HTMLElement;
    this.el.parentElement?.removeChild(this.el);
    this.update()
  }

  beforeDestroy() {
    if (this.popup) {
      this.popup();
    }
  }

  @Watch("value")
  protected onValueChange() {
    this.update();
  }

  protected async update() {
    // 当前正在进行动画
    if (this.playing) {
      this.dirty = true;
      return;
    }
    this.dirty = false;

    this.playing = true;
    if (this.value && this.popup == null) {
      this.popup = await popup(this.el, {
        effect: effectTable[this.actualPosition],
        closeOnClickModal:this.closeOnClickModal,
        onMaskClick: () => this.$emit("input", false),
      });
      this.$emit("open")
    } else if (!this.value && this.popup != null) {
      this.$emit("close")
      await this.popup();
      this.popup = null;
    }
    this.playing = false;

    if (this.dirty) {
      this.update();
    }
  }
}
</script>
<style lang="less">
.o-pop {
  position: fixed;
  background-color: white;

  &-center {
    position: static;
    width: 400px;
    margin: 0 auto;
  }

  &-top {
    width: 100%;
    top: 0;
  }

  &-bottom {
    width: 100%;
    bottom: 0;
  }

  &-left {
    width: 50%;
    height: 100%;
    left: 0;
  }

  &-right {
    width: 50%;
    height: 100%;
    right: 0;
  }
}
</style>