<template>
  <button
    class="o-button o-box o-hoverable o-rounded"
    :disabled="actualDisabled"
    :icon="icon"
    @click.prevent="onClick"
    :class="actualClass"
  >
    <i v-if="actualIcon" :class="actualIcon" style="margin-right: 5px" />
    <span>
      <slot />
    </span>
  </button>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class Button extends Vue {
  @Prop() loading!: any;
  @Prop() outlined!: any;
  @Prop() disabled!: any;
  @Prop() icon!: string;
  @Prop() type!: string | undefined;

  protected get actualIcon() {
    if (this.loading || this.loading === "") {
      return "el-icon-loading";
    }
    return this.icon;
  }

  protected get actualDisabled() {
    if (this.loading || this.loading === "") {
      return "disabled";
    }
    if (this.disabled || this.disabled === "") {
      return "disabled";
    }
  }

  protected get actualClass() {
    let type = "default";
    let outlined = false;
    if (this.type === "" || this.type == null || this.type === "default") {
      type = "primary";
      outlined = true;
    } else {
      type = this.type;
      outlined = this.outlined || this.outlined === "";
    }

    let v = [] as string[];
    if (outlined) {
      v.push("o-outlined");
    }
    v.push("o-" + type);
    if (this.actualDisabled != null) {
      v.push("o-disabled");
    }
    return v;
  }

  protected onClick(event: any) {
    if (!this.loading && !this.disabled) {
      this.$emit("click", event);
    }
  }
}
</script>