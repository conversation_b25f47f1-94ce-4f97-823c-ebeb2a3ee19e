<template>
  <el-input
    :value="value"
    @input="onInput"
    @blur="onBlur"
    v-bind="$attrs"
  ></el-input>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { Input as ElInput } from "element-ui";
import { FormItem } from "../type";
import _ from "lodash"

@Component({
  inheritAttrs: false,
  components: {
    ElInput
  },
})
export default class OelSelect extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;
  @Prop() readonly trim!: boolean;
  @Prop({ default:"string" }) readonly valueType!: string;
  

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    if(this.valueType === "int") {
      $event = String($event).replace(/[^0-9]/ig, "")
    }

    this.$emit("input", $event);
  }

  onBlur(){
    if(this.trim && this.valueType === "string") this.onInput(String(this.value).trim())
  }

  onFieldReset(){
    this.onInput(this.formItem.defaultValue || "")
  }
}
</script>