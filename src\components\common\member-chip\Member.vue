<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { store } from "../../../util";

@Component
export default class  Member extends Vue {
  @Prop() value!: string;
  @Prop({ default: "span" }) tag!: string;
  @Prop({ default:false }) showDeleteText!: boolean;

  text = "";

  mounted() {
    this.refresh();
  }

  @Watch("value")
  watchValue() {
    this.refresh();
  }

  async refresh() {
    if (this.value) {
      let member = await store.loadMember(this.value);
      if (member) {
        this.text = member.name;
        this.$emit("update:change", member);
      }else if(this.showDeleteText){
        const name = `(${this.value})已删除`;
        this.text = name;
        this.$emit("update:change", name);
      }
    }
  }

  render(h) {
    return h(this.tag, this.text);
  }
}
</script>