<template>
  <div>
    <slot></slot>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { asArray } from "../common/util";

@Component({
  name: "o-toggle-item-group",
})
export default class ToggleItemGroup extends Vue {
  // 选中的值
  @Prop() value: any;
  // 是否是多选
  @Prop() multiple!: any;
  // 是否必须至少选中一个
  @Prop() mandatory!: any;

  ctx = {
    items: [] as any[],
  } as any;

  addToggleItem(item) {
    this.ctx.items.push(item);
    return {
      toggle: () => {
        const actived = [] as any[];
        const index = this.ctx.items.indexOf(item);

        // 计算当前item是否active
        let active = this.activeValues.indexOf(index) >= 0;
        if (active && this.actualMandatory && this.activeValues.length <= 1) {
          // 当设置了mandatory且是最后一个active时
          actived.push(index);
        } else if (!active) {
          actived.push(index);
        }

        if (this.actualMultiple) {
          actived.push(...this.activeValues.filter((o) => o !== index));
        }

        this.$emit("input", actived);
      },
      active: () => {
        const index = this.ctx.items.indexOf(item);
        return this.activeValues.indexOf(index) >= 0;
      },
    };
  }

  protected get actualMultiple() {
    return this.multiple || this.multiple === "";
  }

  protected get actualMandatory() {
    return this.mandatory || this.mandatory === "";
  }

  protected get activeValues() {
    return asArray(this.value);
  }
}
</script>