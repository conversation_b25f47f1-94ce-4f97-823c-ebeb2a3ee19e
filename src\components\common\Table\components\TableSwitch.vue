<template>
    <div>
      <o-switch :value="newValue" @input="onInput" shape="square"></o-switch>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, } from "vue-property-decorator";
import _ from "lodash"

@Component
export default class TableCheckBox extends Vue {
  @Prop() readonly value!: any;
  @Prop({ default: ()=>{} }) readonly scope!: any;

  get newValue (){
    return !!this.value
  }

  onInput (check){
    if(_.isFunction(this.$attrs.onInput)) this.$attrs.onInput(this.scope.row,check)
  }
}
</script>