@import './var.less';
@import './css-variables.less';

body {
  width: 100vw;
  overflow-x: hidden;
}

.o-app {

  font-family: "PingFangSC-Regular", "微软雅黑", "Avenir", Helvetica, Arial, sans-serif;
  font-weight: 500;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #24262a;

  .shallow() {
    background-color: currentColor;
    border-radius: inherit;
    bottom: -1px;
    content: "";
    left: -1px;
    opacity: 0.08;
    position: absolute;
    pointer-events: none;
    right: -1px;
    top: -1px;
  }

  .o-box {
    color: black;
    background-color: white;
    border: solid 1px white;
    position: relative;

    // overflow: hidden;

    &:after {
      .shallow();
      background-color: white;
      opacity: 0;
    }

    &-bar {
      position: absolute;

      &.o-left {
        left: -1px;
        top: -1px;
        bottom: -1px;
        width: 6px;
        border-top-left-radius: inherit;
        border-bottom-left-radius: inherit;
      }

      &.o-right {
        right: -1px;
        top: -1px;
        bottom: -1px;
        width: 6px;
        border-top-right-radius: inherit;
        border-bottom-right-radius: inherit;
      }

      &.o-top {
        left: -1px;
        right: -1px;
        top: -1px;
        height: 6px;
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
      }

      &.o-bottom {
        left: -1px;
        right: -1px;
        bottom: -1px;
        height: 6px;
        border-bottom-left-radius: inherit;
        border-bottom-right-radius: inherit;
      }
    }

    &.o-disabled {
      &:after {
        .shallow();
        background-color: white;
        opacity: 0.5;
        // left: -1px;
        // right: -1px;
        // top: -1px;
        // bottom: -1px;
      }

    }

    &.o-hoverable-shadow:not(.o-disabled) {
      &:hover {
        .o-elevation-3();
      }
    }

    &.o-hoverable:not(.o-disabled) {

      &:not(.o-hover-shadow):hover:after {
        .shallow();
        background-color: #555555;
        opacity: 0.1;
      }

      &.o-primary:hover:after:not(.o-hover-shadow) {
        background-color: white;
      }

      &.o-success:hover:after:not(.o-hover-shadow) {
        background-color: white;
      }

      &.o-warning:hover:after:not(.o-hover-shadow) {
        background-color: white;
      }

      &.o-danger:hover:after:not(.o-hover-shadow) {
        background-color: white;
      }

      &.o-outlined:not(.hover-shadow) {
        &:hover:after {
          .shallow();
        }
      }
    }

    &.o-primary {
      color: white;
      background-color: var(--o-primary-color);
      border-color: var(--o-primary-color);
    }

    &.o-success {
      color: white;
      background-color: var(--o-success-color);
      border-color: var(--o-success-color);
    }

    &.o-warning {
      color: white;
      background-color: var(--o-warning-color);
      border-color: var(--o-warning-color);
    }

    &.o-danger {
      color: white;
      background-color: var(--o-danger-color);
      border-color: var(--o-danger-color);
    }

    &.o-outlined {
      // outline: solid 1px;
      border: solid 1px;
      border-color: @border-color;
      background-color: inherit;

      &.o-primary {
        border-color: var(--o-primary-color);
        color: var(--o-primary-color);

        .o-box-bar {
          background-color: var(--o-primary-color);
        }
      }

      &.o-success {
        border-color: var(--o-success-color);
        color: var(--o-success-color);

        .o-box-bar {
          background-color: var(--o-success-color);
        }
      }

      &.o-warning {
        border-color: var(--o-warning-color);
        color: var(--o-warning-color);

        .o-box-bar {
          background-color: var(--o-warning-color);
        }
      }

      &.o-danger {
        border-color: var(--o-danger-color);
        color: var(--o-danger-color);

        .o-box-bar {
          background-color: var(--o-danger-color);
        }
      }
    }

    &.o-shallow {
      background-color: white;

      &.o-primary {
        color: var(--o-primary-color);
      }

      &.o-success {
        color: var(--o-success-color);
      }

      &.o-warning {
        color: var(--o-warning-color);
      }

      &.o-danger {
        color: var(--o-danger-color);
      }

      &:after {
        .shallow();
        opacity: 0.12;
      }

      &.o-disabled:after {
        background-color: white;
        opacity: 0.5;
      }
    }
  }

  .o-rounded {
    border-radius: 4px;
  }

  .o-circle {
    border-radius: 50%;
  }

  .o-elevation-1 {
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-2 {
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, .2), 0 2px 2px 0 rgba(0, 0, 0, .14), 0 1px 5px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-3 {
    box-shadow: 0 3px 3px -2px rgba(0, 0, 0, .2), 0 3px 4px 0 rgba(0, 0, 0, .14), 0 1px 8px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-4 {
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, .2), 0 4px 5px 0 rgba(0, 0, 0, .14), 0 1px 10px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-5 {
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, .2), 0 5px 8px 0 rgba(0, 0, 0, .14), 0 1px 14px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-6 {
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, .2), 0 6px 10px 0 rgba(0, 0, 0, .14), 0 1px 18px 0 rgba(0, 0, 0, .12)
  }

  .o-elevation-7 {
    box-shadow: 0 4px 5px -2px rgba(0, 0, 0, .2), 0 7px 10px 1px rgba(0, 0, 0, .14), 0 2px 16px 1px rgba(0, 0, 0, .12)
  }

  .o-elevation-8 {
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, .2), 0 8px 10px 1px rgba(0, 0, 0, .14), 0 3px 14px 2px rgba(0, 0, 0, .12)
  }

  &.o-modal-parent {
    overflow: hidden;
  }

  &.o-modal {
    position: fixed;
    overflow: hidden;
    display: flex;
    align-items: center;
    backdrop-filter: blur(0px);
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: 0;
    padding: 0;
    z-index: 2002;
    transition: background-color, backdrop-filter 0.2s;
    background-color: rgba(#000, 0);
    

    &.fade {
      background-color: rgba(#000, 0.7);
      // backdrop-filter: blur(5px);
    }
  }

  .o-button {
    border: solid 1px;
    font-size: 14px;
    height: 40px;
    padding: 0 20px;
    cursor: pointer;
    margin: 0 5px;
    line-height: 1;
  }

  .o-chip {
    border-radius: 16px;
    height: 32px;
    padding: 0 12px;
    display: inline-flex;
    align-items: center;
    font-size: 12px;

    &:not(.o-primary, .o-success, .o-warning, .o-danger) {
      background-color: #F1F1F1;
    }


    &-close {
      padding: 3px 0 0 0;
      margin: 0 -5px 0 5px;
      cursor: pointer;
    }
  }

  .o-input {
    .el-input-group__append {
      background-color: #fff;
    }

    .el-input__suffix-inner{
      display: flex;
    }

    textarea {
      resize: none;
    }

    &.o-text-align-left {
      input {
        text-align: left;
      }
    }
    &.o-text-align-right {
      input {
        text-align: right;
      }
    }
  }

  .o-input.is-error {
    .el-input__inner {
      border-color: var(--o-red);
    }

    .el-textarea__inner {
      border-color: var(--o-red);
    }
  }

  .o-form-block {
    overflow-x: hidden;
  }

  .o-check-group {
    &.horizontal {
      display: flex;

      .o-check {
        margin: 0 10px 0 0;
      }
    }

    &.vertical {
      .o-check:not(:last-child):not(:only-child) {
        margin: 0 0 10px 0;
      }
    }


  }

  // 按照颜色循环生成样式
  .colors-loop(@i: 1) when (@i <=length(@colors)) {
    @color : extract(@colors, @i);

    // 按钮的颜色
    .el-button--@{color} {
      background-color: var(e("--o-@{color}-color"));
      border-color: var(e("--o-@{color}-color"));

      // &:hover {
      //   opacity: .8;
      // }

    }

    .o-tag-@{color} {
      color: var(e("--o-@{color}-color"));
      background-color: var(e("--o-shallow-@{color}-color"));
      border:none;
      .el-icon-close{
        color: var(e("--o-@{color}-color"));
      }
      .el-tag__close:hover{
        color: var(e("--o-shallow-default-color"));
      }
      &.o-tag-info{
        .el-tag__close:hover{
          color: #fff;
          background:#CCCED7;
        }
      }
    }

    .o-card-@{color} {
      outline-color: var(e("--o-@{color}-color"));

      .bar {
        background-color: var(e("--o-@{color}-color"));
      }
    }

    .colors-loop((@i + 1));
  }

  .colors-loop();


  .el-button--text {
    color: var(--o-primary-color);
  }

  // .el-button--default:not(.is-disabled) {
  //   border-color: var(--o-primary-color);
  //   color: var(--o-primary-color);
  // }
  .el-button--default .el-button--primary:not(.is-disabled) {
    color: #FFF;
  }
  .el-button--default:not(.el-button--primary, .is-disabled) {
    border-color: var(--o-primary-color);
    color: var(--o-primary-color);
  }

  .el-switch.is-checked .el-switch__core {
    background-color: var(--o-primary-color);
    border-color: var(--o-primary-color);
  }

  .el-dropdown-menu__item:focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: var(--o-shallow-primary-color);
    color: var(--o-primary-color);
  }

  .van-checkbox__icon--checked .van-icon {
    background-color: var(--o-primary-color);
    border-color: var(--o-primary-color);
  }

  .o-field.o-mobile {
    .o-field-content>.el-input .el-input__inner {
      padding: 0px;
      border: 0px;
    }

    .o-field-content>.el-textarea .el-textarea__inner {
      padding: 0px;
      border: 0px;
    }
  }

  .o-field {

    .o-placeholder {
      min-height: 36px;
      cursor: pointer;
    }

    .el-input-group__append {
      padding-right: 0px;
    }

    input {
      line-height: 36px;
    }

    .o-text-align-left {
      input {
        text-align: left;
      }
    }

    .o-text-align-right {
      input {
        text-align: right;
      }
    }

    ::placeholder {
      color: #CCCCCC
    }

    .el-input-group__append {
      border: none;
    }
  }

  .o-form-section {

    &-title {
      
      display: flex;
      align-items: center;
      font-family: PingFangSC-S0pxibold;
      font-weight: 600;
      font-size: 14px;
      color: #24262A;
      margin-left: 8px;
      margin-bottom: 22px;

      .text{
        line-height: 14px;
        height: 14px;
      }

      &.none{
        display: none;
      }
     
      span{
        padding-left: 12px;
      }

      &.title-render{
        &::before{
          content: none;
        }
      }

      &::before {
        width: 4px;
        height: 14px;
        background-color: var(--o-primary-color);
        content: "";
        display: block;
      }
    }
  }

  .o-form-scene {

    font-size: 14px;
    margin: 0 40px;

    // 大型表单
    &.large {
      .o-form-block {
        margin: 0 40px;
      }
    }
  }

}

.el-message,
.van-toast {
  z-index: 9999 !important;
}

//h5兼容顶部/底部安全距离
@supports (top: constant(safe-area-inset-top)) or (top: env(safe-area-inset-top)) {
  .o-safe-area-top {
    margin-top: constant(safe-area-inset-top);
    margin-top: env(safe-area-inset-top);
  }
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  .o-safe-area-bottom {
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);
  }
}