<template>
  <div>
    <div v-if="textModel">
      <span v-if="!value">-</span>
      <city v-if="value" :value="value" :full="true" :change.sync="valueTextMap" />
    </div>
    <placeholder
      v-else
      :border="!mobile"
      style="min-height: 36px"
      @click="onClick"
      :clearable="true"
      @clear="onInput([])"
      :placeholder="placeholder"
    >
      <city v-if="value" :value="value" :full="true"  :change.sync="valueTextMap"  />
    </placeholder>
    <popup v-model="show" title="选择城市" mobileHeight="80%">
      <select-city
        style="height: 100%"
        v-if="show"
        @input="onInput"
        :value="value"
        :max-deep="maxDeep"
        :selectable-deep="selectableDeep"
        :only-select-leaf="onlySelectLeaf"
      />
    </popup>
  </div>
</template>
<script lang="ts">
import { Component, Inject, Prop } from "vue-property-decorator";

import SelectCity from "./SelectCity.vue";
import City from "./City.vue";
import Popup from "../Popup.vue";
import Placeholder from "../Placeholder.vue";
import { FieldContext, registerField } from "../form";
import { CONTEXT_PROVIDER, defaultContext } from "../context";
import formFieldMixins from "../formFieldMixins";

@Component({
  mixins:[formFieldMixins],
  components: {
    SelectCity,
    Popup,
    City,
    Placeholder,
  },
})
export default class SelectCityField extends formFieldMixins {
  @Prop() value!: string;
  // 显示的最大深度,默认无限制，0为第一级,
  @Prop() maxDeep!: number;
  // 可选择的深度,例如[0, 2]，只允许选择第一级和第三级, 默认无限制
  @Prop() selectableDeep!: number[];
  // 只能选择叶子节点
  @Prop() onlySelectLeaf!: any;
  @Prop() placeholder!: string;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  get mobile() {
    return this.context.isMobile;
  }

  show = false;
  valueTextMap:any = {}

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  beforeDestroy() {
    this.field?.clear();
  }

  get valueText(){
    return this.valueTextMap?.name
  }

  async validate() {
    let rule = this.field.requiredRule;
    if (rule && !this.value) {
      return rule.message ?? "请选择";
    }
    return null;
  }

  onInput(v) {
    this.show = false;
    this.$emit("input", v[0]);
  }

  onClick() {
    if(this.isDisabled) return false
    if (this.readonly || this.readonly === "") {
      return;
    }
    this.show = true;
  }
}
</script>