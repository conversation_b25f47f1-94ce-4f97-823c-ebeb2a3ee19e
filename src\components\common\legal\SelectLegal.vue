<template>
  <div class="select-legal">
    <tree-view
      class="select-legal-tree"
      :loader="loader"
      :value="selected"
      :limit="20"
      rootTitle="选择法人实体"
      @change="onChange"
    >
      <template v-slot:default="r">
        <div class="select-legal-record">
          {{ r.item.name }}
        </div>
      </template>
    </tree-view>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { listLegal } from "../../../util";
import OButton from "../button";

import TreeView from "../TreeView.vue";

@Component({
  components: {
    TreeView,
    OButton,
  },
})
export default class  SelectLegal extends Vue {
  @Prop() value!: any;

  selected: string[] = [];
  loader: any = null;

  mounted() {
    this.refresh();
    this.measure();
  }

  @Watch("value")
  watchValue() {
    this.measure();
  }

  measure() {
    this.selected.splice(0, this.selected.length);
    if (this.value) {
      this.selected = [this.value];
    }
  }

  async refresh() {
    this.loader = async ({ directory, start, limit, keywords }) => {
      let data = await listLegal({
        start,
        limit,
      });
      return data.list.map((o) => ({
        id: o.id,
        name: o.name ?? o.compName,
        selectable: true,
        isDirectory: false,
        data: o,
      }));
    };
  }

  onChange(item) {
    this.selected = [item.id];
    this.$emit("input", [...this.selected]);
  }

  async onOk() {
    this.$emit("input", [...this.selected]);
  }
}
</script>

<style scoped lang="less">
.select-legal {
  display: flex;
  flex-direction: column;

  .select-legal-tree {
    // flex-grow: 1;
    height: 100%;
  }

  .select-legal-record {
    padding: 20px 5px 20px 5px;
  }

  .select-legal-bottom {
    display: flex;
    height: 50px;
    padding: 5px 20px 5px 20px;

    .select-legal-status {
      flex-grow: 1;
    }

    .select-legal-button {
      width: 100px;
    }
  }
}
</style>