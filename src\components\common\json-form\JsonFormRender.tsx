import { Component,Inject,Prop,Ref } from "vue-property-decorator";
import { TForm<PERSON>son, FormJsonItem } from "./type";
import * as tsx from 'vue-tsx-support'
import { CreateElement } from "vue"
import _ from "lodash";
const files = import.meta.globEager("./components/*.vue")
const components = {};

for(var key in files){
  const file = files[key]
  const tmp = key.split('/')
  components[tmp[2].replace(/(\.\/|\.vue)/g, "")] = file.default
}

// 根据type 类型做一层映射
export enum DynamicComponentName {
  radio = "j-radio",
  cascader = "j-cascader",
  inputRange = "j-input-range",
  // 再包一层 custom 作为代理， 避免用户自定义组件 无法使用 registerField 造成的无法通讯问题
  custom = "j-custom",
  inputNumber = "j-input-number",
  input = "j-input",
  citySelect = "j-city-select",
  remoteSearchSelect = "j-remote-search-select",
  render = "j-render",
  checkbox = "j-checkbox",
  datePicker = "j-date-picker",
  selectUser = "j-select-user",
  newSelect = "j-new-select",

  dateTimeRange = "o-select-datetime-range-field",
  dateTime = "o-select-datetime-field",
  // input = "o-input",
  select = "o-select-field",
  member = "o-select-member-field",
  dept = "o-select-dept-field",
  group = "o-form-section",
  city = "o-select-city-field",
}

@Component({
  inheritAttrs: false,
  components
})
export default class JsonFormRender extends tsx.Component<any> {

  @Prop({ default: () => { } }) readonly formJson!: TFormJson;
  @Prop() readonly columns!: number;
  @Prop() readonly textModel!: boolean;
  @Prop({ default:"realtime" }) readonly validateTrigger!: string;
  
  // 请求函数，可适配请求API地址 和 函数
  @Prop({ default: () => { } }) readonly save!: Function | string;

  @Ref("form") form: any;

  @Inject("JsonForm")
  JsonForm:any

  formData: Record<string, any> = {};

  // 获取组件名称
  getCommentName(item) {
    const componentName = item.type;
    if (item.type === "custom") return DynamicComponentName[item.type];
    return DynamicComponentName[componentName];
  }

  async created() {
    this.initFormData();
    await this.$nextTick()
    this.onWatchFormData()
  }

  // 煎饼formData发生变化
  onWatchFormData() {
    // 当表单内容发生变化 发射change事件
    this.$watch("formData", newValue => this.$emit("change", newValue), {
      deep: true
    })
  }

  // 获取表单数据
  getFormData(noValidate?:boolean) {
    if(!noValidate) return this.form.getValues()
    return this.form.form.getValues()
  }

  // 动态设置表单数据 
  setFormData(formData) {
    this.formData = Object.assign(this.formData, { ...formData })
    this.$nextTick(()=>{
      Object.keys(this.formData).forEach(key=>{
        const ref = (this.$refs[key] as any)
        const setValue = ref && ref.setValue
        setValue && setValue(this.formData[key])
      })
    })
  }

  // 监听自定义组件 数据发生变化 ， 跟formData 进行数据联动
  customComponentOnInput(prop, value) {
    this.formData[prop] = value;
    this.$emit("valueTextChange",prop,value)
  }

  // 初始化formData
  initFormData() {
    
    const setData = (item) => {
      const formItem = item.item

      let value = formItem.defaultValue ?? formItem.value ?? "";

      // 如果是多选 并且没有值的情况 给value 默认值赋值为数组
      if(formItem.multiple) value = value === "" ? [] : value
      // 多选按钮组件
      if(item.type === "checkbox" && !value) value = []

      this.$set(this.formData, formItem.prop, value);
    }

    for (let item of this.formJson) {
      const formItem = item.item
      if (item.children) item.children.forEach(setData)
      if (formItem) setData(item)
    }
  }

  resetForm() {
    this.form.handleResetButtonClick();
  }

  // 动态触发某一项校验
  validator(prop){
    const ref = (this.$refs[prop] as any)
    return ref && ref.validate()
  }

  // 获取指定选项的Ref
  getFormItemRef(prop){
    return (this.$refs[prop] as any)
  }


  clearFieldsErrorMessage() {
    this.form.clearFieldsErrorMessage();
  }

  protected render(h: CreateElement) {

    const { formData, columns, 
      formJson, getCommentName, 
      customComponentOnInput, 
      save,$attrs,textModel } = this

    // 最外层组件
    const formSceneWrap = (content) => {
      const noop = ()=>{}
      const cancel = this.$listeners.cancel || noop
      
      return (<o-form-scene
        ref="form"
        class="form-render"
        save={save}
        textModel={textModel}
        readonly={$attrs.readonly}
        disabled={$attrs.disabled}
        labelWidth={$attrs.labelWidth}
        showResetButton={$attrs.showResetButton}
        hideButton={$attrs.hideFootButton}
        saveButtonText={$attrs.saveButtonText}
        onCancel={cancel}
      >{content}</o-form-scene>)
    }

    const getIfShow = (ifShow)=> {
      // 字段设置了隐藏
      if(!ifShow) return false
      // 如果ifShow 为函数 ， 通过函数返回结果 判断是否隐藏
      if(_.isFunction(ifShow) && !ifShow()) return false
      return true
    }

    // 分组栏
    const formGroup = ({ children, groupName , groupNameRender, columns: itemColumns,ifShow }) => {
      if(!getIfShow(ifShow)) return ""

      // 用户设置的大于默认的
      const newColumns = itemColumns || columns

      return (<o-form-section
        title={groupName}
        titleRender={groupNameRender}
        columns={newColumns}
      >
        {children.map(item => formField(item))}
      </o-form-section>)
    }

    // 动态组件
    const dynamicComponent = (item:FormJsonItem) => {
      const formItem = item.item as any
      const prop = formItem?.prop as string
      const isCustomComponent = item.type === "custom"
      const componentName = getCommentName(item)
      const popupTitle = formItem?.popupTitle || formItem?.label
      let placeholder = formItem?.placeholder || formItem?.label

      if(!formItem?.placeholder) {
        if(item.type === "select") placeholder = "请选择" + placeholder
        if(item.type === "input") placeholder = "请输入" + placeholder
      }

      if(item.type==="select") {
        formItem.labelProp = "label"
        formItem.valueProp = "value"
      }

      const vNode = h(componentName, {
        props: {
          ...formItem,
          formItem: formItem,
          clearable: true,
          value: formData[prop],
          formJsonItem:item,
          popupTitle,
          placeholder,
          validateTrigger:formItem?.validateTrigger || this.validateTrigger
        },
        attrs: {
          ...formItem,
        },
        ref: prop,
        on: {
          input: (...arg) =>{
            if(_.isFunction(formItem?.onInput)) formItem?.onInput(...arg,this.JsonForm,vNode)
            if (isCustomComponent) return customComponentOnInput(arg[0], arg[1])
            formData[prop] = arg[0]
            this.$emit("valueTextChange",prop,arg[0])
          },
          change: value =>{
            if(_.isFunction(formItem?.onChange)) formItem?.onChange(value)
          },
          valueTextChange:(valueText)=>{
            this.$emit("valueTextChange",prop,valueText)
          }
        },
        class: [getCommentName(item), "dynamic-component"],
      });
      
      return vNode
    }

    // 字段
    const formField = (item:FormJsonItem) => {
      const fieldProp = item.field || {}
      const formItem = item.item
      
      const slot = item?.slot
      const footerSlot = slot && slot.footer && slot.footer(h)
      const headerSlot = slot && slot.header && slot.header(h)
      
      if(!getIfShow(formItem?.ifShow)) return ""
      const colspan = columns === 1 ? columns : fieldProp.colspan
      const fieldClass = ['json-form-field-'+formItem?.type,'json-form-prop-'+formItem?.prop]
      return (
        <o-field
          label={formItem?.label}
          name={formItem?.prop}
          key={formItem?.prop}
          attrs={fieldProp}
          class={fieldClass}
          colspan={colspan}
        >
          {headerSlot}
          {dynamicComponent(item)}
          {footerSlot}
        </o-field>
      )
    }

    return formSceneWrap(formJson.map(item => {
      if (item.groupName || (Array.isArray(item.children) && item.children.length)) {
        return formGroup(item as Required<FormJsonItem>)
      } 
      return formField(item)
    }))
  }
}

