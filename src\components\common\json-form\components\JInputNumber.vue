<template>
  <div>
    <div v-if="textModel">
      {{valueText}}
    </div>
    <el-input-number v-bind="$attrs"  @input="onInput" :value="value" ></el-input-number>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { FieldContext, registerField } from "../../form";
import { FormItem } from "../type";
import formFieldMixins from "../../formFieldMixins";

@Component({
  inheritAttrs: false,
  mixins: [formFieldMixins],
})
export default class JRadio extends Vue {
  @Prop() readonly value!: string | number;
  @Prop() readonly formItem!: FormItem;

  field!: FieldContext;

  beforeMount() {
    this.field = registerField(this);
  }

  get valueText (){
    try {
      return (this.formItem.options as any[]).find(item=>item.value === this.value).label
    }catch{
      return ""
    }
  }

  beforeDestroy() {
    this.field.clear();
  }

  onInput($event) {
    this.$emit("input", $event);
  }

  onFieldReset() {
    this.onInput([this.formItem.defaultValue]);
  }
}
</script>