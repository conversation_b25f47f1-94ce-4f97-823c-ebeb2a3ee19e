import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';

import Input from './Input.vue';

<Meta title="基础组件/o-input" component={Input} argTypes={{
  type: {
    control: { type: 'select' },
    options: ['', 'text', 'password', 'textarea'],
  },
  placeholder: {
    type: 'string'
  },
  maxlength: {
    type: 'number'
  },
  minlength: {
    type: 'number'
  },
  disabled: {
    type: 'boolean'
  },
  readonly: {
    type: 'boolean'
  },
  "textAlign": {
    control: { type: 'select' },
    options: ['', 'left', 'right'],
  },
  clearable: {
    type: 'boolean'
  },
  rules: {
    control: 'object'
  },
  validateTrigger: {
    control: { type: 'select' },
    options: ['', 'realtime', 'blur', 'prevent'],
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { Input },
  data: () => ({ v: "" }),
  template: '<o-input v-bind="$props" v-model="v"></o-input>',
});


# 输入框

基本

<Canvas>
  <Story 
    name="basic"
    args={{
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

禁用状态

<Canvas>
  <Story 
    name="disabled"
    args={{
      disabled: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


只读状态

<Canvas>
  <Story 
    name="readonly"
    args={{
      readonly: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

右对齐

<Canvas>
  <Story 
    name="text-align"
    args={{
      textAlign: "right"
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


占位符

<Canvas>
  <Story 
    name="placeholder"
    args={{
      placeholder: "占位符"
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


可清空

<Canvas>
  <Story 
    name="clearable"
    args={{
      clearable: true
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

最大输入长度

<Canvas>
  <Story 
    name="maxlength"
    args={{
      maxlength: 3
    }}>
    {Template.bind({})}
   </Story>
</Canvas>


输入验证

<Canvas>
  <Story 
    name="整数"
    args={{
      rules: [{ type: "int", message: "请正确输入" }],
      validateTrigger: "realtime",
      placeholder: "输入整数"
    }}>
    {Template.bind({})}
   </Story>
   <Story 
    name="邮箱"
    args={{
      rules: [{ type: "email", message: "请正确输入" }],
      validateTrigger: "realtime",
      placeholder: "输入邮箱"
    }}>
    {Template.bind({})}
   </Story>
</Canvas>

