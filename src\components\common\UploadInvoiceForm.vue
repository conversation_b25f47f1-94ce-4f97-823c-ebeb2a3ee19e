<template>
  <popup
    v-model="show"
    title="手录发票"
    :ifShowTitle="ifShowTitle"
    class="invoice-upload-form"
    mobileHeight="100%"
  >
    <o-context class="o-app content" device="mobile">
      <o-form
        ref="form"
        v-if="show"
        :label-width="labelWidth"
        :labelPosition="labelPosition"
        :layout="layout"
      >
        <!-- <div class="placeholder">请输入增值税发票的信息</div> -->
        <o-field name="picture" label="查看原图" v-if="formData.image">
          <van-image
            class="picture"
            width="40"
            height="40"
            @click="handleLookPicClick"
            :src="formData.image"
          />
        </o-field>

        <o-field
          name="labelWidth"
          label="发票代码"
          :rules="[
            { type: 'number', message: '只能输入数字' },
          ]"
        >
          <o-input
            v-model.trim="formData.invoiceCode"
            maxlength="20"
            placeholder="请输入发票代码"
            validate-trigger="prevent"
          />
        </o-field>
        <o-field
          name="text"
          label="发票号码"
          :rules="[
            { required: true, message: '请输入发票号码' },
            { type: 'number', message: '只能输入数字' },
            illegalCharacterRule,
          ]"
        >
          <o-input
            v-model.trim="formData.invoiceNumber"
            maxlength="20"
            placeholder="请输入发票号码"
            validate-trigger="prevent"
          />
        </o-field>
        <o-field
          label="日期"
          :rules="[{ required: true, message: '请选择日期时间' }]"
        >
          <o-select-date-time-field
            v-if="show"
            v-model.trim="formData.invoiceDate"
            placeholder="请选择发票日期"
          />
        </o-field>

        <o-field
          name="labelWidth"
          label="校验后六位"
          :rules="[{ type: 'number', message: '只能输入数字' }]"
        >
          <o-input
            v-model.trim="formData.checkCode"
            maxlength="6"
            placeholder="增值税普通发票请输入校验码后6位"
            validate-trigger="prevent"
          />
        </o-field>

        <o-field
          name="labelWidth"
          label="金额"
          :rules="[
            { type: 'number', message: '只能输入数字' },
            { validator: isAmount },
          ]"
        >
          <o-input
            v-model.trim="formData.amount"
            maxlength="20"
            @blur="onAmountBlur"
            placeholder="请输入金额"
            validate-trigger="realtime"
          />
        </o-field>
        <div class="hint">
          <h2>录入须知</h2>
          <p>1. 手录发票仅支持增值税发票</p>
          <p>2. 发票代码：仅全电发票可选填，其他增值税发票均必填</p>
          <p>3. 金额：增值税普通发票非必填</p>
          <p style="margin-left:12px;">增值税专用发票&机动车销售统一发票必填，填写不含税金额</p>
          <p style="margin-left:12px;">增值税专用发票必填，填写不含税金额</p>
          <p style="margin-left:12px;">二手车销售统一发票必填，填写车价合计</p>
          <p>4. 验证后六位：增值税普通发票必填，其他选填</p>
        </div>
      </o-form>
    </o-context>
    <div class="footer o-safe-area-bottom">
      <o-button class="button-cancel" @click="closePopup">取消</o-button>
      <o-button
        class="button-confirm"
        type="primary"
        @click="submit"
        :loading="loading"
        >确认</o-button
      >
    </div>
  </popup>
</template>
<script lang="ts">
import { Component, Ref, Vue, Watch } from "vue-property-decorator";
import isMobile from "is-mobile";

import {
  showError,
  writeInvoice,
  WriteInvoiceParameter,
} from "../../util";
import Popup from "./Popup.vue";
import dayjs from "dayjs";

function trimObject(obj: Record<string, any>) {
  const params = { ...obj };
  Object.keys(params).forEach((key) => {
    if (typeof params[key] === "string") {
      params[key] = params[key].replace(/\s+/g, "");
    }
  });
  return params;
}

const writeInvoiceApi = (params) => {
  const newParams = trimObject(params);
  newParams.invoiceDate = dayjs(newParams.invoiceDate).format("YYYY-MM-DD");
  return writeInvoice(newParams as WriteInvoiceParameter);
};

function formatNumber(value) {
  const numValue = Number(value);

  if (isNaN(numValue)) {
    console.error('Invalid input. Please provide a valid number or string representation of a number.');
    return null;
  }

  const formattedValue = numValue.toFixed(2);

  // 如果格式化后的字符串中包含小数点，说明是浮点数，直接返回
  if (formattedValue.includes('.')) {
    return formattedValue;
  }

  // 否则是整数，添加 .00 后返回
  return formattedValue + '.00';
}

@Component({
  components: {
    Popup,
  },
})
export default class  UploadInvoiceForm extends Vue {
  layout = "normal";
  labelWidth = "90px";
  text = "";
  labelPosition = "left";
  loading = false;
  show = false;

  illegalCharacterRule: any = {
    type: "illegalCharacter",
    message: "不能包含非法字符",
  };

  formData: WriteInvoiceParameter = {
    invoiceCode: "",
    invoiceDate: null,
    invoiceNumber: "",
    amount: "",
    checkCode: "",
    image: "",
    // invoiceCode: "041001900204",
    // invoiceDate: null,
    // invoiceNumber: "14033341",
    // amount: "4513.27",
    // checkCode: "438829",
  };

  get ifShowTitle(){
    return !isMobile()
  }

  @Ref("form") form: any;

  setFromData(formData) {
    Object.assign(this.formData, formData);
  }

  @Watch("show")
  onShow(newValue) {
    if (newValue) this.resetFormData();
  }

  async submit() {
    const ok = await this.form.validate();
    if (!ok) return;
    if (!this.formData.checkCode && !this.formData.amount) {
      return showError("校验码后六位与不含税金额二者其一是必填");
    }

    try {
      this.loading = true;
      const data = await writeInvoiceApi(this.formData);
      if (data.result === "FAIL") {
        return showError(data.desc ?? "发票信息校验失败");
      }
      data.invoice.checked = true;
      data.invoice.parentIndex = this.formData.parentIndex;
      data.invoice.index = this.formData.index;

      this.$emit("confirm", data.invoice);
      this.closePopup();
    } finally {
      this.loading = false;
    }
  }

  resetFormData() {
    Object.keys(this.formData).forEach((key) => {
      this.formData[key] = typeof this.formData[key] === "string" ? "" : null;
    });
  }

  closePopup() {
    this.show = false;
  }

  onAmountBlur(){
    (this.formData as any).amount = formatNumber(this.formData.amount)
  }

  isAmount(value) {
    if (!value) return null;
    const reg =
      /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/;
    return reg.test(value) ? null : "金额输入有误";
  }

  handleLookPicClick() {
    window.open(this.formData.image);
  }
}
</script>
<style scoped lang="less">
.content {
  height: calc(100% - 50px);
}
.footer {
  display: flex;
  height: 50px;
  padding: 5px 10px 5px 10px;
  box-shadow: 0 -1px 4px 0 rgba(224, 224, 224, 0.6);

  .button-cancel {
    flex: 1;
    margin: 0 5px;
  }
  .button-add {
    // flex:1;
    border: 1px solid #4f71ff;
    margin: 0 5px;
    color: #4f71ff;
  }
  .button-confirm {
    flex: 1;
    margin: 0 5px;
    border-color: #4f71ff;
    background: #4f71ff;
  }
}
.flex-1 {
  flex: 1;
}
.picture {
  border-radius: 5px;
  overflow: hidden;
  float: right;
}
.placeholder {
  background: #fff;
  line-height: 56px;
  margin-bottom: 10px;
  padding-left: 20px;
  color: #999;
  font-size: 14px;
}
.hint{
  background: #F4F4F4;
  font-size: 12px;
  padding: 14px 20px 18px 20px;
  color: #333;
  font-weight: bold;
  h2{
    font-size: 14px;
  }
  p{
    margin: 0;
    line-height: 16px;
  }
}
</style>