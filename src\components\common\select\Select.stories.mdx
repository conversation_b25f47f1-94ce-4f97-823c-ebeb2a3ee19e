import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Source } from '@storybook/addon-docs';
import dedent from 'ts-dedent';
import <PERSON>Field from './SelectField.vue';

<Meta title="基础组件/o-select-field" component={SelectField} argTypes={{
  options: {
    type: 'array',
  },
  readonly: {
    type: 'boolean'
  },
  multiple: {
    type: 'boolean'
  },
  labelProp: {
    type: 'string'
  },
  valueProp: {
    type: 'string'
  }
}} />

export const Template = (args, { argTypes }) => ({
  props: Object.keys(argTypes),
  components: { SelectField },
  data: () => ({
    v: 1,
    optionsData: [{
      text: '北京',
      value: 1
    }, {
      text: '上海',
      value: 2
    }]
  }),
  template: '<o-select-field v-bind="$props" v-model="v" ></o-select-field>',
});


# 选择输入

基本,只读，多选

<Canvas>
  <Story 
    name="basic"
    args={{
      options: [{
        text: '北京',
        value: 1
      }, {
        text: '上海',
        value: 2
      }],
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="readonly"
    args={{
      options: [{
        text: '北京',
        value: 1
      }, {
        text: '上海',
        value: 2
      }],
      readonly: true
    }}>
    {Template.bind({})}
  </Story>
  <Story 
    name="multiple"
    args={{
      options: [{
        text: '北京',
        value: 1
      }, {
        text: '上海',
        value: 2
      }],
      multiple: true
    }}>
    {Template.bind({})}
  </Story>
</Canvas>



可以设置用哪个属性作为text和value

<Canvas>
  <Story 
    name="labelProp"
    args={{
      options: [{
        name: '北京',
        id: 1
      }, {
        name: '上海',
        id: 2
      }],
      labelProp: 'name',
      valueProp: 'id'
    }}>
    {Template.bind({})}
  </Story>
</Canvas>


options可以传入一个函数,从而实现远程异步加载的

```jsx
loader = () => {
  return request("/api/list")
}

<template>
  <o-select-field v-model="v" :options="loader" />
</template>
```

<Story 
  name="远程加载"
  args={{
    options: () => new Promise((resolve) => {
      setTimeout(() => {
        resolve([{
          text: '北京',
          value: 1
        }, {
        text: '上海',
        value: 2
      }])
      }, 3000)
    }),
  }}>
  {Template.bind({})}
</Story>
