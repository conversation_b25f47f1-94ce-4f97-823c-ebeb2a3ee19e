<template>
  <pop
    :value="value"
    :style="popStyle"
    :overlay="overlay"
    :duration="duration"
    :position="position"
    class="o-popup"
    :class="popupClass"
    @input="$emit('input', $event)"
  >
    <div class="o-popup-header" v-if="ifShowTitle">
      <div>{{ title }}</div>
      <div class="close" @click="close">
        <i class="olading-iconfont oi-shanchu"></i>
      </div>
    </div>
    <div class="o-popup-container">
      <slot></slot>
    </div>
    <div v-if="$slots['footer']" class="o-popup-footer">
      <slot name="footer"></slot>
    </div>
  </pop>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue } from "vue-property-decorator";
import { CONTEXT_PROVIDER, defaultContext } from "./context";
import Pop from "./Pop.vue";

@Component({
  components: {
    Pop,
  },
})
export default class Popup extends Vue {
  @Prop() title!: string;
  @Prop() value!: boolean;
  @Prop() showMobileHeader!: boolean;
  @Prop({ default: true }) ifShowTitle!: boolean;
  @Prop({ default: true }) overlay!: boolean;
  @Prop({ default: 0.3 }) duration!: number;
  @Prop({ default: "70%" }) height!: string;
  /**移动模式下，弹出框的高度 */
  @Prop({ default: "100%" }) mobileHeight!: string;

  @Inject({
    from: CONTEXT_PROVIDER,
    default: defaultContext,
  })
  context: any;

  show = false;

  close() {
    this.$emit("input", false);
  }

  get popupClass() {
    let v = {} as any;
    v["o-popup-" + (this.mobile ? "mobile" : "pc")] = true;
    return v;
  }

  get popStyle() {
    let height = this.mobile ? this.mobileHeight : this.height;
    // PC下限定对话框的最大高度
    let maxHeight = this.mobile ? "" : `${window.screen.availHeight - 200}px`;
    return {
      width: this.mobile ? "100%" : "700px",
      height,
      maxHeight,
    };
  }

  protected get position() {
    return this.mobile ? "bottom" : "center";
  }

  get mobile() {
    return this.context.isMobile;
  }
}
</script>
<style lang="less">
.o-popup {
  display: flex;
  flex-direction: column;

  &-pc {
    border-radius: 6px;

    .o-popup-container {
      padding: 0px;
    }
  }
  &-mobile {
    // border-top-right-radius: 20px;
    // border-top-left-radius: 20px;

    .o-popup-container {
      padding: 0px;
    }
  }

  &-header {
    height: 60px;
    line-height: 60px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #070f29;
    text-align: center;
    border-bottom: 0.5px solid #eaeaea;
    position: relative;
    .close {
      width: 54px;
      height: 60px;
      // background: pink;
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      color: #bcbcbc;
    }
  }

  &-container {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
  }
}
</style>