<template>
  <o-pop
    class="o-drawer o-elevation-8"
    :position="actualPosition"
    :title="title"
    :style="actualStyle"
    :value="value"
    @input="onInput"
    @open="$emit('open', $event)"
    @close="$emit('close', $event)"
  >
    <header class="header">
      <span style="flex: 1">{{ title }}</span>
      <i
        class="el-icon-close"
        style="cursor: pointer"
        @click="$emit('input', false)"
      ></i>
    </header>
    <div style="flex: 1; padding: 20px">
      <slot />
    </div>
  </o-pop>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class  Drawer extends Vue {
  @Prop() value!: any;
  @Prop() position!: string;
  @Prop() size!: any;
  @Prop() title!: string;
  @Prop({ default: true }) maskClosable!: any;

  protected get actualPosition() {
    return this.position ? this.position : "right";
  }

  protected get actualStyle() {
    const position = this.actualPosition;
    const size = this.size ?? 300;
    const v = {} as any;
    if (position == "right" || position == "left") {
      v.width = `${size}px`;
    } else if (position == "top" || position == "bottom") {
      v.height = `${size}px`;
    }
    return v;
  }

  protected onInput(v) {
    if (this.maskClosable || this.maskClosable === "") {
      this.$emit("input", v);
    }
  }
}
</script>
<style lang="less">
.o-drawer {
  display: flex;
  flex-direction: column;

  .header {
    align-items: center;
    font-size: 24px;
    line-height: 36px;
    padding: 20px;
    display: flex;
    border-bottom: 1px solid rgb(234, 234, 234);
  }
}
</style>