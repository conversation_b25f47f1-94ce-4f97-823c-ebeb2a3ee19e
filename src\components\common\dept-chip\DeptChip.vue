<template>
  <o-chip
    :closable="closable"
    @close="$emit('close')"
    style="background-color: #f1f1f1"
  >
    <div style="display: flex; align-items: center">
      <o-avatar :size="25" :name="simpleName" style="margin: 0 3px 0 -7px" />
      <o-dept class="dept" :value="value" :change.sync="dept" />
    </div>
  </o-chip>
</template>

<script lang='ts'>
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component
export default class  DeptChip extends Vue {
  @Prop() value!: string;
  @Prop() closable!: any;

  get simpleName() {
    let v = (this.dept?.name ?? "").substring(0, 1);
    return v;
  }

  dept: any = null;

  @Watch("dept") 
  onWatchDept (value){
    this.$emit("change",value)
  }
}
</script>
<style lang='less' scoped>
.dept {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  font-size: 14px;
}
</style>